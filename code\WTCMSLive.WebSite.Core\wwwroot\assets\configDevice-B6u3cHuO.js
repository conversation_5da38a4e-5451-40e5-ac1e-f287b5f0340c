import{C as c,a_ as a,a$ as n,b0 as i,b1 as h,b2 as l,b3 as p,b4 as y,b5 as u,b6 as d,b7 as L,b8 as w,b9 as M,ba as b,bb as f,bc as m,bd as D,be as g,bf as k,bg as C,bh as S,bi as G,bj as v,bk as P,bl as O,bm as W,bn as V,bo as A,bp as E}from"./index-BjOW8S1L.js";import{e as s,a as t}from"./tools-zTE6InS0.js";const I=c("configDevice",{state:()=>({deviceInfo:{},vibMeaslocationList:[],processMeaslocationList:[],componentList:[],sectionList:[],orientatioList:[],workCondMeasLocDicOptions:[],workCondMeasLocsList:[],enumWorkConDataSourceOptions:[],rotSpdMeasLocList:[],modbusMeasLocList:[],sVMParamTypeList:[],modbusMeasLocoptions:[],OilParamType:[]}),actions:{reset(){this.$reset()},async fetchDeviceInfo(r={}){try{const e=await E(r);return this.deviceInfo=e,e}catch(e){throw console.error("获取设备失败:",e),e}},async fetcheditOneDevice(r={}){try{return await A(r)}catch(e){throw console.error("编辑失败:",e),e}},async fetchGetVibMeaslocation(r={}){try{const e=await V(r);return e&&e.length>0?(this.getVibMeaslocation=e,e):[]}catch(e){throw console.error("获取失败:",e),e}},async fetchGetProcessMeaslocation(r={}){try{const e=await W(r);return e&&e.length>0?(this.processMeaslocationList=e,e):[]}catch(e){throw console.error("获取失败:",e),e}},async fetchGetOrientationList(r={}){try{const e=await O(r);let o=s(e,"key");return this.orientatioList=o,o}catch(e){throw console.error("获取失败:",e),e}},async fetchGetComponentList(r={}){try{const e=await P(r);if(e&&e.length>0){let o=t(e,{label:"componentName",value:"componentID"},{nother:!0});return this.componentList=o,o}return[]}catch(e){throw console.error("获取失败:",e),e}},async fetchGetSectionList(r={}){try{const e=await v(r);let o=s(e,"key");return this.sectionList=o,o}catch(e){throw console.error("获取失败:",e),e}},async fetchGetWorkCondMeasLocDic(r={}){try{const e=await G(r);let o=t(e);return this.workCondMeasLocDicOptions=o,o}catch(e){throw console.error("获取失败:",e),e}},async fetchGetEnumWorkConDataSource(r={}){try{const e=await S(r);let o=t(e,{label:"value",value:"key",text:"value"},{nother:!0});return this.enumWorkConDataSourceOptions=o,o}catch(e){throw console.error("获取失败:",e),e}},async fetchAddVibMeasLocs(r={}){try{return await C(r)}catch(e){throw console.error("批量添加失败:",e),e}},async fetchEditVibMeasLoc(r={}){try{return await k(r)}catch(e){throw console.error("编辑失败:",e),e}},async fetchDeleteVibMeasLocs(r={}){try{return await g(r)}catch(e){throw console.error("删除失败:",e),e}},async fetchAddProcessMeasLocs(r={}){try{return await D(r)}catch(e){throw console.error("批量添加失败:",e),e}},async fetchEditProcessMeasLoc(r={}){try{return await m(r)}catch(e){throw console.error("编辑失败:",e),e}},async fetchDeleteProcessMeasLocs(r={}){try{return await f(r)}catch(e){throw console.error("删除失败:",e),e}},async fetchGetWorkCondMeasLocs(r={}){try{const e=await b(r);return e&&e.length>0?(this.workCondMeasLocsList=e,e):[]}catch(e){throw console.error("获取失败:",e),e}},async fetchAddWorkingConditionMeaslocs(r={}){try{return await M(r)}catch(e){throw console.error("批量添加失败:",e),e}},async fetchEditWorkingConditionMeas(r={}){try{return await w(r)}catch(e){throw console.error("编辑失败:",e),e}},async fetchDeleteWorkingConditionMeasBatch(r={}){try{return await L(r)}catch(e){throw console.error("删除失败:",e),e}},async fetchGetRotSpdMeasLocList(r={}){try{const e=await d(r);return this.rotSpdMeasLocList=e,e}catch(e){throw console.error("删除失败:",e),e}},async fetchAddRotSpdMeaslocs(r={}){try{return await u(r)}catch(e){throw console.error("批量添加失败:",e),e}},async fetchEditRotSpdLoc(r={}){try{return await y(r)}catch(e){throw console.error("编辑失败:",e),e}},async fetchDeleteRotSpdLoc(r={}){try{return await p(r)}catch(e){throw console.error("删除失败:",e),e}},async fetchGetModbusMeasLocList(r){try{const e=await l(r);this.modbusMeasLocList=e;let o=t(e,{label:"measLocName",value:"measLocationID"},{nother:!0});return this.modbusMeasLocoptions=o,e}catch(e){throw console.error("获取失败:",e),e}},async fetchAddModbusMeasloc(r){try{return await h(r)}catch(e){throw console.error("操作失败:",e),e}},async fetchBatchDeleteMeasLoc(r){try{return await i(r)}catch(e){throw console.error("操作失败:",e),e}},async fetchGetSVMParamType(r){try{const e=await n(r);let o=t(e,{label:"value",value:"value"},{nother:!0});return this.sVMParamType=o,o}catch(e){throw console.error("操作失败:",e),e}},async fetchGetOilParamType(r){try{const e=await a(r);let o=s(e,!1);return this.OilParamType=o,o}catch(e){throw console.error("操作失败:",e),e}}}});export{I as u};
