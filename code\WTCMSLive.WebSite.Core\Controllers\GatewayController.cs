using CMSFramework.BusinessEntity;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using WTCMSLive.BusinessModel;

namespace WTCMSLive.WebSite.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class GatewayController : ControllerBase
    {
        /// <summary>
        /// 获取
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetDevice")]
        public IActionResult GetDevice()
        {
            List<WindPark> windParkList = DevTreeManagement.GetWindParkList();
            return Ok(windParkList.FirstOrDefault());
        }
    }
}
