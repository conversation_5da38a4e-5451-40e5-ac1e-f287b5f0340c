import{c as oe,W as N}from"./table-RP3jLHlo.js";import{O as B}from"./index-CzSbT6op.js";import{L as ie}from"./index-BXWgJpPi.js";import{z as o,r as a,u as re,y as se,w as ue,f as b,d as m,o as r,i as s,b as D,c as g,F as O,e as H,q as ce,t as W,g as me,A as _}from"./index-BjOW8S1L.js";import{u as de}from"./collectionUnitConfig-BbRKo_Zp.js";import{u as De}from"./devTree-Dwa9wLl9.js";import{u as pe}from"./collectionUnitMonitor-CiAcUbgo.js";import{S as he,b as S}from"./tools-zTE6InS0.js";import{_ as fe}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{D as ve,a as be}from"./index-D82yULGq.js";import{M as ge}from"./index-BnSFuLp6.js";import"./ActionButton-C_grUmdF.js";import"./styleChecker-CFtINSLw.js";import"./index-7iPMz_Qy.js";import"./initDefaultProps-P4j1rGDC.js";import"./shallowequal-gCpTBdTi.js";import"./index-DTxROkTj.js";import"./index-kP-mINcM.js";import"./index-BJEkaghg.js";import"./index-D3J_dKbJ.js";import"./index-CpBSPak5.js";const _e={class:"btnGroups"},Ie={class:"clearfix"},Ye=["onClick"],Me={class:"border"},Ce=["onClick"],ye={class:"formHeader"},xe={class:"chartBox"},ke={key:1,class:"nodata"},Se={__name:"status",setup(Te){const p=de(),z=De(),I=pe();let u=[o().subtract(3,"month"),o()];const T=a(""),Y=a(!1),M=a(!1),V=re(),n=a({}),A=a(),d=a(V.params.id),L=a([]),G=a({}),v=a(u),C=a(u),U=a({timeRange:u}),h=a({time:u}),c=a({}),i=se({dauList:[],tableData1:[],tableData2:[],chartInformation:{title:"",legendArr:[],unit:["时间","电压(V)"]}}),P=()=>{let e=z.findAncestorsWithNodes(d.value);e&&e.length&&e.length>1&&(A.value=e[e.length-2].id)},y=e=>{G.value=e,L.value=[{label:"采集单元名称",value:e.dauName},{label:"IP地址",value:e.ip},{label:"采集间隔(分钟)",value:e.dataAcquisitionInterval},{label:"状态",value:e.isAvailable?"开启":"禁用"},{label:"端口",value:e.port}]},$=async e=>{Y.value=!0,i.dauList=await p.fetchGetDAUList({WindTurbineID:d.value,WindParkId:A.value}),Y.value=!1,p.dAUList&&p.dAUList.length?(n.value=p.dAUList[0],y(n.value),w(),x()):(n.value={},y({}))},w=async()=>{const e=await I.fetchGetDauStatusInfoByTurbineIDAndDauID({turbineID:d.value,DAUID:n.value.dauID});i.tableData1=e.channelStatusList||[]},x=async()=>{const e=await I.fetchGetDSearchDAULog({turbineID:d.value,dauID:n.value.dauID,beginTime:C.value[0],endTime:C.value[1]});i.tableData2=e||[]};ue(()=>V.params.id,e=>{e&&(p.reset(),d.value=e,P(),$())},{immediate:!0});const q=[{title:"选择时间",dataIndex:"timeRange",inputType:"rangePicker",timeFormat:["YYYY-MM-DD","YYYY-MM-DD"],noMinLabelWidth:!0}],E=[{title:"通道",dataIndex:"channelNumber"},{title:"测量位置",dataIndex:"measLocationName"},{title:"偏置电压(V)",dataIndex:"dcDataValue"},{title:"状态",dataIndex:"alarmState",customRender:({record:e})=>e.alarmState?_("span",{style:{color:S(e.alarmState).color}},S(e.alarmState).text):""},{title:"更新时间",dataIndex:"statusUpdateTime",headerOperations:{sorter:!0,date:!0},customRender:({record:e})=>e.statusUpdateTime?_("span",{},o(e.statusUpdateTime).format("YYYY-MM-DD HH:mm:ss")):""},{title:"操作",dataIndex:"otherColumn",columnHidden:!0,width:160}],j=[{title:"采集单元状态",dataIndex:"alarmStateDescription",headerOperations:{filters:[]},width:160,customRender:({text:e,record:t})=>t.alarmState?_("span",{style:{color:S(t.alarmState).color}},e):""},{title:"日志",dataIndex:"logTitle",customRender:({text:e,record:t})=>t.logTitle?_("p",{style:{textAlign:"left"}},e):""},{title:"更新时间",dataIndex:"eventTimeFormatted",headerOperations:{sorter:!0,date:!0},width:160}],J=e=>{!e.timeRange||!e.timeRange.length||(C.value=[o(e.timeRange[0]).format("YYYY-MM-DD"),o(e.timeRange[1]).format("YYYY-MM-DD")],x())},K=e=>{e.dauID!=n.value.dauID&&(n.value=e,y(e),U.value={timeRange:u},w(),x())},Q=e=>{T.value=`${e.measLocationName}`,X(),h.value={...h.value,...e},F()},F=async()=>{const e=h.value;let t=await I.fetchGetChannelDCTrendChart({turbineID:d.value,beginTime:o(v.value[0]).format("YYYY-MM-DD"),endTime:o(v.value[1]).format("YYYY-MM-DD"),channelID:e.channelNumber,DAUID:n.value.dauID});if(t&&t.timeValueData&&t.timeValueData.length){let f=[];t.waringValueData&&t.waringValueData.length&&(f.push(t.waringValueData[0]),t.errorValueData&&t.errorValueData.length&&f.push(t.errorValueData[0]));const k={time:t.timeValueData,max:Math.max(...t.eigenValueData,...t.errorValueData,...t.waringValueData),min:Math.min(...t.eigenValueData,...t.errorValueData,...t.waringValueData),lineData:[{line:t.eigenValueData}],markLine:f};c.value=k,i.chartInformation={title:`${t.titleName}`,legendArr:[t.eigenValue||"偏置电压"],unit:["时间","电压(V)"]}}else c.value={}},X=()=>{M.value=!0},Z=e=>{M.value=!1,h.value={time:u},v.value=u,c.value={}},ee=e=>{v.value=[o(e.time[0]).format("YYYY-MM-DD"),o(e.time[1]).format("YYYY-MM-DD")],F()},te=[{title:"选择时间",dataIndex:"time",inputType:"rangePicker",timeFormat:["YYYY-MM-DD","YYYY-MM-DD"],noMinLabelWidth:!0}],ae={showToolbox:!0};return(e,t)=>{const f=be,k=ve,ne=ge,le=he;return r(),b(le,{spinning:Y.value,size:"large"},{default:m(()=>[s("div",null,[s("div",_e,[s("ul",Ie,[(r(!0),g(O,null,H(i.dauList,l=>(r(),g("li",{key:l.dauID,class:ce({active:n.value.dauID===l.dauID}),onClick:R=>K(l)},W(l.dauName),11,Ye))),128))])]),D(oe,{tableTitle:"采集单元信息",defaultCollapse:!0,batchApply:!1},{content:m(()=>[s("div",Me,[D(k,{column:5,size:"small"},{default:m(()=>[(r(!0),g(O,null,H(L.value,l=>(r(),b(f,{label:l.label,key:l.label},{default:m(()=>[me(W(l.value),1)]),_:2},1032,["label"]))),128))]),_:1})])]),_:1}),s("div",null,[D(N,{ref:"table",size:"default","table-key":"0","table-title":"传感器状态列表","table-columns":E,noPagination:!0,"record-key":"measLocationID","table-datas":i.tableData1,noBatchApply:!0},{otherColumn:m(({column:l,record:R,text:Ve})=>[s("span",{onClick:Ae=>Q(R),class:"editBtn"},"查看偏置电压趋势图",8,Ce)]),_:1},8,["table-datas"]),D(N,{ref:"table",size:"default","table-key":"1","table-title":"采集单元监测日志","table-columns":j,"record-key":"ModbusUnitID","table-datas":i.tableData2,noBatchApply:!0},{contentHeader:m(()=>[s("div",ye,[(r(),b(B,{titleCol:q,initFormData:U.value,key:n.value.dauID,onSubmit:J,formlayout:"inline"},null,8,["initFormData"]))])]),_:1},8,["table-datas"])]),D(ne,{maskClosable:!1,destroyOnClose:!0,width:"800px",open:M.value,title:T.value,footer:"",onCancel:Z},{default:m(()=>[D(B,{titleCol:te,ref:"formRef",initFormData:h.value,formlayout:"inline",onSubmit:ee},null,8,["initFormData"]),s("div",xe,[c.value&&c.value.time&&c.value.time.length?(r(),b(ie,{key:0,boxId:"chart2",chartOptions:ae,informations:i.chartInformation,chartData:c.value},null,8,["informations","chartData"])):(r(),g("div",ke,"暂无数据"))])]),_:1},8,["open","title"])])]),_:1},8,["spinning"])}}},Ze=fe(Se,[["__scopeId","data-v-3d35e148"]]);export{Ze as default};
