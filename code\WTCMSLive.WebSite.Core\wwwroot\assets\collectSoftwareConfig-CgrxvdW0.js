import{ad as Pl,r as ie,w as Ke,h as Wo,ae as _o,c as cs,o as Ot,s as Kh,i as xe,a4 as sd,af as Ys,ag as rd,j as Vr,ah as od,A as ld,b as Nt,ai as ad,x as Uh,C as hd,aj as cd,ak as fd,al as ud,am as dd,an as pd,f as Li,d as Dt,m as Pt,z as md,u as gd,g as Ut,t as Js}from"./index-BjOW8S1L.js";import{_ as $o}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{O as yd}from"./index-CzSbT6op.js";import{a as bd,S as xd}from"./tools-zTE6InS0.js";import{B as vd}from"./index-7iPMz_Qy.js";import{U as wd}from"./UploadOutlined-BLiqGWw7.js";import{_ as Sd}from"./index-9RFCYCf2.js";import{a as Od}from"./index-DTxROkTj.js";import"./index-D3J_dKbJ.js";import"./index-CpBSPak5.js";import"./styleChecker-CFtINSLw.js";import"./initDefaultProps-P4j1rGDC.js";import"./shallowequal-gCpTBdTi.js";import"./index-BJEkaghg.js";import"./useRefs-CX1uwt0r.js";let Wr=[],Gh=[];(()=>{let n="lc,34,7n,7,7b,19,,,,2,,2,,,20,b,1c,l,g,,2t,7,2,6,2,2,,4,z,,u,r,2j,b,1m,9,9,,o,4,,9,,3,,5,17,3,3b,f,,w,1j,,,,4,8,4,,3,7,a,2,t,,1m,,,,2,4,8,,9,,a,2,q,,2,2,1l,,4,2,4,2,2,3,3,,u,2,3,,b,2,1l,,4,5,,2,4,,k,2,m,6,,,1m,,,2,,4,8,,7,3,a,2,u,,1n,,,,c,,9,,14,,3,,1l,3,5,3,,4,7,2,b,2,t,,1m,,2,,2,,3,,5,2,7,2,b,2,s,2,1l,2,,,2,4,8,,9,,a,2,t,,20,,4,,2,3,,,8,,29,,2,7,c,8,2q,,2,9,b,6,22,2,r,,,,,,1j,e,,5,,2,5,b,,10,9,,2u,4,,6,,2,2,2,p,2,4,3,g,4,d,,2,2,6,,f,,jj,3,qa,3,t,3,t,2,u,2,1s,2,,7,8,,2,b,9,,19,3,3b,2,y,,3a,3,4,2,9,,6,3,63,2,2,,1m,,,7,,,,,2,8,6,a,2,,1c,h,1r,4,1c,7,,,5,,14,9,c,2,w,4,2,2,,3,1k,,,2,3,,,3,1m,8,2,2,48,3,,d,,7,4,,6,,3,2,5i,1m,,5,ek,,5f,x,2da,3,3x,,2o,w,fe,6,2x,2,n9w,4,,a,w,2,28,2,7k,,3,,4,,p,2,5,,47,2,q,i,d,,12,8,p,b,1a,3,1c,,2,4,2,2,13,,1v,6,2,2,2,2,c,,8,,1b,,1f,,,3,2,2,5,2,,,16,2,8,,6m,,2,,4,,fn4,,kh,g,g,g,a6,2,gt,,6a,,45,5,1ae,3,,2,5,4,14,3,4,,4l,2,fx,4,ar,2,49,b,4w,,1i,f,1k,3,1d,4,2,2,1x,3,10,5,,8,1q,,c,2,1g,9,a,4,2,,2n,3,2,,,2,6,,4g,,3,8,l,2,1l,2,,,,,m,,e,7,3,5,5f,8,2,3,,,n,,29,,2,6,,,2,,,2,,2,6j,,2,4,6,2,,2,r,2,2d,8,2,,,2,2y,,,,2,6,,,2t,3,2,4,,5,77,9,,2,6t,,a,2,,,4,,40,4,2,2,4,,w,a,14,6,2,4,8,,9,6,2,3,1a,d,,2,ba,7,,6,,,2a,m,2,7,,2,,2,3e,6,3,,,2,,7,,,20,2,3,,,,9n,2,f0b,5,1n,7,t4,,1r,4,29,,f5k,2,43q,,,3,4,5,8,8,2,7,u,4,44,3,1iz,1j,4,1e,8,,e,,m,5,,f,11s,7,,h,2,7,,2,,5,79,7,c5,4,15s,7,31,7,240,5,gx7k,2o,3k,6o".split(",").map(e=>e?parseInt(e,36):1);for(let e=0,t=0;e<n.length;e++)(e%2?Gh:Wr).push(t=t+n[e])})();function kd(n){if(n<768)return!1;for(let e=0,t=Wr.length;;){let i=e+t>>1;if(n<Wr[i])t=i;else if(n>=Gh[i])e=i+1;else return!0;if(e==t)return!1}}function Nl(n){return n>=127462&&n<=127487}const Rl=8205;function Cd(n,e,t=!0,i=!0){return(t?Xh:Ad)(n,e,i)}function Xh(n,e,t){if(e==n.length)return e;e&&Qh(n.charCodeAt(e))&&Yh(n.charCodeAt(e-1))&&e--;let i=Zs(n,e);for(e+=Bl(i);e<n.length;){let s=Zs(n,e);if(i==Rl||s==Rl||t&&kd(s))e+=Bl(s),i=s;else if(Nl(s)){let r=0,o=e-2;for(;o>=0&&Nl(Zs(n,o));)r++,o-=2;if(r%2==0)break;e+=2}else break}return e}function Ad(n,e,t){for(;e>0;){let i=Xh(n,e-2,t);if(i<e)return i;e--}return 0}function Zs(n,e){let t=n.charCodeAt(e);if(!Yh(t)||e+1==n.length)return t;let i=n.charCodeAt(e+1);return Qh(i)?(t-55296<<10)+(i-56320)+65536:t}function Qh(n){return n>=56320&&n<57344}function Yh(n){return n>=55296&&n<56320}function Bl(n){return n<65536?1:2}class G{lineAt(e){if(e<0||e>this.length)throw new RangeError(`Invalid position ${e} in document of length ${this.length}`);return this.lineInner(e,!1,1,0)}line(e){if(e<1||e>this.lines)throw new RangeError(`Invalid line number ${e} in ${this.lines}-line document`);return this.lineInner(e,!0,1,0)}replace(e,t,i){[e,t]=ki(this,e,t);let s=[];return this.decompose(0,e,s,2),i.length&&i.decompose(0,i.length,s,3),this.decompose(t,this.length,s,1),ct.from(s,this.length-(t-e)+i.length)}append(e){return this.replace(this.length,this.length,e)}slice(e,t=this.length){[e,t]=ki(this,e,t);let i=[];return this.decompose(e,t,i,0),ct.from(i,t-e)}eq(e){if(e==this)return!0;if(e.length!=this.length||e.lines!=this.lines)return!1;let t=this.scanIdentical(e,1),i=this.length-this.scanIdentical(e,-1),s=new Ui(this),r=new Ui(e);for(let o=t,l=t;;){if(s.next(o),r.next(o),o=0,s.lineBreak!=r.lineBreak||s.done!=r.done||s.value!=r.value)return!1;if(l+=s.value.length,s.done||l>=i)return!0}}iter(e=1){return new Ui(this,e)}iterRange(e,t=this.length){return new Jh(this,e,t)}iterLines(e,t){let i;if(e==null)i=this.iter();else{t==null&&(t=this.lines+1);let s=this.line(e).from;i=this.iterRange(s,Math.max(s,t==this.lines+1?this.length:t<=1?0:this.line(t-1).to))}return new Zh(i)}toString(){return this.sliceString(0)}toJSON(){let e=[];return this.flatten(e),e}constructor(){}static of(e){if(e.length==0)throw new RangeError("A document must have at least one line");return e.length==1&&!e[0]?G.empty:e.length<=32?new re(e):ct.from(re.split(e,[]))}}class re extends G{constructor(e,t=Td(e)){super(),this.text=e,this.length=t}get lines(){return this.text.length}get children(){return null}lineInner(e,t,i,s){for(let r=0;;r++){let o=this.text[r],l=s+o.length;if((t?i:l)>=e)return new Md(s,l,i,o);s=l+1,i++}}decompose(e,t,i,s){let r=e<=0&&t>=this.length?this:new re(Il(this.text,e,t),Math.min(t,this.length)-Math.max(0,e));if(s&1){let o=i.pop(),l=Qn(r.text,o.text.slice(),0,r.length);if(l.length<=32)i.push(new re(l,o.length+r.length));else{let a=l.length>>1;i.push(new re(l.slice(0,a)),new re(l.slice(a)))}}else i.push(r)}replace(e,t,i){if(!(i instanceof re))return super.replace(e,t,i);[e,t]=ki(this,e,t);let s=Qn(this.text,Qn(i.text,Il(this.text,0,e)),t),r=this.length+i.length-(t-e);return s.length<=32?new re(s,r):ct.from(re.split(s,[]),r)}sliceString(e,t=this.length,i=`
`){[e,t]=ki(this,e,t);let s="";for(let r=0,o=0;r<=t&&o<this.text.length;o++){let l=this.text[o],a=r+l.length;r>e&&o&&(s+=i),e<a&&t>r&&(s+=l.slice(Math.max(0,e-r),t-r)),r=a+1}return s}flatten(e){for(let t of this.text)e.push(t)}scanIdentical(){return 0}static split(e,t){let i=[],s=-1;for(let r of e)i.push(r),s+=r.length+1,i.length==32&&(t.push(new re(i,s)),i=[],s=-1);return s>-1&&t.push(new re(i,s)),t}}class ct extends G{constructor(e,t){super(),this.children=e,this.length=t,this.lines=0;for(let i of e)this.lines+=i.lines}lineInner(e,t,i,s){for(let r=0;;r++){let o=this.children[r],l=s+o.length,a=i+o.lines-1;if((t?a:l)>=e)return o.lineInner(e,t,i,s);s=l+1,i=a+1}}decompose(e,t,i,s){for(let r=0,o=0;o<=t&&r<this.children.length;r++){let l=this.children[r],a=o+l.length;if(e<=a&&t>=o){let h=s&((o<=e?1:0)|(a>=t?2:0));o>=e&&a<=t&&!h?i.push(l):l.decompose(e-o,t-o,i,h)}o=a+1}}replace(e,t,i){if([e,t]=ki(this,e,t),i.lines<this.lines)for(let s=0,r=0;s<this.children.length;s++){let o=this.children[s],l=r+o.length;if(e>=r&&t<=l){let a=o.replace(e-r,t-r,i),h=this.lines-o.lines+a.lines;if(a.lines<h>>4&&a.lines>h>>6){let c=this.children.slice();return c[s]=a,new ct(c,this.length-(t-e)+i.length)}return super.replace(r,l,a)}r=l+1}return super.replace(e,t,i)}sliceString(e,t=this.length,i=`
`){[e,t]=ki(this,e,t);let s="";for(let r=0,o=0;r<this.children.length&&o<=t;r++){let l=this.children[r],a=o+l.length;o>e&&r&&(s+=i),e<a&&t>o&&(s+=l.sliceString(e-o,t-o,i)),o=a+1}return s}flatten(e){for(let t of this.children)t.flatten(e)}scanIdentical(e,t){if(!(e instanceof ct))return 0;let i=0,[s,r,o,l]=t>0?[0,0,this.children.length,e.children.length]:[this.children.length-1,e.children.length-1,-1,-1];for(;;s+=t,r+=t){if(s==o||r==l)return i;let a=this.children[s],h=e.children[r];if(a!=h)return i+a.scanIdentical(h,t);i+=a.length+1}}static from(e,t=e.reduce((i,s)=>i+s.length+1,-1)){let i=0;for(let d of e)i+=d.lines;if(i<32){let d=[];for(let m of e)m.flatten(d);return new re(d,t)}let s=Math.max(32,i>>5),r=s<<1,o=s>>1,l=[],a=0,h=-1,c=[];function f(d){let m;if(d.lines>r&&d instanceof ct)for(let x of d.children)f(x);else d.lines>o&&(a>o||!a)?(u(),l.push(d)):d instanceof re&&a&&(m=c[c.length-1])instanceof re&&d.lines+m.lines<=32?(a+=d.lines,h+=d.length+1,c[c.length-1]=new re(m.text.concat(d.text),m.length+1+d.length)):(a+d.lines>s&&u(),a+=d.lines,h+=d.length+1,c.push(d))}function u(){a!=0&&(l.push(c.length==1?c[0]:ct.from(c,h)),h=-1,a=c.length=0)}for(let d of e)f(d);return u(),l.length==1?l[0]:new ct(l,t)}}G.empty=new re([""],0);function Td(n){let e=-1;for(let t of n)e+=t.length+1;return e}function Qn(n,e,t=0,i=1e9){for(let s=0,r=0,o=!0;r<n.length&&s<=i;r++){let l=n[r],a=s+l.length;a>=t&&(a>i&&(l=l.slice(0,i-s)),s<t&&(l=l.slice(t-s)),o?(e[e.length-1]+=l,o=!1):e.push(l)),s=a+1}return e}function Il(n,e,t){return Qn(n,[""],e,t)}class Ui{constructor(e,t=1){this.dir=t,this.done=!1,this.lineBreak=!1,this.value="",this.nodes=[e],this.offsets=[t>0?1:(e instanceof re?e.text.length:e.children.length)<<1]}nextInner(e,t){for(this.done=this.lineBreak=!1;;){let i=this.nodes.length-1,s=this.nodes[i],r=this.offsets[i],o=r>>1,l=s instanceof re?s.text.length:s.children.length;if(o==(t>0?l:0)){if(i==0)return this.done=!0,this.value="",this;t>0&&this.offsets[i-1]++,this.nodes.pop(),this.offsets.pop()}else if((r&1)==(t>0?0:1)){if(this.offsets[i]+=t,e==0)return this.lineBreak=!0,this.value=`
`,this;e--}else if(s instanceof re){let a=s.text[o+(t<0?-1:0)];if(this.offsets[i]+=t,a.length>Math.max(0,e))return this.value=e==0?a:t>0?a.slice(e):a.slice(0,a.length-e),this;e-=a.length}else{let a=s.children[o+(t<0?-1:0)];e>a.length?(e-=a.length,this.offsets[i]+=t):(t<0&&this.offsets[i]--,this.nodes.push(a),this.offsets.push(t>0?1:(a instanceof re?a.text.length:a.children.length)<<1))}}}next(e=0){return e<0&&(this.nextInner(-e,-this.dir),e=this.value.length),this.nextInner(e,this.dir)}}class Jh{constructor(e,t,i){this.value="",this.done=!1,this.cursor=new Ui(e,t>i?-1:1),this.pos=t>i?e.length:0,this.from=Math.min(t,i),this.to=Math.max(t,i)}nextInner(e,t){if(t<0?this.pos<=this.from:this.pos>=this.to)return this.value="",this.done=!0,this;e+=Math.max(0,t<0?this.pos-this.to:this.from-this.pos);let i=t<0?this.pos-this.from:this.to-this.pos;e>i&&(e=i),i-=e;let{value:s}=this.cursor.next(e);return this.pos+=(s.length+e)*t,this.value=s.length<=i?s:t<0?s.slice(s.length-i):s.slice(0,i),this.done=!this.value,this}next(e=0){return e<0?e=Math.max(e,this.from-this.pos):e>0&&(e=Math.min(e,this.to-this.pos)),this.nextInner(e,this.cursor.dir)}get lineBreak(){return this.cursor.lineBreak&&this.value!=""}}class Zh{constructor(e){this.inner=e,this.afterBreak=!0,this.value="",this.done=!1}next(e=0){let{done:t,lineBreak:i,value:s}=this.inner.next(e);return t&&this.afterBreak?(this.value="",this.afterBreak=!1):t?(this.done=!0,this.value=""):i?this.afterBreak?this.value="":(this.afterBreak=!0,this.next()):(this.value=s,this.afterBreak=!1),this}get lineBreak(){return!1}}typeof Symbol<"u"&&(G.prototype[Symbol.iterator]=function(){return this.iter()},Ui.prototype[Symbol.iterator]=Jh.prototype[Symbol.iterator]=Zh.prototype[Symbol.iterator]=function(){return this});class Md{constructor(e,t,i,s){this.from=e,this.to=t,this.number=i,this.text=s}get length(){return this.to-this.from}}function ki(n,e,t){return e=Math.max(0,Math.min(n.length,e)),[e,Math.max(e,Math.min(n.length,t))]}function Se(n,e,t=!0,i=!0){return Cd(n,e,t,i)}function Ed(n){return n>=56320&&n<57344}function Dd(n){return n>=55296&&n<56320}function Ie(n,e){let t=n.charCodeAt(e);if(!Dd(t)||e+1==n.length)return t;let i=n.charCodeAt(e+1);return Ed(i)?(t-55296<<10)+(i-56320)+65536:t}function qo(n){return n<=65535?String.fromCharCode(n):(n-=65536,String.fromCharCode((n>>10)+55296,(n&1023)+56320))}function ft(n){return n<65536?1:2}const _r=/\r\n?|\n/;var Ee=function(n){return n[n.Simple=0]="Simple",n[n.TrackDel=1]="TrackDel",n[n.TrackBefore=2]="TrackBefore",n[n.TrackAfter=3]="TrackAfter",n}(Ee||(Ee={}));class gt{constructor(e){this.sections=e}get length(){let e=0;for(let t=0;t<this.sections.length;t+=2)e+=this.sections[t];return e}get newLength(){let e=0;for(let t=0;t<this.sections.length;t+=2){let i=this.sections[t+1];e+=i<0?this.sections[t]:i}return e}get empty(){return this.sections.length==0||this.sections.length==2&&this.sections[1]<0}iterGaps(e){for(let t=0,i=0,s=0;t<this.sections.length;){let r=this.sections[t++],o=this.sections[t++];o<0?(e(i,s,r),s+=r):s+=o,i+=r}}iterChangedRanges(e,t=!1){$r(this,e,t)}get invertedDesc(){let e=[];for(let t=0;t<this.sections.length;){let i=this.sections[t++],s=this.sections[t++];s<0?e.push(i,s):e.push(s,i)}return new gt(e)}composeDesc(e){return this.empty?e:e.empty?this:ec(this,e)}mapDesc(e,t=!1){return e.empty?this:qr(this,e,t)}mapPos(e,t=-1,i=Ee.Simple){let s=0,r=0;for(let o=0;o<this.sections.length;){let l=this.sections[o++],a=this.sections[o++],h=s+l;if(a<0){if(h>e)return r+(e-s);r+=l}else{if(i!=Ee.Simple&&h>=e&&(i==Ee.TrackDel&&s<e&&h>e||i==Ee.TrackBefore&&s<e||i==Ee.TrackAfter&&h>e))return null;if(h>e||h==e&&t<0&&!l)return e==s||t<0?r:r+a;r+=a}s=h}if(e>s)throw new RangeError(`Position ${e} is out of range for changeset of length ${s}`);return r}touchesRange(e,t=e){for(let i=0,s=0;i<this.sections.length&&s<=t;){let r=this.sections[i++],o=this.sections[i++],l=s+r;if(o>=0&&s<=t&&l>=e)return s<e&&l>t?"cover":!0;s=l}return!1}toString(){let e="";for(let t=0;t<this.sections.length;){let i=this.sections[t++],s=this.sections[t++];e+=(e?" ":"")+i+(s>=0?":"+s:"")}return e}toJSON(){return this.sections}static fromJSON(e){if(!Array.isArray(e)||e.length%2||e.some(t=>typeof t!="number"))throw new RangeError("Invalid JSON representation of ChangeDesc");return new gt(e)}static create(e){return new gt(e)}}class ce extends gt{constructor(e,t){super(e),this.inserted=t}apply(e){if(this.length!=e.length)throw new RangeError("Applying change set to a document with the wrong length");return $r(this,(t,i,s,r,o)=>e=e.replace(s,s+(i-t),o),!1),e}mapDesc(e,t=!1){return qr(this,e,t,!0)}invert(e){let t=this.sections.slice(),i=[];for(let s=0,r=0;s<t.length;s+=2){let o=t[s],l=t[s+1];if(l>=0){t[s]=l,t[s+1]=o;let a=s>>1;for(;i.length<a;)i.push(G.empty);i.push(o?e.slice(r,r+o):G.empty)}r+=o}return new ce(t,i)}compose(e){return this.empty?e:e.empty?this:ec(this,e,!0)}map(e,t=!1){return e.empty?this:qr(this,e,t,!0)}iterChanges(e,t=!1){$r(this,e,t)}get desc(){return gt.create(this.sections)}filter(e){let t=[],i=[],s=[],r=new Zi(this);e:for(let o=0,l=0;;){let a=o==e.length?1e9:e[o++];for(;l<a||l==a&&r.len==0;){if(r.done)break e;let c=Math.min(r.len,a-l);Ce(s,c,-1);let f=r.ins==-1?-1:r.off==0?r.ins:0;Ce(t,c,f),f>0&&Lt(i,t,r.text),r.forward(c),l+=c}let h=e[o++];for(;l<h;){if(r.done)break e;let c=Math.min(r.len,h-l);Ce(t,c,-1),Ce(s,c,r.ins==-1?-1:r.off==0?r.ins:0),r.forward(c),l+=c}}return{changes:new ce(t,i),filtered:gt.create(s)}}toJSON(){let e=[];for(let t=0;t<this.sections.length;t+=2){let i=this.sections[t],s=this.sections[t+1];s<0?e.push(i):s==0?e.push([i]):e.push([i].concat(this.inserted[t>>1].toJSON()))}return e}static of(e,t,i){let s=[],r=[],o=0,l=null;function a(c=!1){if(!c&&!s.length)return;o<t&&Ce(s,t-o,-1);let f=new ce(s,r);l=l?l.compose(f.map(l)):f,s=[],r=[],o=0}function h(c){if(Array.isArray(c))for(let f of c)h(f);else if(c instanceof ce){if(c.length!=t)throw new RangeError(`Mismatched change set length (got ${c.length}, expected ${t})`);a(),l=l?l.compose(c.map(l)):c}else{let{from:f,to:u=f,insert:d}=c;if(f>u||f<0||u>t)throw new RangeError(`Invalid change range ${f} to ${u} (in doc of length ${t})`);let m=d?typeof d=="string"?G.of(d.split(i||_r)):d:G.empty,x=m.length;if(f==u&&x==0)return;f<o&&a(),f>o&&Ce(s,f-o,-1),Ce(s,u-f,x),Lt(r,s,m),o=u}}return h(e),a(!l),l}static empty(e){return new ce(e?[e,-1]:[],[])}static fromJSON(e){if(!Array.isArray(e))throw new RangeError("Invalid JSON representation of ChangeSet");let t=[],i=[];for(let s=0;s<e.length;s++){let r=e[s];if(typeof r=="number")t.push(r,-1);else{if(!Array.isArray(r)||typeof r[0]!="number"||r.some((o,l)=>l&&typeof o!="string"))throw new RangeError("Invalid JSON representation of ChangeSet");if(r.length==1)t.push(r[0],0);else{for(;i.length<s;)i.push(G.empty);i[s]=G.of(r.slice(1)),t.push(r[0],i[s].length)}}}return new ce(t,i)}static createSet(e,t){return new ce(e,t)}}function Ce(n,e,t,i=!1){if(e==0&&t<=0)return;let s=n.length-2;s>=0&&t<=0&&t==n[s+1]?n[s]+=e:s>=0&&e==0&&n[s]==0?n[s+1]+=t:i?(n[s]+=e,n[s+1]+=t):n.push(e,t)}function Lt(n,e,t){if(t.length==0)return;let i=e.length-2>>1;if(i<n.length)n[n.length-1]=n[n.length-1].append(t);else{for(;n.length<i;)n.push(G.empty);n.push(t)}}function $r(n,e,t){let i=n.inserted;for(let s=0,r=0,o=0;o<n.sections.length;){let l=n.sections[o++],a=n.sections[o++];if(a<0)s+=l,r+=l;else{let h=s,c=r,f=G.empty;for(;h+=l,c+=a,a&&i&&(f=f.append(i[o-2>>1])),!(t||o==n.sections.length||n.sections[o+1]<0);)l=n.sections[o++],a=n.sections[o++];e(s,h,r,c,f),s=h,r=c}}}function qr(n,e,t,i=!1){let s=[],r=i?[]:null,o=new Zi(n),l=new Zi(e);for(let a=-1;;){if(o.done&&l.len||l.done&&o.len)throw new Error("Mismatched change set lengths");if(o.ins==-1&&l.ins==-1){let h=Math.min(o.len,l.len);Ce(s,h,-1),o.forward(h),l.forward(h)}else if(l.ins>=0&&(o.ins<0||a==o.i||o.off==0&&(l.len<o.len||l.len==o.len&&!t))){let h=l.len;for(Ce(s,l.ins,-1);h;){let c=Math.min(o.len,h);o.ins>=0&&a<o.i&&o.len<=c&&(Ce(s,0,o.ins),r&&Lt(r,s,o.text),a=o.i),o.forward(c),h-=c}l.next()}else if(o.ins>=0){let h=0,c=o.len;for(;c;)if(l.ins==-1){let f=Math.min(c,l.len);h+=f,c-=f,l.forward(f)}else if(l.ins==0&&l.len<c)c-=l.len,l.next();else break;Ce(s,h,a<o.i?o.ins:0),r&&a<o.i&&Lt(r,s,o.text),a=o.i,o.forward(o.len-c)}else{if(o.done&&l.done)return r?ce.createSet(s,r):gt.create(s);throw new Error("Mismatched change set lengths")}}}function ec(n,e,t=!1){let i=[],s=t?[]:null,r=new Zi(n),o=new Zi(e);for(let l=!1;;){if(r.done&&o.done)return s?ce.createSet(i,s):gt.create(i);if(r.ins==0)Ce(i,r.len,0,l),r.next();else if(o.len==0&&!o.done)Ce(i,0,o.ins,l),s&&Lt(s,i,o.text),o.next();else{if(r.done||o.done)throw new Error("Mismatched change set lengths");{let a=Math.min(r.len2,o.len),h=i.length;if(r.ins==-1){let c=o.ins==-1?-1:o.off?0:o.ins;Ce(i,a,c,l),s&&c&&Lt(s,i,o.text)}else o.ins==-1?(Ce(i,r.off?0:r.len,a,l),s&&Lt(s,i,r.textBit(a))):(Ce(i,r.off?0:r.len,o.off?0:o.ins,l),s&&!o.off&&Lt(s,i,o.text));l=(r.ins>a||o.ins>=0&&o.len>a)&&(l||i.length>h),r.forward2(a),o.forward(a)}}}}class Zi{constructor(e){this.set=e,this.i=0,this.next()}next(){let{sections:e}=this.set;this.i<e.length?(this.len=e[this.i++],this.ins=e[this.i++]):(this.len=0,this.ins=-2),this.off=0}get done(){return this.ins==-2}get len2(){return this.ins<0?this.len:this.ins}get text(){let{inserted:e}=this.set,t=this.i-2>>1;return t>=e.length?G.empty:e[t]}textBit(e){let{inserted:t}=this.set,i=this.i-2>>1;return i>=t.length&&!e?G.empty:t[i].slice(this.off,e==null?void 0:this.off+e)}forward(e){e==this.len?this.next():(this.len-=e,this.off+=e)}forward2(e){this.ins==-1?this.forward(e):e==this.ins?this.next():(this.ins-=e,this.off+=e)}}class Jt{constructor(e,t,i){this.from=e,this.to=t,this.flags=i}get anchor(){return this.flags&32?this.to:this.from}get head(){return this.flags&32?this.from:this.to}get empty(){return this.from==this.to}get assoc(){return this.flags&8?-1:this.flags&16?1:0}get bidiLevel(){let e=this.flags&7;return e==7?null:e}get goalColumn(){let e=this.flags>>6;return e==16777215?void 0:e}map(e,t=-1){let i,s;return this.empty?i=s=e.mapPos(this.from,t):(i=e.mapPos(this.from,1),s=e.mapPos(this.to,-1)),i==this.from&&s==this.to?this:new Jt(i,s,this.flags)}extend(e,t=e){if(e<=this.anchor&&t>=this.anchor)return E.range(e,t);let i=Math.abs(e-this.anchor)>Math.abs(t-this.anchor)?e:t;return E.range(this.anchor,i)}eq(e,t=!1){return this.anchor==e.anchor&&this.head==e.head&&(!t||!this.empty||this.assoc==e.assoc)}toJSON(){return{anchor:this.anchor,head:this.head}}static fromJSON(e){if(!e||typeof e.anchor!="number"||typeof e.head!="number")throw new RangeError("Invalid JSON representation for SelectionRange");return E.range(e.anchor,e.head)}static create(e,t,i){return new Jt(e,t,i)}}class E{constructor(e,t){this.ranges=e,this.mainIndex=t}map(e,t=-1){return e.empty?this:E.create(this.ranges.map(i=>i.map(e,t)),this.mainIndex)}eq(e,t=!1){if(this.ranges.length!=e.ranges.length||this.mainIndex!=e.mainIndex)return!1;for(let i=0;i<this.ranges.length;i++)if(!this.ranges[i].eq(e.ranges[i],t))return!1;return!0}get main(){return this.ranges[this.mainIndex]}asSingle(){return this.ranges.length==1?this:new E([this.main],0)}addRange(e,t=!0){return E.create([e].concat(this.ranges),t?0:this.mainIndex+1)}replaceRange(e,t=this.mainIndex){let i=this.ranges.slice();return i[t]=e,E.create(i,this.mainIndex)}toJSON(){return{ranges:this.ranges.map(e=>e.toJSON()),main:this.mainIndex}}static fromJSON(e){if(!e||!Array.isArray(e.ranges)||typeof e.main!="number"||e.main>=e.ranges.length)throw new RangeError("Invalid JSON representation for EditorSelection");return new E(e.ranges.map(t=>Jt.fromJSON(t)),e.main)}static single(e,t=e){return new E([E.range(e,t)],0)}static create(e,t=0){if(e.length==0)throw new RangeError("A selection needs at least one range");for(let i=0,s=0;s<e.length;s++){let r=e[s];if(r.empty?r.from<=i:r.from<i)return E.normalized(e.slice(),t);i=r.to}return new E(e,t)}static cursor(e,t=0,i,s){return Jt.create(e,e,(t==0?0:t<0?8:16)|(i==null?7:Math.min(6,i))|(s??16777215)<<6)}static range(e,t,i,s){let r=(i??16777215)<<6|(s==null?7:Math.min(6,s));return t<e?Jt.create(t,e,48|r):Jt.create(e,t,(t>e?8:0)|r)}static normalized(e,t=0){let i=e[t];e.sort((s,r)=>s.from-r.from),t=e.indexOf(i);for(let s=1;s<e.length;s++){let r=e[s],o=e[s-1];if(r.empty?r.from<=o.to:r.from<o.to){let l=o.from,a=Math.max(r.to,o.to);s<=t&&t--,e.splice(--s,2,r.anchor>r.head?E.range(a,l):E.range(l,a))}}return new E(e,t)}}function tc(n,e){for(let t of n.ranges)if(t.to>e)throw new RangeError("Selection points outside of document")}let Ho=0;class F{constructor(e,t,i,s,r){this.combine=e,this.compareInput=t,this.compare=i,this.isStatic=s,this.id=Ho++,this.default=e([]),this.extensions=typeof r=="function"?r(this):r}get reader(){return this}static define(e={}){return new F(e.combine||(t=>t),e.compareInput||((t,i)=>t===i),e.compare||(e.combine?(t,i)=>t===i:zo),!!e.static,e.enables)}of(e){return new Yn([],this,0,e)}compute(e,t){if(this.isStatic)throw new Error("Can't compute a static facet");return new Yn(e,this,1,t)}computeN(e,t){if(this.isStatic)throw new Error("Can't compute a static facet");return new Yn(e,this,2,t)}from(e,t){return t||(t=i=>i),this.compute([e],i=>t(i.field(e)))}}function zo(n,e){return n==e||n.length==e.length&&n.every((t,i)=>t===e[i])}class Yn{constructor(e,t,i,s){this.dependencies=e,this.facet=t,this.type=i,this.value=s,this.id=Ho++}dynamicSlot(e){var t;let i=this.value,s=this.facet.compareInput,r=this.id,o=e[r]>>1,l=this.type==2,a=!1,h=!1,c=[];for(let f of this.dependencies)f=="doc"?a=!0:f=="selection"?h=!0:(((t=e[f.id])!==null&&t!==void 0?t:1)&1)==0&&c.push(e[f.id]);return{create(f){return f.values[o]=i(f),1},update(f,u){if(a&&u.docChanged||h&&(u.docChanged||u.selection)||Hr(f,c)){let d=i(f);if(l?!Ll(d,f.values[o],s):!s(d,f.values[o]))return f.values[o]=d,1}return 0},reconfigure:(f,u)=>{let d,m=u.config.address[r];if(m!=null){let x=us(u,m);if(this.dependencies.every(S=>S instanceof F?u.facet(S)===f.facet(S):S instanceof ue?u.field(S,!1)==f.field(S,!1):!0)||(l?Ll(d=i(f),x,s):s(d=i(f),x)))return f.values[o]=x,0}else d=i(f);return f.values[o]=d,1}}}}function Ll(n,e,t){if(n.length!=e.length)return!1;for(let i=0;i<n.length;i++)if(!t(n[i],e[i]))return!1;return!0}function Hr(n,e){let t=!1;for(let i of e)Gi(n,i)&1&&(t=!0);return t}function Pd(n,e,t){let i=t.map(a=>n[a.id]),s=t.map(a=>a.type),r=i.filter(a=>!(a&1)),o=n[e.id]>>1;function l(a){let h=[];for(let c=0;c<i.length;c++){let f=us(a,i[c]);if(s[c]==2)for(let u of f)h.push(u);else h.push(f)}return e.combine(h)}return{create(a){for(let h of i)Gi(a,h);return a.values[o]=l(a),1},update(a,h){if(!Hr(a,r))return 0;let c=l(a);return e.compare(c,a.values[o])?0:(a.values[o]=c,1)},reconfigure(a,h){let c=Hr(a,i),f=h.config.facets[e.id],u=h.facet(e);if(f&&!c&&zo(t,f))return a.values[o]=u,0;let d=l(a);return e.compare(d,u)?(a.values[o]=u,0):(a.values[o]=d,1)}}}const Cn=F.define({static:!0});class ue{constructor(e,t,i,s,r){this.id=e,this.createF=t,this.updateF=i,this.compareF=s,this.spec=r,this.provides=void 0}static define(e){let t=new ue(Ho++,e.create,e.update,e.compare||((i,s)=>i===s),e);return e.provide&&(t.provides=e.provide(t)),t}create(e){let t=e.facet(Cn).find(i=>i.field==this);return((t==null?void 0:t.create)||this.createF)(e)}slot(e){let t=e[this.id]>>1;return{create:i=>(i.values[t]=this.create(i),1),update:(i,s)=>{let r=i.values[t],o=this.updateF(r,s);return this.compareF(r,o)?0:(i.values[t]=o,1)},reconfigure:(i,s)=>{let r=i.facet(Cn),o=s.facet(Cn),l;return(l=r.find(a=>a.field==this))&&l!=o.find(a=>a.field==this)?(i.values[t]=l.create(i),1):s.config.address[this.id]!=null?(i.values[t]=s.field(this),0):(i.values[t]=this.create(i),1)}}}init(e){return[this,Cn.of({field:this,create:e})]}get extension(){return this}}const Xt={lowest:4,low:3,default:2,high:1,highest:0};function Fi(n){return e=>new ic(e,n)}const li={highest:Fi(Xt.highest),high:Fi(Xt.high),default:Fi(Xt.default),low:Fi(Xt.low),lowest:Fi(Xt.lowest)};class ic{constructor(e,t){this.inner=e,this.prec=t}}class gn{of(e){return new zr(this,e)}reconfigure(e){return gn.reconfigure.of({compartment:this,extension:e})}get(e){return e.config.compartments.get(this)}}class zr{constructor(e,t){this.compartment=e,this.inner=t}}class fs{constructor(e,t,i,s,r,o){for(this.base=e,this.compartments=t,this.dynamicSlots=i,this.address=s,this.staticValues=r,this.facets=o,this.statusTemplate=[];this.statusTemplate.length<i.length;)this.statusTemplate.push(0)}staticFacet(e){let t=this.address[e.id];return t==null?e.default:this.staticValues[t>>1]}static resolve(e,t,i){let s=[],r=Object.create(null),o=new Map;for(let u of Nd(e,t,o))u instanceof ue?s.push(u):(r[u.facet.id]||(r[u.facet.id]=[])).push(u);let l=Object.create(null),a=[],h=[];for(let u of s)l[u.id]=h.length<<1,h.push(d=>u.slot(d));let c=i==null?void 0:i.config.facets;for(let u in r){let d=r[u],m=d[0].facet,x=c&&c[u]||[];if(d.every(S=>S.type==0))if(l[m.id]=a.length<<1|1,zo(x,d))a.push(i.facet(m));else{let S=m.combine(d.map(y=>y.value));a.push(i&&m.compare(S,i.facet(m))?i.facet(m):S)}else{for(let S of d)S.type==0?(l[S.id]=a.length<<1|1,a.push(S.value)):(l[S.id]=h.length<<1,h.push(y=>S.dynamicSlot(y)));l[m.id]=h.length<<1,h.push(S=>Pd(S,m,d))}}let f=h.map(u=>u(l));return new fs(e,o,f,l,a,r)}}function Nd(n,e,t){let i=[[],[],[],[],[]],s=new Map;function r(o,l){let a=s.get(o);if(a!=null){if(a<=l)return;let h=i[a].indexOf(o);h>-1&&i[a].splice(h,1),o instanceof zr&&t.delete(o.compartment)}if(s.set(o,l),Array.isArray(o))for(let h of o)r(h,l);else if(o instanceof zr){if(t.has(o.compartment))throw new RangeError("Duplicate use of compartment in extensions");let h=e.get(o.compartment)||o.inner;t.set(o.compartment,h),r(h,l)}else if(o instanceof ic)r(o.inner,o.prec);else if(o instanceof ue)i[l].push(o),o.provides&&r(o.provides,l);else if(o instanceof Yn)i[l].push(o),o.facet.extensions&&r(o.facet.extensions,Xt.default);else{let h=o.extension;if(!h)throw new Error(`Unrecognized extension value in extension set (${o}). This sometimes happens because multiple instances of @codemirror/state are loaded, breaking instanceof checks.`);r(h,l)}}return r(n,Xt.default),i.reduce((o,l)=>o.concat(l))}function Gi(n,e){if(e&1)return 2;let t=e>>1,i=n.status[t];if(i==4)throw new Error("Cyclic dependency between fields and/or facets");if(i&2)return i;n.status[t]=4;let s=n.computeSlot(n,n.config.dynamicSlots[t]);return n.status[t]=2|s}function us(n,e){return e&1?n.config.staticValues[e>>1]:n.values[e>>1]}const nc=F.define(),jr=F.define({combine:n=>n.some(e=>e),static:!0}),sc=F.define({combine:n=>n.length?n[0]:void 0,static:!0}),rc=F.define(),oc=F.define(),lc=F.define(),ac=F.define({combine:n=>n.length?n[0]:!1});class Mt{constructor(e,t){this.type=e,this.value=t}static define(){return new Rd}}class Rd{of(e){return new Mt(this,e)}}class Bd{constructor(e){this.map=e}of(e){return new _(this,e)}}class _{constructor(e,t){this.type=e,this.value=t}map(e){let t=this.type.map(this.value,e);return t===void 0?void 0:t==this.value?this:new _(this.type,t)}is(e){return this.type==e}static define(e={}){return new Bd(e.map||(t=>t))}static mapEffects(e,t){if(!e.length)return e;let i=[];for(let s of e){let r=s.map(t);r&&i.push(r)}return i}}_.reconfigure=_.define();_.appendConfig=_.define();class fe{constructor(e,t,i,s,r,o){this.startState=e,this.changes=t,this.selection=i,this.effects=s,this.annotations=r,this.scrollIntoView=o,this._doc=null,this._state=null,i&&tc(i,t.newLength),r.some(l=>l.type==fe.time)||(this.annotations=r.concat(fe.time.of(Date.now())))}static create(e,t,i,s,r,o){return new fe(e,t,i,s,r,o)}get newDoc(){return this._doc||(this._doc=this.changes.apply(this.startState.doc))}get newSelection(){return this.selection||this.startState.selection.map(this.changes)}get state(){return this._state||this.startState.applyTransaction(this),this._state}annotation(e){for(let t of this.annotations)if(t.type==e)return t.value}get docChanged(){return!this.changes.empty}get reconfigured(){return this.startState.config!=this.state.config}isUserEvent(e){let t=this.annotation(fe.userEvent);return!!(t&&(t==e||t.length>e.length&&t.slice(0,e.length)==e&&t[e.length]=="."))}}fe.time=Mt.define();fe.userEvent=Mt.define();fe.addToHistory=Mt.define();fe.remote=Mt.define();function Id(n,e){let t=[];for(let i=0,s=0;;){let r,o;if(i<n.length&&(s==e.length||e[s]>=n[i]))r=n[i++],o=n[i++];else if(s<e.length)r=e[s++],o=e[s++];else return t;!t.length||t[t.length-1]<r?t.push(r,o):t[t.length-1]<o&&(t[t.length-1]=o)}}function hc(n,e,t){var i;let s,r,o;return t?(s=e.changes,r=ce.empty(e.changes.length),o=n.changes.compose(e.changes)):(s=e.changes.map(n.changes),r=n.changes.mapDesc(e.changes,!0),o=n.changes.compose(s)),{changes:o,selection:e.selection?e.selection.map(r):(i=n.selection)===null||i===void 0?void 0:i.map(s),effects:_.mapEffects(n.effects,s).concat(_.mapEffects(e.effects,r)),annotations:n.annotations.length?n.annotations.concat(e.annotations):e.annotations,scrollIntoView:n.scrollIntoView||e.scrollIntoView}}function Kr(n,e,t){let i=e.selection,s=bi(e.annotations);return e.userEvent&&(s=s.concat(fe.userEvent.of(e.userEvent))),{changes:e.changes instanceof ce?e.changes:ce.of(e.changes||[],t,n.facet(sc)),selection:i&&(i instanceof E?i:E.single(i.anchor,i.head)),effects:bi(e.effects),annotations:s,scrollIntoView:!!e.scrollIntoView}}function cc(n,e,t){let i=Kr(n,e.length?e[0]:{},n.doc.length);e.length&&e[0].filter===!1&&(t=!1);for(let r=1;r<e.length;r++){e[r].filter===!1&&(t=!1);let o=!!e[r].sequential;i=hc(i,Kr(n,e[r],o?i.changes.newLength:n.doc.length),o)}let s=fe.create(n,i.changes,i.selection,i.effects,i.annotations,i.scrollIntoView);return Fd(t?Ld(s):s)}function Ld(n){let e=n.startState,t=!0;for(let s of e.facet(rc)){let r=s(n);if(r===!1){t=!1;break}Array.isArray(r)&&(t=t===!0?r:Id(t,r))}if(t!==!0){let s,r;if(t===!1)r=n.changes.invertedDesc,s=ce.empty(e.doc.length);else{let o=n.changes.filter(t);s=o.changes,r=o.filtered.mapDesc(o.changes).invertedDesc}n=fe.create(e,s,n.selection&&n.selection.map(r),_.mapEffects(n.effects,r),n.annotations,n.scrollIntoView)}let i=e.facet(oc);for(let s=i.length-1;s>=0;s--){let r=i[s](n);r instanceof fe?n=r:Array.isArray(r)&&r.length==1&&r[0]instanceof fe?n=r[0]:n=cc(e,bi(r),!1)}return n}function Fd(n){let e=n.startState,t=e.facet(lc),i=n;for(let s=t.length-1;s>=0;s--){let r=t[s](n);r&&Object.keys(r).length&&(i=hc(i,Kr(e,r,n.changes.newLength),!0))}return i==n?n:fe.create(e,n.changes,n.selection,i.effects,i.annotations,i.scrollIntoView)}const Vd=[];function bi(n){return n==null?Vd:Array.isArray(n)?n:[n]}var te=function(n){return n[n.Word=0]="Word",n[n.Space=1]="Space",n[n.Other=2]="Other",n}(te||(te={}));const Wd=/[\u00df\u0587\u0590-\u05f4\u0600-\u06ff\u3040-\u309f\u30a0-\u30ff\u3400-\u4db5\u4e00-\u9fcc\uac00-\ud7af]/;let Ur;try{Ur=new RegExp("[\\p{Alphabetic}\\p{Number}_]","u")}catch{}function _d(n){if(Ur)return Ur.test(n);for(let e=0;e<n.length;e++){let t=n[e];if(/\w/.test(t)||t>""&&(t.toUpperCase()!=t.toLowerCase()||Wd.test(t)))return!0}return!1}function $d(n){return e=>{if(!/\S/.test(e))return te.Space;if(_d(e))return te.Word;for(let t=0;t<n.length;t++)if(e.indexOf(n[t])>-1)return te.Word;return te.Other}}class j{constructor(e,t,i,s,r,o){this.config=e,this.doc=t,this.selection=i,this.values=s,this.status=e.statusTemplate.slice(),this.computeSlot=r,o&&(o._state=this);for(let l=0;l<this.config.dynamicSlots.length;l++)Gi(this,l<<1);this.computeSlot=null}field(e,t=!0){let i=this.config.address[e.id];if(i==null){if(t)throw new RangeError("Field is not present in this state");return}return Gi(this,i),us(this,i)}update(...e){return cc(this,e,!0)}applyTransaction(e){let t=this.config,{base:i,compartments:s}=t;for(let l of e.effects)l.is(gn.reconfigure)?(t&&(s=new Map,t.compartments.forEach((a,h)=>s.set(h,a)),t=null),s.set(l.value.compartment,l.value.extension)):l.is(_.reconfigure)?(t=null,i=l.value):l.is(_.appendConfig)&&(t=null,i=bi(i).concat(l.value));let r;t?r=e.startState.values.slice():(t=fs.resolve(i,s,this),r=new j(t,this.doc,this.selection,t.dynamicSlots.map(()=>null),(a,h)=>h.reconfigure(a,this),null).values);let o=e.startState.facet(jr)?e.newSelection:e.newSelection.asSingle();new j(t,e.newDoc,o,r,(l,a)=>a.update(l,e),e)}replaceSelection(e){return typeof e=="string"&&(e=this.toText(e)),this.changeByRange(t=>({changes:{from:t.from,to:t.to,insert:e},range:E.cursor(t.from+e.length)}))}changeByRange(e){let t=this.selection,i=e(t.ranges[0]),s=this.changes(i.changes),r=[i.range],o=bi(i.effects);for(let l=1;l<t.ranges.length;l++){let a=e(t.ranges[l]),h=this.changes(a.changes),c=h.map(s);for(let u=0;u<l;u++)r[u]=r[u].map(c);let f=s.mapDesc(h,!0);r.push(a.range.map(f)),s=s.compose(c),o=_.mapEffects(o,c).concat(_.mapEffects(bi(a.effects),f))}return{changes:s,selection:E.create(r,t.mainIndex),effects:o}}changes(e=[]){return e instanceof ce?e:ce.of(e,this.doc.length,this.facet(j.lineSeparator))}toText(e){return G.of(e.split(this.facet(j.lineSeparator)||_r))}sliceDoc(e=0,t=this.doc.length){return this.doc.sliceString(e,t,this.lineBreak)}facet(e){let t=this.config.address[e.id];return t==null?e.default:(Gi(this,t),us(this,t))}toJSON(e){let t={doc:this.sliceDoc(),selection:this.selection.toJSON()};if(e)for(let i in e){let s=e[i];s instanceof ue&&this.config.address[s.id]!=null&&(t[i]=s.spec.toJSON(this.field(e[i]),this))}return t}static fromJSON(e,t={},i){if(!e||typeof e.doc!="string")throw new RangeError("Invalid JSON representation for EditorState");let s=[];if(i){for(let r in i)if(Object.prototype.hasOwnProperty.call(e,r)){let o=i[r],l=e[r];s.push(o.init(a=>o.spec.fromJSON(l,a)))}}return j.create({doc:e.doc,selection:E.fromJSON(e.selection),extensions:t.extensions?s.concat([t.extensions]):s})}static create(e={}){let t=fs.resolve(e.extensions||[],new Map),i=e.doc instanceof G?e.doc:G.of((e.doc||"").split(t.staticFacet(j.lineSeparator)||_r)),s=e.selection?e.selection instanceof E?e.selection:E.single(e.selection.anchor,e.selection.head):E.single(0);return tc(s,i.length),t.staticFacet(jr)||(s=s.asSingle()),new j(t,i,s,t.dynamicSlots.map(()=>null),(r,o)=>o.create(r),null)}get tabSize(){return this.facet(j.tabSize)}get lineBreak(){return this.facet(j.lineSeparator)||`
`}get readOnly(){return this.facet(ac)}phrase(e,...t){for(let i of this.facet(j.phrases))if(Object.prototype.hasOwnProperty.call(i,e)){e=i[e];break}return t.length&&(e=e.replace(/\$(\$|\d*)/g,(i,s)=>{if(s=="$")return"$";let r=+(s||1);return!r||r>t.length?i:t[r-1]})),e}languageDataAt(e,t,i=-1){let s=[];for(let r of this.facet(nc))for(let o of r(this,t,i))Object.prototype.hasOwnProperty.call(o,e)&&s.push(o[e]);return s}charCategorizer(e){return $d(this.languageDataAt("wordChars",e).join(""))}wordAt(e){let{text:t,from:i,length:s}=this.doc.lineAt(e),r=this.charCategorizer(e),o=e-i,l=e-i;for(;o>0;){let a=Se(t,o,!1);if(r(t.slice(a,o))!=te.Word)break;o=a}for(;l<s;){let a=Se(t,l);if(r(t.slice(l,a))!=te.Word)break;l=a}return o==l?null:E.range(o+i,l+i)}}j.allowMultipleSelections=jr;j.tabSize=F.define({combine:n=>n.length?n[0]:4});j.lineSeparator=sc;j.readOnly=ac;j.phrases=F.define({compare(n,e){let t=Object.keys(n),i=Object.keys(e);return t.length==i.length&&t.every(s=>n[s]==e[s])}});j.languageData=nc;j.changeFilter=rc;j.transactionFilter=oc;j.transactionExtender=lc;gn.reconfigure=_.define();function nt(n,e,t={}){let i={};for(let s of n)for(let r of Object.keys(s)){let o=s[r],l=i[r];if(l===void 0)i[r]=o;else if(!(l===o||o===void 0))if(Object.hasOwnProperty.call(t,r))i[r]=t[r](l,o);else throw new Error("Config merge conflict for field "+r)}for(let s in e)i[s]===void 0&&(i[s]=e[s]);return i}class ii{eq(e){return this==e}range(e,t=e){return Gr.create(e,t,this)}}ii.prototype.startSide=ii.prototype.endSide=0;ii.prototype.point=!1;ii.prototype.mapMode=Ee.TrackDel;let Gr=class fc{constructor(e,t,i){this.from=e,this.to=t,this.value=i}static create(e,t,i){return new fc(e,t,i)}};function Xr(n,e){return n.from-e.from||n.value.startSide-e.value.startSide}class jo{constructor(e,t,i,s){this.from=e,this.to=t,this.value=i,this.maxPoint=s}get length(){return this.to[this.to.length-1]}findIndex(e,t,i,s=0){let r=i?this.to:this.from;for(let o=s,l=r.length;;){if(o==l)return o;let a=o+l>>1,h=r[a]-e||(i?this.value[a].endSide:this.value[a].startSide)-t;if(a==o)return h>=0?o:l;h>=0?l=a:o=a+1}}between(e,t,i,s){for(let r=this.findIndex(t,-1e9,!0),o=this.findIndex(i,1e9,!1,r);r<o;r++)if(s(this.from[r]+e,this.to[r]+e,this.value[r])===!1)return!1}map(e,t){let i=[],s=[],r=[],o=-1,l=-1;for(let a=0;a<this.value.length;a++){let h=this.value[a],c=this.from[a]+e,f=this.to[a]+e,u,d;if(c==f){let m=t.mapPos(c,h.startSide,h.mapMode);if(m==null||(u=d=m,h.startSide!=h.endSide&&(d=t.mapPos(c,h.endSide),d<u)))continue}else if(u=t.mapPos(c,h.startSide),d=t.mapPos(f,h.endSide),u>d||u==d&&h.startSide>0&&h.endSide<=0)continue;(d-u||h.endSide-h.startSide)<0||(o<0&&(o=u),h.point&&(l=Math.max(l,d-u)),i.push(h),s.push(u-o),r.push(d-o))}return{mapped:i.length?new jo(s,r,i,l):null,pos:o}}}class U{constructor(e,t,i,s){this.chunkPos=e,this.chunk=t,this.nextLayer=i,this.maxPoint=s}static create(e,t,i,s){return new U(e,t,i,s)}get length(){let e=this.chunk.length-1;return e<0?0:Math.max(this.chunkEnd(e),this.nextLayer.length)}get size(){if(this.isEmpty)return 0;let e=this.nextLayer.size;for(let t of this.chunk)e+=t.value.length;return e}chunkEnd(e){return this.chunkPos[e]+this.chunk[e].length}update(e){let{add:t=[],sort:i=!1,filterFrom:s=0,filterTo:r=this.length}=e,o=e.filter;if(t.length==0&&!o)return this;if(i&&(t=t.slice().sort(Xr)),this.isEmpty)return t.length?U.of(t):this;let l=new uc(this,null,-1).goto(0),a=0,h=[],c=new At;for(;l.value||a<t.length;)if(a<t.length&&(l.from-t[a].from||l.startSide-t[a].value.startSide)>=0){let f=t[a++];c.addInner(f.from,f.to,f.value)||h.push(f)}else l.rangeIndex==1&&l.chunkIndex<this.chunk.length&&(a==t.length||this.chunkEnd(l.chunkIndex)<t[a].from)&&(!o||s>this.chunkEnd(l.chunkIndex)||r<this.chunkPos[l.chunkIndex])&&c.addChunk(this.chunkPos[l.chunkIndex],this.chunk[l.chunkIndex])?l.nextChunk():((!o||s>l.to||r<l.from||o(l.from,l.to,l.value))&&(c.addInner(l.from,l.to,l.value)||h.push(Gr.create(l.from,l.to,l.value))),l.next());return c.finishInner(this.nextLayer.isEmpty&&!h.length?U.empty:this.nextLayer.update({add:h,filter:o,filterFrom:s,filterTo:r}))}map(e){if(e.empty||this.isEmpty)return this;let t=[],i=[],s=-1;for(let o=0;o<this.chunk.length;o++){let l=this.chunkPos[o],a=this.chunk[o],h=e.touchesRange(l,l+a.length);if(h===!1)s=Math.max(s,a.maxPoint),t.push(a),i.push(e.mapPos(l));else if(h===!0){let{mapped:c,pos:f}=a.map(l,e);c&&(s=Math.max(s,c.maxPoint),t.push(c),i.push(f))}}let r=this.nextLayer.map(e);return t.length==0?r:new U(i,t,r||U.empty,s)}between(e,t,i){if(!this.isEmpty){for(let s=0;s<this.chunk.length;s++){let r=this.chunkPos[s],o=this.chunk[s];if(t>=r&&e<=r+o.length&&o.between(r,e-r,t-r,i)===!1)return}this.nextLayer.between(e,t,i)}}iter(e=0){return en.from([this]).goto(e)}get isEmpty(){return this.nextLayer==this}static iter(e,t=0){return en.from(e).goto(t)}static compare(e,t,i,s,r=-1){let o=e.filter(f=>f.maxPoint>0||!f.isEmpty&&f.maxPoint>=r),l=t.filter(f=>f.maxPoint>0||!f.isEmpty&&f.maxPoint>=r),a=Fl(o,l,i),h=new Vi(o,a,r),c=new Vi(l,a,r);i.iterGaps((f,u,d)=>Vl(h,f,c,u,d,s)),i.empty&&i.length==0&&Vl(h,0,c,0,0,s)}static eq(e,t,i=0,s){s==null&&(s=999999999);let r=e.filter(c=>!c.isEmpty&&t.indexOf(c)<0),o=t.filter(c=>!c.isEmpty&&e.indexOf(c)<0);if(r.length!=o.length)return!1;if(!r.length)return!0;let l=Fl(r,o),a=new Vi(r,l,0).goto(i),h=new Vi(o,l,0).goto(i);for(;;){if(a.to!=h.to||!Qr(a.active,h.active)||a.point&&(!h.point||!a.point.eq(h.point)))return!1;if(a.to>s)return!0;a.next(),h.next()}}static spans(e,t,i,s,r=-1){let o=new Vi(e,null,r).goto(t),l=t,a=o.openStart;for(;;){let h=Math.min(o.to,i);if(o.point){let c=o.activeForPoint(o.to),f=o.pointFrom<t?c.length+1:o.point.startSide<0?c.length:Math.min(c.length,a);s.point(l,h,o.point,c,f,o.pointRank),a=Math.min(o.openEnd(h),c.length)}else h>l&&(s.span(l,h,o.active,a),a=o.openEnd(h));if(o.to>i)return a+(o.point&&o.to>i?1:0);l=o.to,o.next()}}static of(e,t=!1){let i=new At;for(let s of e instanceof Gr?[e]:t?qd(e):e)i.add(s.from,s.to,s.value);return i.finish()}static join(e){if(!e.length)return U.empty;let t=e[e.length-1];for(let i=e.length-2;i>=0;i--)for(let s=e[i];s!=U.empty;s=s.nextLayer)t=new U(s.chunkPos,s.chunk,t,Math.max(s.maxPoint,t.maxPoint));return t}}U.empty=new U([],[],null,-1);function qd(n){if(n.length>1)for(let e=n[0],t=1;t<n.length;t++){let i=n[t];if(Xr(e,i)>0)return n.slice().sort(Xr);e=i}return n}U.empty.nextLayer=U.empty;class At{finishChunk(e){this.chunks.push(new jo(this.from,this.to,this.value,this.maxPoint)),this.chunkPos.push(this.chunkStart),this.chunkStart=-1,this.setMaxPoint=Math.max(this.setMaxPoint,this.maxPoint),this.maxPoint=-1,e&&(this.from=[],this.to=[],this.value=[])}constructor(){this.chunks=[],this.chunkPos=[],this.chunkStart=-1,this.last=null,this.lastFrom=-1e9,this.lastTo=-1e9,this.from=[],this.to=[],this.value=[],this.maxPoint=-1,this.setMaxPoint=-1,this.nextLayer=null}add(e,t,i){this.addInner(e,t,i)||(this.nextLayer||(this.nextLayer=new At)).add(e,t,i)}addInner(e,t,i){let s=e-this.lastTo||i.startSide-this.last.endSide;if(s<=0&&(e-this.lastFrom||i.startSide-this.last.startSide)<0)throw new Error("Ranges must be added sorted by `from` position and `startSide`");return s<0?!1:(this.from.length==250&&this.finishChunk(!0),this.chunkStart<0&&(this.chunkStart=e),this.from.push(e-this.chunkStart),this.to.push(t-this.chunkStart),this.last=i,this.lastFrom=e,this.lastTo=t,this.value.push(i),i.point&&(this.maxPoint=Math.max(this.maxPoint,t-e)),!0)}addChunk(e,t){if((e-this.lastTo||t.value[0].startSide-this.last.endSide)<0)return!1;this.from.length&&this.finishChunk(!0),this.setMaxPoint=Math.max(this.setMaxPoint,t.maxPoint),this.chunks.push(t),this.chunkPos.push(e);let i=t.value.length-1;return this.last=t.value[i],this.lastFrom=t.from[i]+e,this.lastTo=t.to[i]+e,!0}finish(){return this.finishInner(U.empty)}finishInner(e){if(this.from.length&&this.finishChunk(!1),this.chunks.length==0)return e;let t=U.create(this.chunkPos,this.chunks,this.nextLayer?this.nextLayer.finishInner(e):e,this.setMaxPoint);return this.from=null,t}}function Fl(n,e,t){let i=new Map;for(let r of n)for(let o=0;o<r.chunk.length;o++)r.chunk[o].maxPoint<=0&&i.set(r.chunk[o],r.chunkPos[o]);let s=new Set;for(let r of e)for(let o=0;o<r.chunk.length;o++){let l=i.get(r.chunk[o]);l!=null&&(t?t.mapPos(l):l)==r.chunkPos[o]&&!(t!=null&&t.touchesRange(l,l+r.chunk[o].length))&&s.add(r.chunk[o])}return s}class uc{constructor(e,t,i,s=0){this.layer=e,this.skip=t,this.minPoint=i,this.rank=s}get startSide(){return this.value?this.value.startSide:0}get endSide(){return this.value?this.value.endSide:0}goto(e,t=-1e9){return this.chunkIndex=this.rangeIndex=0,this.gotoInner(e,t,!1),this}gotoInner(e,t,i){for(;this.chunkIndex<this.layer.chunk.length;){let s=this.layer.chunk[this.chunkIndex];if(!(this.skip&&this.skip.has(s)||this.layer.chunkEnd(this.chunkIndex)<e||s.maxPoint<this.minPoint))break;this.chunkIndex++,i=!1}if(this.chunkIndex<this.layer.chunk.length){let s=this.layer.chunk[this.chunkIndex].findIndex(e-this.layer.chunkPos[this.chunkIndex],t,!0);(!i||this.rangeIndex<s)&&this.setRangeIndex(s)}this.next()}forward(e,t){(this.to-e||this.endSide-t)<0&&this.gotoInner(e,t,!0)}next(){for(;;)if(this.chunkIndex==this.layer.chunk.length){this.from=this.to=1e9,this.value=null;break}else{let e=this.layer.chunkPos[this.chunkIndex],t=this.layer.chunk[this.chunkIndex],i=e+t.from[this.rangeIndex];if(this.from=i,this.to=e+t.to[this.rangeIndex],this.value=t.value[this.rangeIndex],this.setRangeIndex(this.rangeIndex+1),this.minPoint<0||this.value.point&&this.to-this.from>=this.minPoint)break}}setRangeIndex(e){if(e==this.layer.chunk[this.chunkIndex].value.length){if(this.chunkIndex++,this.skip)for(;this.chunkIndex<this.layer.chunk.length&&this.skip.has(this.layer.chunk[this.chunkIndex]);)this.chunkIndex++;this.rangeIndex=0}else this.rangeIndex=e}nextChunk(){this.chunkIndex++,this.rangeIndex=0,this.next()}compare(e){return this.from-e.from||this.startSide-e.startSide||this.rank-e.rank||this.to-e.to||this.endSide-e.endSide}}class en{constructor(e){this.heap=e}static from(e,t=null,i=-1){let s=[];for(let r=0;r<e.length;r++)for(let o=e[r];!o.isEmpty;o=o.nextLayer)o.maxPoint>=i&&s.push(new uc(o,t,i,r));return s.length==1?s[0]:new en(s)}get startSide(){return this.value?this.value.startSide:0}goto(e,t=-1e9){for(let i of this.heap)i.goto(e,t);for(let i=this.heap.length>>1;i>=0;i--)er(this.heap,i);return this.next(),this}forward(e,t){for(let i of this.heap)i.forward(e,t);for(let i=this.heap.length>>1;i>=0;i--)er(this.heap,i);(this.to-e||this.value.endSide-t)<0&&this.next()}next(){if(this.heap.length==0)this.from=this.to=1e9,this.value=null,this.rank=-1;else{let e=this.heap[0];this.from=e.from,this.to=e.to,this.value=e.value,this.rank=e.rank,e.value&&e.next(),er(this.heap,0)}}}function er(n,e){for(let t=n[e];;){let i=(e<<1)+1;if(i>=n.length)break;let s=n[i];if(i+1<n.length&&s.compare(n[i+1])>=0&&(s=n[i+1],i++),t.compare(s)<0)break;n[i]=t,n[e]=s,e=i}}class Vi{constructor(e,t,i){this.minPoint=i,this.active=[],this.activeTo=[],this.activeRank=[],this.minActive=-1,this.point=null,this.pointFrom=0,this.pointRank=0,this.to=-1e9,this.endSide=0,this.openStart=-1,this.cursor=en.from(e,t,i)}goto(e,t=-1e9){return this.cursor.goto(e,t),this.active.length=this.activeTo.length=this.activeRank.length=0,this.minActive=-1,this.to=e,this.endSide=t,this.openStart=-1,this.next(),this}forward(e,t){for(;this.minActive>-1&&(this.activeTo[this.minActive]-e||this.active[this.minActive].endSide-t)<0;)this.removeActive(this.minActive);this.cursor.forward(e,t)}removeActive(e){An(this.active,e),An(this.activeTo,e),An(this.activeRank,e),this.minActive=Wl(this.active,this.activeTo)}addActive(e){let t=0,{value:i,to:s,rank:r}=this.cursor;for(;t<this.activeRank.length&&(r-this.activeRank[t]||s-this.activeTo[t])>0;)t++;Tn(this.active,t,i),Tn(this.activeTo,t,s),Tn(this.activeRank,t,r),e&&Tn(e,t,this.cursor.from),this.minActive=Wl(this.active,this.activeTo)}next(){let e=this.to,t=this.point;this.point=null;let i=this.openStart<0?[]:null;for(;;){let s=this.minActive;if(s>-1&&(this.activeTo[s]-this.cursor.from||this.active[s].endSide-this.cursor.startSide)<0){if(this.activeTo[s]>e){this.to=this.activeTo[s],this.endSide=this.active[s].endSide;break}this.removeActive(s),i&&An(i,s)}else if(this.cursor.value)if(this.cursor.from>e){this.to=this.cursor.from,this.endSide=this.cursor.startSide;break}else{let r=this.cursor.value;if(!r.point)this.addActive(i),this.cursor.next();else if(t&&this.cursor.to==this.to&&this.cursor.from<this.cursor.to)this.cursor.next();else{this.point=r,this.pointFrom=this.cursor.from,this.pointRank=this.cursor.rank,this.to=this.cursor.to,this.endSide=r.endSide,this.cursor.next(),this.forward(this.to,this.endSide);break}}else{this.to=this.endSide=1e9;break}}if(i){this.openStart=0;for(let s=i.length-1;s>=0&&i[s]<e;s--)this.openStart++}}activeForPoint(e){if(!this.active.length)return this.active;let t=[];for(let i=this.active.length-1;i>=0&&!(this.activeRank[i]<this.pointRank);i--)(this.activeTo[i]>e||this.activeTo[i]==e&&this.active[i].endSide>=this.point.endSide)&&t.push(this.active[i]);return t.reverse()}openEnd(e){let t=0;for(let i=this.activeTo.length-1;i>=0&&this.activeTo[i]>e;i--)t++;return t}}function Vl(n,e,t,i,s,r){n.goto(e),t.goto(i);let o=i+s,l=i,a=i-e;for(;;){let h=n.to+a-t.to,c=h||n.endSide-t.endSide,f=c<0?n.to+a:t.to,u=Math.min(f,o);if(n.point||t.point?n.point&&t.point&&(n.point==t.point||n.point.eq(t.point))&&Qr(n.activeForPoint(n.to),t.activeForPoint(t.to))||r.comparePoint(l,u,n.point,t.point):u>l&&!Qr(n.active,t.active)&&r.compareRange(l,u,n.active,t.active),f>o)break;(h||n.openEnd!=t.openEnd)&&r.boundChange&&r.boundChange(f),l=f,c<=0&&n.next(),c>=0&&t.next()}}function Qr(n,e){if(n.length!=e.length)return!1;for(let t=0;t<n.length;t++)if(n[t]!=e[t]&&!n[t].eq(e[t]))return!1;return!0}function An(n,e){for(let t=e,i=n.length-1;t<i;t++)n[t]=n[t+1];n.pop()}function Tn(n,e,t){for(let i=n.length-1;i>=e;i--)n[i+1]=n[i];n[e]=t}function Wl(n,e){let t=-1,i=1e9;for(let s=0;s<e.length;s++)(e[s]-i||n[s].endSide-n[t].endSide)<0&&(t=s,i=e[s]);return t}function Ni(n,e,t=n.length){let i=0;for(let s=0;s<t&&s<n.length;)n.charCodeAt(s)==9?(i+=e-i%e,s++):(i++,s=Se(n,s));return i}function Yr(n,e,t,i){for(let s=0,r=0;;){if(r>=e)return s;if(s==n.length)break;r+=n.charCodeAt(s)==9?t-r%t:1,s=Se(n,s)}return i===!0?-1:n.length}const Jr="ͼ",_l=typeof Symbol>"u"?"__"+Jr:Symbol.for(Jr),Zr=typeof Symbol>"u"?"__styleSet"+Math.floor(Math.random()*1e8):Symbol("styleSet"),$l=typeof globalThis<"u"?globalThis:typeof window<"u"?window:{};class $t{constructor(e,t){this.rules=[];let{finish:i}=t||{};function s(o){return/^@/.test(o)?[o]:o.split(/,\s*/)}function r(o,l,a,h){let c=[],f=/^@(\w+)\b/.exec(o[0]),u=f&&f[1]=="keyframes";if(f&&l==null)return a.push(o[0]+";");for(let d in l){let m=l[d];if(/&/.test(d))r(d.split(/,\s*/).map(x=>o.map(S=>x.replace(/&/,S))).reduce((x,S)=>x.concat(S)),m,a);else if(m&&typeof m=="object"){if(!f)throw new RangeError("The value of a property ("+d+") should be a primitive value.");r(s(d),m,c,u)}else m!=null&&c.push(d.replace(/_.*/,"").replace(/[A-Z]/g,x=>"-"+x.toLowerCase())+": "+m+";")}(c.length||u)&&a.push((i&&!f&&!h?o.map(i):o).join(", ")+" {"+c.join(" ")+"}")}for(let o in e)r(s(o),e[o],this.rules)}getRules(){return this.rules.join(`
`)}static newName(){let e=$l[_l]||1;return $l[_l]=e+1,Jr+e.toString(36)}static mount(e,t,i){let s=e[Zr],r=i&&i.nonce;s?r&&s.setNonce(r):s=new Hd(e,r),s.mount(Array.isArray(t)?t:[t],e)}}let ql=new Map;class Hd{constructor(e,t){let i=e.ownerDocument||e,s=i.defaultView;if(!e.head&&e.adoptedStyleSheets&&s.CSSStyleSheet){let r=ql.get(i);if(r)return e[Zr]=r;this.sheet=new s.CSSStyleSheet,ql.set(i,this)}else this.styleTag=i.createElement("style"),t&&this.styleTag.setAttribute("nonce",t);this.modules=[],e[Zr]=this}mount(e,t){let i=this.sheet,s=0,r=0;for(let o=0;o<e.length;o++){let l=e[o],a=this.modules.indexOf(l);if(a<r&&a>-1&&(this.modules.splice(a,1),r--,a=-1),a==-1){if(this.modules.splice(r++,0,l),i)for(let h=0;h<l.rules.length;h++)i.insertRule(l.rules[h],s++)}else{for(;r<a;)s+=this.modules[r++].rules.length;s+=l.rules.length,r++}}if(i)t.adoptedStyleSheets.indexOf(this.sheet)<0&&(t.adoptedStyleSheets=[this.sheet,...t.adoptedStyleSheets]);else{let o="";for(let a=0;a<this.modules.length;a++)o+=this.modules[a].getRules()+`
`;this.styleTag.textContent=o;let l=t.head||t;this.styleTag.parentNode!=l&&l.insertBefore(this.styleTag,l.firstChild)}}setNonce(e){this.styleTag&&this.styleTag.getAttribute("nonce")!=e&&this.styleTag.setAttribute("nonce",e)}}var qt={8:"Backspace",9:"Tab",10:"Enter",12:"NumLock",13:"Enter",16:"Shift",17:"Control",18:"Alt",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",44:"PrintScreen",45:"Insert",46:"Delete",59:";",61:"=",91:"Meta",92:"Meta",106:"*",107:"+",108:",",109:"-",110:".",111:"/",144:"NumLock",145:"ScrollLock",160:"Shift",161:"Shift",162:"Control",163:"Control",164:"Alt",165:"Alt",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'"},tn={48:")",49:"!",50:"@",51:"#",52:"$",53:"%",54:"^",55:"&",56:"*",57:"(",59:":",61:"+",173:"_",186:":",187:"+",188:"<",189:"_",190:">",191:"?",192:"~",219:"{",220:"|",221:"}",222:'"'},zd=typeof navigator<"u"&&/Mac/.test(navigator.platform),jd=typeof navigator<"u"&&/MSIE \d|Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(navigator.userAgent);for(var we=0;we<10;we++)qt[48+we]=qt[96+we]=String(we);for(var we=1;we<=24;we++)qt[we+111]="F"+we;for(var we=65;we<=90;we++)qt[we]=String.fromCharCode(we+32),tn[we]=String.fromCharCode(we);for(var tr in qt)tn.hasOwnProperty(tr)||(tn[tr]=qt[tr]);function Kd(n){var e=zd&&n.metaKey&&n.shiftKey&&!n.ctrlKey&&!n.altKey||jd&&n.shiftKey&&n.key&&n.key.length==1||n.key=="Unidentified",t=!e&&n.key||(n.shiftKey?tn:qt)[n.keyCode]||n.key||"Unidentified";return t=="Esc"&&(t="Escape"),t=="Del"&&(t="Delete"),t=="Left"&&(t="ArrowLeft"),t=="Up"&&(t="ArrowUp"),t=="Right"&&(t="ArrowRight"),t=="Down"&&(t="ArrowDown"),t}function Q(){var n=arguments[0];typeof n=="string"&&(n=document.createElement(n));var e=1,t=arguments[1];if(t&&typeof t=="object"&&t.nodeType==null&&!Array.isArray(t)){for(var i in t)if(Object.prototype.hasOwnProperty.call(t,i)){var s=t[i];typeof s=="string"?n.setAttribute(i,s):s!=null&&(n[i]=s)}e++}for(;e<arguments.length;e++)dc(n,arguments[e]);return n}function dc(n,e){if(typeof e=="string")n.appendChild(document.createTextNode(e));else if(e!=null)if(e.nodeType!=null)n.appendChild(e);else if(Array.isArray(e))for(var t=0;t<e.length;t++)dc(n,e[t]);else throw new RangeError("Unsupported child node: "+e)}function nn(n){let e;return n.nodeType==11?e=n.getSelection?n:n.ownerDocument:e=n,e.getSelection()}function eo(n,e){return e?n==e||n.contains(e.nodeType!=1?e.parentNode:e):!1}function Jn(n,e){if(!e.anchorNode)return!1;try{return eo(n,e.anchorNode)}catch{return!1}}function Ci(n){return n.nodeType==3?si(n,0,n.nodeValue.length).getClientRects():n.nodeType==1?n.getClientRects():[]}function Xi(n,e,t,i){return t?Hl(n,e,t,i,-1)||Hl(n,e,t,i,1):!1}function ni(n){for(var e=0;;e++)if(n=n.previousSibling,!n)return e}function ds(n){return n.nodeType==1&&/^(DIV|P|LI|UL|OL|BLOCKQUOTE|DD|DT|H\d|SECTION|PRE)$/.test(n.nodeName)}function Hl(n,e,t,i,s){for(;;){if(n==t&&e==i)return!0;if(e==(s<0?0:yt(n))){if(n.nodeName=="DIV")return!1;let r=n.parentNode;if(!r||r.nodeType!=1)return!1;e=ni(n)+(s<0?0:1),n=r}else if(n.nodeType==1){if(n=n.childNodes[e+(s<0?-1:0)],n.nodeType==1&&n.contentEditable=="false")return!1;e=s<0?yt(n):0}else return!1}}function yt(n){return n.nodeType==3?n.nodeValue.length:n.childNodes.length}function yn(n,e){let t=e?n.left:n.right;return{left:t,right:t,top:n.top,bottom:n.bottom}}function Ud(n){let e=n.visualViewport;return e?{left:0,right:e.width,top:0,bottom:e.height}:{left:0,right:n.innerWidth,top:0,bottom:n.innerHeight}}function pc(n,e){let t=e.width/n.offsetWidth,i=e.height/n.offsetHeight;return(t>.995&&t<1.005||!isFinite(t)||Math.abs(e.width-n.offsetWidth)<1)&&(t=1),(i>.995&&i<1.005||!isFinite(i)||Math.abs(e.height-n.offsetHeight)<1)&&(i=1),{scaleX:t,scaleY:i}}function Gd(n,e,t,i,s,r,o,l){let a=n.ownerDocument,h=a.defaultView||window;for(let c=n,f=!1;c&&!f;)if(c.nodeType==1){let u,d=c==a.body,m=1,x=1;if(d)u=Ud(h);else{if(/^(fixed|sticky)$/.test(getComputedStyle(c).position)&&(f=!0),c.scrollHeight<=c.clientHeight&&c.scrollWidth<=c.clientWidth){c=c.assignedSlot||c.parentNode;continue}let O=c.getBoundingClientRect();({scaleX:m,scaleY:x}=pc(c,O)),u={left:O.left,right:O.left+c.clientWidth*m,top:O.top,bottom:O.top+c.clientHeight*x}}let S=0,y=0;if(s=="nearest")e.top<u.top?(y=e.top-(u.top+o),t>0&&e.bottom>u.bottom+y&&(y=e.bottom-u.bottom+o)):e.bottom>u.bottom&&(y=e.bottom-u.bottom+o,t<0&&e.top-y<u.top&&(y=e.top-(u.top+o)));else{let O=e.bottom-e.top,v=u.bottom-u.top;y=(s=="center"&&O<=v?e.top+O/2-v/2:s=="start"||s=="center"&&t<0?e.top-o:e.bottom-v+o)-u.top}if(i=="nearest"?e.left<u.left?(S=e.left-(u.left+r),t>0&&e.right>u.right+S&&(S=e.right-u.right+r)):e.right>u.right&&(S=e.right-u.right+r,t<0&&e.left<u.left+S&&(S=e.left-(u.left+r))):S=(i=="center"?e.left+(e.right-e.left)/2-(u.right-u.left)/2:i=="start"==l?e.left-r:e.right-(u.right-u.left)+r)-u.left,S||y)if(d)h.scrollBy(S,y);else{let O=0,v=0;if(y){let g=c.scrollTop;c.scrollTop+=y/x,v=(c.scrollTop-g)*x}if(S){let g=c.scrollLeft;c.scrollLeft+=S/m,O=(c.scrollLeft-g)*m}e={left:e.left-O,top:e.top-v,right:e.right-O,bottom:e.bottom-v},O&&Math.abs(O-S)<1&&(i="nearest"),v&&Math.abs(v-y)<1&&(s="nearest")}if(d)break;(e.top<u.top||e.bottom>u.bottom||e.left<u.left||e.right>u.right)&&(e={left:Math.max(e.left,u.left),right:Math.min(e.right,u.right),top:Math.max(e.top,u.top),bottom:Math.min(e.bottom,u.bottom)}),c=c.assignedSlot||c.parentNode}else if(c.nodeType==11)c=c.host;else break}function Xd(n){let e=n.ownerDocument,t,i;for(let s=n.parentNode;s&&!(s==e.body||t&&i);)if(s.nodeType==1)!i&&s.scrollHeight>s.clientHeight&&(i=s),!t&&s.scrollWidth>s.clientWidth&&(t=s),s=s.assignedSlot||s.parentNode;else if(s.nodeType==11)s=s.host;else break;return{x:t,y:i}}class Qd{constructor(){this.anchorNode=null,this.anchorOffset=0,this.focusNode=null,this.focusOffset=0}eq(e){return this.anchorNode==e.anchorNode&&this.anchorOffset==e.anchorOffset&&this.focusNode==e.focusNode&&this.focusOffset==e.focusOffset}setRange(e){let{anchorNode:t,focusNode:i}=e;this.set(t,Math.min(e.anchorOffset,t?yt(t):0),i,Math.min(e.focusOffset,i?yt(i):0))}set(e,t,i,s){this.anchorNode=e,this.anchorOffset=t,this.focusNode=i,this.focusOffset=s}}let ci=null;function mc(n){if(n.setActive)return n.setActive();if(ci)return n.focus(ci);let e=[];for(let t=n;t&&(e.push(t,t.scrollTop,t.scrollLeft),t!=t.ownerDocument);t=t.parentNode);if(n.focus(ci==null?{get preventScroll(){return ci={preventScroll:!0},!0}}:void 0),!ci){ci=!1;for(let t=0;t<e.length;){let i=e[t++],s=e[t++],r=e[t++];i.scrollTop!=s&&(i.scrollTop=s),i.scrollLeft!=r&&(i.scrollLeft=r)}}}let zl;function si(n,e,t=e){let i=zl||(zl=document.createRange());return i.setEnd(n,t),i.setStart(n,e),i}function xi(n,e,t,i){let s={key:e,code:e,keyCode:t,which:t,cancelable:!0};i&&({altKey:s.altKey,ctrlKey:s.ctrlKey,shiftKey:s.shiftKey,metaKey:s.metaKey}=i);let r=new KeyboardEvent("keydown",s);r.synthetic=!0,n.dispatchEvent(r);let o=new KeyboardEvent("keyup",s);return o.synthetic=!0,n.dispatchEvent(o),r.defaultPrevented||o.defaultPrevented}function Yd(n){for(;n;){if(n&&(n.nodeType==9||n.nodeType==11&&n.host))return n;n=n.assignedSlot||n.parentNode}return null}function gc(n){for(;n.attributes.length;)n.removeAttributeNode(n.attributes[0])}function Jd(n,e){let t=e.focusNode,i=e.focusOffset;if(!t||e.anchorNode!=t||e.anchorOffset!=i)return!1;for(i=Math.min(i,yt(t));;)if(i){if(t.nodeType!=1)return!1;let s=t.childNodes[i-1];s.contentEditable=="false"?i--:(t=s,i=yt(t))}else{if(t==n)return!0;i=ni(t),t=t.parentNode}}function yc(n){return n.scrollTop>Math.max(1,n.scrollHeight-n.clientHeight-4)}function bc(n,e){for(let t=n,i=e;;){if(t.nodeType==3&&i>0)return{node:t,offset:i};if(t.nodeType==1&&i>0){if(t.contentEditable=="false")return null;t=t.childNodes[i-1],i=yt(t)}else if(t.parentNode&&!ds(t))i=ni(t),t=t.parentNode;else return null}}function xc(n,e){for(let t=n,i=e;;){if(t.nodeType==3&&i<t.nodeValue.length)return{node:t,offset:i};if(t.nodeType==1&&i<t.childNodes.length){if(t.contentEditable=="false")return null;t=t.childNodes[i],i=0}else if(t.parentNode&&!ds(t))i=ni(t)+1,t=t.parentNode;else return null}}class Ae{constructor(e,t,i=!0){this.node=e,this.offset=t,this.precise=i}static before(e,t){return new Ae(e.parentNode,ni(e),t)}static after(e,t){return new Ae(e.parentNode,ni(e)+1,t)}}const Ko=[];class J{constructor(){this.parent=null,this.dom=null,this.flags=2}get overrideDOMText(){return null}get posAtStart(){return this.parent?this.parent.posBefore(this):0}get posAtEnd(){return this.posAtStart+this.length}posBefore(e){let t=this.posAtStart;for(let i of this.children){if(i==e)return t;t+=i.length+i.breakAfter}throw new RangeError("Invalid child in posBefore")}posAfter(e){return this.posBefore(e)+e.length}sync(e,t){if(this.flags&2){let i=this.dom,s=null,r;for(let o of this.children){if(o.flags&7){if(!o.dom&&(r=s?s.nextSibling:i.firstChild)){let l=J.get(r);(!l||!l.parent&&l.canReuseDOM(o))&&o.reuseDOM(r)}o.sync(e,t),o.flags&=-8}if(r=s?s.nextSibling:i.firstChild,t&&!t.written&&t.node==i&&r!=o.dom&&(t.written=!0),o.dom.parentNode==i)for(;r&&r!=o.dom;)r=jl(r);else i.insertBefore(o.dom,r);s=o.dom}for(r=s?s.nextSibling:i.firstChild,r&&t&&t.node==i&&(t.written=!0);r;)r=jl(r)}else if(this.flags&1)for(let i of this.children)i.flags&7&&(i.sync(e,t),i.flags&=-8)}reuseDOM(e){}localPosFromDOM(e,t){let i;if(e==this.dom)i=this.dom.childNodes[t];else{let s=yt(e)==0?0:t==0?-1:1;for(;;){let r=e.parentNode;if(r==this.dom)break;s==0&&r.firstChild!=r.lastChild&&(e==r.firstChild?s=-1:s=1),e=r}s<0?i=e:i=e.nextSibling}if(i==this.dom.firstChild)return 0;for(;i&&!J.get(i);)i=i.nextSibling;if(!i)return this.length;for(let s=0,r=0;;s++){let o=this.children[s];if(o.dom==i)return r;r+=o.length+o.breakAfter}}domBoundsAround(e,t,i=0){let s=-1,r=-1,o=-1,l=-1;for(let a=0,h=i,c=i;a<this.children.length;a++){let f=this.children[a],u=h+f.length;if(h<e&&u>t)return f.domBoundsAround(e,t,h);if(u>=e&&s==-1&&(s=a,r=h),h>t&&f.dom.parentNode==this.dom){o=a,l=c;break}c=u,h=u+f.breakAfter}return{from:r,to:l<0?i+this.length:l,startDOM:(s?this.children[s-1].dom.nextSibling:null)||this.dom.firstChild,endDOM:o<this.children.length&&o>=0?this.children[o].dom:null}}markDirty(e=!1){this.flags|=2,this.markParentsDirty(e)}markParentsDirty(e){for(let t=this.parent;t;t=t.parent){if(e&&(t.flags|=2),t.flags&1)return;t.flags|=1,e=!1}}setParent(e){this.parent!=e&&(this.parent=e,this.flags&7&&this.markParentsDirty(!0))}setDOM(e){this.dom!=e&&(this.dom&&(this.dom.cmView=null),this.dom=e,e.cmView=this)}get rootView(){for(let e=this;;){let t=e.parent;if(!t)return e;e=t}}replaceChildren(e,t,i=Ko){this.markDirty();for(let s=e;s<t;s++){let r=this.children[s];r.parent==this&&i.indexOf(r)<0&&r.destroy()}i.length<250?this.children.splice(e,t-e,...i):this.children=[].concat(this.children.slice(0,e),i,this.children.slice(t));for(let s=0;s<i.length;s++)i[s].setParent(this)}ignoreMutation(e){return!1}ignoreEvent(e){return!1}childCursor(e=this.length){return new vc(this.children,e,this.children.length)}childPos(e,t=1){return this.childCursor().findPos(e,t)}toString(){let e=this.constructor.name.replace("View","");return e+(this.children.length?"("+this.children.join()+")":this.length?"["+(e=="Text"?this.text:this.length)+"]":"")+(this.breakAfter?"#":"")}static get(e){return e.cmView}get isEditable(){return!0}get isWidget(){return!1}get isHidden(){return!1}merge(e,t,i,s,r,o){return!1}become(e){return!1}canReuseDOM(e){return e.constructor==this.constructor&&!((this.flags|e.flags)&8)}getSide(){return 0}destroy(){for(let e of this.children)e.parent==this&&e.destroy();this.parent=null}}J.prototype.breakAfter=0;function jl(n){let e=n.nextSibling;return n.parentNode.removeChild(n),e}class vc{constructor(e,t,i){this.children=e,this.pos=t,this.i=i,this.off=0}findPos(e,t=1){for(;;){if(e>this.pos||e==this.pos&&(t>0||this.i==0||this.children[this.i-1].breakAfter))return this.off=e-this.pos,this;let i=this.children[--this.i];this.pos-=i.length+i.breakAfter}}}function wc(n,e,t,i,s,r,o,l,a){let{children:h}=n,c=h.length?h[e]:null,f=r.length?r[r.length-1]:null,u=f?f.breakAfter:o;if(!(e==i&&c&&!o&&!u&&r.length<2&&c.merge(t,s,r.length?f:null,t==0,l,a))){if(i<h.length){let d=h[i];d&&(s<d.length||d.breakAfter&&(f!=null&&f.breakAfter))?(e==i&&(d=d.split(s),s=0),!u&&f&&d.merge(0,s,f,!0,0,a)?r[r.length-1]=d:((s||d.children.length&&!d.children[0].length)&&d.merge(0,s,null,!1,0,a),r.push(d))):d!=null&&d.breakAfter&&(f?f.breakAfter=1:o=1),i++}for(c&&(c.breakAfter=o,t>0&&(!o&&r.length&&c.merge(t,c.length,r[0],!1,l,0)?c.breakAfter=r.shift().breakAfter:(t<c.length||c.children.length&&c.children[c.children.length-1].length==0)&&c.merge(t,c.length,null,!1,l,0),e++));e<i&&r.length;)if(h[i-1].become(r[r.length-1]))i--,r.pop(),a=r.length?0:l;else if(h[e].become(r[0]))e++,r.shift(),l=r.length?0:a;else break;!r.length&&e&&i<h.length&&!h[e-1].breakAfter&&h[i].merge(0,0,h[e-1],!1,l,a)&&e--,(e<i||r.length)&&n.replaceChildren(e,i,r)}}function Sc(n,e,t,i,s,r){let o=n.childCursor(),{i:l,off:a}=o.findPos(t,1),{i:h,off:c}=o.findPos(e,-1),f=e-t;for(let u of i)f+=u.length;n.length+=f,wc(n,h,c,l,a,i,0,s,r)}let Le=typeof navigator<"u"?navigator:{userAgent:"",vendor:"",platform:""},to=typeof document<"u"?document:{documentElement:{style:{}}};const io=/Edge\/(\d+)/.exec(Le.userAgent),Oc=/MSIE \d/.test(Le.userAgent),no=/Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(Le.userAgent),Ls=!!(Oc||no||io),Kl=!Ls&&/gecko\/(\d+)/i.test(Le.userAgent),ir=!Ls&&/Chrome\/(\d+)/.exec(Le.userAgent),Zd="webkitFontSmoothing"in to.documentElement.style,kc=!Ls&&/Apple Computer/.test(Le.vendor),Ul=kc&&(/Mobile\/\w+/.test(Le.userAgent)||Le.maxTouchPoints>2);var L={mac:Ul||/Mac/.test(Le.platform),windows:/Win/.test(Le.platform),linux:/Linux|X11/.test(Le.platform),ie:Ls,ie_version:Oc?to.documentMode||6:no?+no[1]:io?+io[1]:0,gecko:Kl,gecko_version:Kl?+(/Firefox\/(\d+)/.exec(Le.userAgent)||[0,0])[1]:0,chrome:!!ir,chrome_version:ir?+ir[1]:0,ios:Ul,android:/Android\b/.test(Le.userAgent),safari:kc,webkit_version:Zd?+(/\bAppleWebKit\/(\d+)/.exec(Le.userAgent)||[0,0])[1]:0,tabSize:to.documentElement.style.tabSize!=null?"tab-size":"-moz-tab-size"};const ep=256;class tt extends J{constructor(e){super(),this.text=e}get length(){return this.text.length}createDOM(e){this.setDOM(e||document.createTextNode(this.text))}sync(e,t){this.dom||this.createDOM(),this.dom.nodeValue!=this.text&&(t&&t.node==this.dom&&(t.written=!0),this.dom.nodeValue=this.text)}reuseDOM(e){e.nodeType==3&&this.createDOM(e)}merge(e,t,i){return this.flags&8||i&&(!(i instanceof tt)||this.length-(t-e)+i.length>ep||i.flags&8)?!1:(this.text=this.text.slice(0,e)+(i?i.text:"")+this.text.slice(t),this.markDirty(),!0)}split(e){let t=new tt(this.text.slice(e));return this.text=this.text.slice(0,e),this.markDirty(),t.flags|=this.flags&8,t}localPosFromDOM(e,t){return e==this.dom?t:t?this.text.length:0}domAtPos(e){return new Ae(this.dom,e)}domBoundsAround(e,t,i){return{from:i,to:i+this.length,startDOM:this.dom,endDOM:this.dom.nextSibling}}coordsAt(e,t){return tp(this.dom,e,t)}}class Tt extends J{constructor(e,t=[],i=0){super(),this.mark=e,this.children=t,this.length=i;for(let s of t)s.setParent(this)}setAttrs(e){if(gc(e),this.mark.class&&(e.className=this.mark.class),this.mark.attrs)for(let t in this.mark.attrs)e.setAttribute(t,this.mark.attrs[t]);return e}canReuseDOM(e){return super.canReuseDOM(e)&&!((this.flags|e.flags)&8)}reuseDOM(e){e.nodeName==this.mark.tagName.toUpperCase()&&(this.setDOM(e),this.flags|=6)}sync(e,t){this.dom?this.flags&4&&this.setAttrs(this.dom):this.setDOM(this.setAttrs(document.createElement(this.mark.tagName))),super.sync(e,t)}merge(e,t,i,s,r,o){return i&&(!(i instanceof Tt&&i.mark.eq(this.mark))||e&&r<=0||t<this.length&&o<=0)?!1:(Sc(this,e,t,i?i.children.slice():[],r-1,o-1),this.markDirty(),!0)}split(e){let t=[],i=0,s=-1,r=0;for(let l of this.children){let a=i+l.length;a>e&&t.push(i<e?l.split(e-i):l),s<0&&i>=e&&(s=r),i=a,r++}let o=this.length-e;return this.length=e,s>-1&&(this.children.length=s,this.markDirty()),new Tt(this.mark,t,o)}domAtPos(e){return Cc(this,e)}coordsAt(e,t){return Tc(this,e,t)}}function tp(n,e,t){let i=n.nodeValue.length;e>i&&(e=i);let s=e,r=e,o=0;e==0&&t<0||e==i&&t>=0?L.chrome||L.gecko||(e?(s--,o=1):r<i&&(r++,o=-1)):t<0?s--:r<i&&r++;let l=si(n,s,r).getClientRects();if(!l.length)return null;let a=l[(o?o<0:t>=0)?0:l.length-1];return L.safari&&!o&&a.width==0&&(a=Array.prototype.find.call(l,h=>h.width)||a),o?yn(a,o<0):a||null}class Ft extends J{static create(e,t,i){return new Ft(e,t,i)}constructor(e,t,i){super(),this.widget=e,this.length=t,this.side=i,this.prevWidget=null}split(e){let t=Ft.create(this.widget,this.length-e,this.side);return this.length-=e,t}sync(e){(!this.dom||!this.widget.updateDOM(this.dom,e))&&(this.dom&&this.prevWidget&&this.prevWidget.destroy(this.dom),this.prevWidget=null,this.setDOM(this.widget.toDOM(e)),this.widget.editable||(this.dom.contentEditable="false"))}getSide(){return this.side}merge(e,t,i,s,r,o){return i&&(!(i instanceof Ft)||!this.widget.compare(i.widget)||e>0&&r<=0||t<this.length&&o<=0)?!1:(this.length=e+(i?i.length:0)+(this.length-t),!0)}become(e){return e instanceof Ft&&e.side==this.side&&this.widget.constructor==e.widget.constructor?(this.widget.compare(e.widget)||this.markDirty(!0),this.dom&&!this.prevWidget&&(this.prevWidget=this.widget),this.widget=e.widget,this.length=e.length,!0):!1}ignoreMutation(){return!0}ignoreEvent(e){return this.widget.ignoreEvent(e)}get overrideDOMText(){if(this.length==0)return G.empty;let e=this;for(;e.parent;)e=e.parent;let{view:t}=e,i=t&&t.state.doc,s=this.posAtStart;return i?i.slice(s,s+this.length):G.empty}domAtPos(e){return(this.length?e==0:this.side>0)?Ae.before(this.dom):Ae.after(this.dom,e==this.length)}domBoundsAround(){return null}coordsAt(e,t){let i=this.widget.coordsAt(this.dom,e,t);if(i)return i;let s=this.dom.getClientRects(),r=null;if(!s.length)return null;let o=this.side?this.side<0:e>0;for(let l=o?s.length-1:0;r=s[l],!(e>0?l==0:l==s.length-1||r.top<r.bottom);l+=o?-1:1);return yn(r,!o)}get isEditable(){return!1}get isWidget(){return!0}get isHidden(){return this.widget.isHidden}destroy(){super.destroy(),this.dom&&this.widget.destroy(this.dom)}}class Ai extends J{constructor(e){super(),this.side=e}get length(){return 0}merge(){return!1}become(e){return e instanceof Ai&&e.side==this.side}split(){return new Ai(this.side)}sync(){if(!this.dom){let e=document.createElement("img");e.className="cm-widgetBuffer",e.setAttribute("aria-hidden","true"),this.setDOM(e)}}getSide(){return this.side}domAtPos(e){return this.side>0?Ae.before(this.dom):Ae.after(this.dom)}localPosFromDOM(){return 0}domBoundsAround(){return null}coordsAt(e){return this.dom.getBoundingClientRect()}get overrideDOMText(){return G.empty}get isHidden(){return!0}}tt.prototype.children=Ft.prototype.children=Ai.prototype.children=Ko;function Cc(n,e){let t=n.dom,{children:i}=n,s=0;for(let r=0;s<i.length;s++){let o=i[s],l=r+o.length;if(!(l==r&&o.getSide()<=0)){if(e>r&&e<l&&o.dom.parentNode==t)return o.domAtPos(e-r);if(e<=r)break;r=l}}for(let r=s;r>0;r--){let o=i[r-1];if(o.dom.parentNode==t)return o.domAtPos(o.length)}for(let r=s;r<i.length;r++){let o=i[r];if(o.dom.parentNode==t)return o.domAtPos(0)}return new Ae(t,0)}function Ac(n,e,t){let i,{children:s}=n;t>0&&e instanceof Tt&&s.length&&(i=s[s.length-1])instanceof Tt&&i.mark.eq(e.mark)?Ac(i,e.children[0],t-1):(s.push(e),e.setParent(n)),n.length+=e.length}function Tc(n,e,t){let i=null,s=-1,r=null,o=-1;function l(h,c){for(let f=0,u=0;f<h.children.length&&u<=c;f++){let d=h.children[f],m=u+d.length;m>=c&&(d.children.length?l(d,c-u):(!r||r.isHidden&&(t>0||np(r,d)))&&(m>c||u==m&&d.getSide()>0)?(r=d,o=c-u):(u<c||u==m&&d.getSide()<0&&!d.isHidden)&&(i=d,s=c-u)),u=m}}l(n,e);let a=(t<0?i:r)||i||r;return a?a.coordsAt(Math.max(0,a==i?s:o),t):ip(n)}function ip(n){let e=n.dom.lastChild;if(!e)return n.dom.getBoundingClientRect();let t=Ci(e);return t[t.length-1]||null}function np(n,e){let t=n.coordsAt(0,1),i=e.coordsAt(0,1);return t&&i&&i.top<t.bottom}function so(n,e){for(let t in n)t=="class"&&e.class?e.class+=" "+n.class:t=="style"&&e.style?e.style+=";"+n.style:e[t]=n[t];return e}const Gl=Object.create(null);function ps(n,e,t){if(n==e)return!0;n||(n=Gl),e||(e=Gl);let i=Object.keys(n),s=Object.keys(e);if(i.length-(t&&i.indexOf(t)>-1?1:0)!=s.length-(t&&s.indexOf(t)>-1?1:0))return!1;for(let r of i)if(r!=t&&(s.indexOf(r)==-1||n[r]!==e[r]))return!1;return!0}function ro(n,e,t){let i=!1;if(e)for(let s in e)t&&s in t||(i=!0,s=="style"?n.style.cssText="":n.removeAttribute(s));if(t)for(let s in t)e&&e[s]==t[s]||(i=!0,s=="style"?n.style.cssText=t[s]:n.setAttribute(s,t[s]));return i}function sp(n){let e=Object.create(null);for(let t=0;t<n.attributes.length;t++){let i=n.attributes[t];e[i.name]=i.value}return e}class Et{eq(e){return!1}updateDOM(e,t){return!1}compare(e){return this==e||this.constructor==e.constructor&&this.eq(e)}get estimatedHeight(){return-1}get lineBreaks(){return 0}ignoreEvent(e){return!0}coordsAt(e,t,i){return null}get isHidden(){return!1}get editable(){return!1}destroy(e){}}var Pe=function(n){return n[n.Text=0]="Text",n[n.WidgetBefore=1]="WidgetBefore",n[n.WidgetAfter=2]="WidgetAfter",n[n.WidgetRange=3]="WidgetRange",n}(Pe||(Pe={}));class W extends ii{constructor(e,t,i,s){super(),this.startSide=e,this.endSide=t,this.widget=i,this.spec=s}get heightRelevant(){return!1}static mark(e){return new bn(e)}static widget(e){let t=Math.max(-1e4,Math.min(1e4,e.side||0)),i=!!e.block;return t+=i&&!e.inlineOrder?t>0?3e8:-4e8:t>0?1e8:-1e8,new Ht(e,t,t,i,e.widget||null,!1)}static replace(e){let t=!!e.block,i,s;if(e.isBlockGap)i=-5e8,s=4e8;else{let{start:r,end:o}=Mc(e,t);i=(r?t?-3e8:-1:5e8)-1,s=(o?t?2e8:1:-6e8)+1}return new Ht(e,i,s,t,e.widget||null,!0)}static line(e){return new xn(e)}static set(e,t=!1){return U.of(e,t)}hasHeight(){return this.widget?this.widget.estimatedHeight>-1:!1}}W.none=U.empty;class bn extends W{constructor(e){let{start:t,end:i}=Mc(e);super(t?-1:5e8,i?1:-6e8,null,e),this.tagName=e.tagName||"span",this.class=e.class||"",this.attrs=e.attributes||null}eq(e){var t,i;return this==e||e instanceof bn&&this.tagName==e.tagName&&(this.class||((t=this.attrs)===null||t===void 0?void 0:t.class))==(e.class||((i=e.attrs)===null||i===void 0?void 0:i.class))&&ps(this.attrs,e.attrs,"class")}range(e,t=e){if(e>=t)throw new RangeError("Mark decorations may not be empty");return super.range(e,t)}}bn.prototype.point=!1;class xn extends W{constructor(e){super(-2e8,-2e8,null,e)}eq(e){return e instanceof xn&&this.spec.class==e.spec.class&&ps(this.spec.attributes,e.spec.attributes)}range(e,t=e){if(t!=e)throw new RangeError("Line decoration ranges must be zero-length");return super.range(e,t)}}xn.prototype.mapMode=Ee.TrackBefore;xn.prototype.point=!0;class Ht extends W{constructor(e,t,i,s,r,o){super(t,i,r,e),this.block=s,this.isReplace=o,this.mapMode=s?t<=0?Ee.TrackBefore:Ee.TrackAfter:Ee.TrackDel}get type(){return this.startSide!=this.endSide?Pe.WidgetRange:this.startSide<=0?Pe.WidgetBefore:Pe.WidgetAfter}get heightRelevant(){return this.block||!!this.widget&&(this.widget.estimatedHeight>=5||this.widget.lineBreaks>0)}eq(e){return e instanceof Ht&&rp(this.widget,e.widget)&&this.block==e.block&&this.startSide==e.startSide&&this.endSide==e.endSide}range(e,t=e){if(this.isReplace&&(e>t||e==t&&this.startSide>0&&this.endSide<=0))throw new RangeError("Invalid range for replacement decoration");if(!this.isReplace&&t!=e)throw new RangeError("Widget decorations can only have zero-length ranges");return super.range(e,t)}}Ht.prototype.point=!0;function Mc(n,e=!1){let{inclusiveStart:t,inclusiveEnd:i}=n;return t==null&&(t=n.inclusive),i==null&&(i=n.inclusive),{start:t??e,end:i??e}}function rp(n,e){return n==e||!!(n&&e&&n.compare(e))}function Zn(n,e,t,i=0){let s=t.length-1;s>=0&&t[s]+i>=n?t[s]=Math.max(t[s],e):t.push(n,e)}class oe extends J{constructor(){super(...arguments),this.children=[],this.length=0,this.prevAttrs=void 0,this.attrs=null,this.breakAfter=0}merge(e,t,i,s,r,o){if(i){if(!(i instanceof oe))return!1;this.dom||i.transferDOM(this)}return s&&this.setDeco(i?i.attrs:null),Sc(this,e,t,i?i.children.slice():[],r,o),!0}split(e){let t=new oe;if(t.breakAfter=this.breakAfter,this.length==0)return t;let{i,off:s}=this.childPos(e);s&&(t.append(this.children[i].split(s),0),this.children[i].merge(s,this.children[i].length,null,!1,0,0),i++);for(let r=i;r<this.children.length;r++)t.append(this.children[r],0);for(;i>0&&this.children[i-1].length==0;)this.children[--i].destroy();return this.children.length=i,this.markDirty(),this.length=e,t}transferDOM(e){this.dom&&(this.markDirty(),e.setDOM(this.dom),e.prevAttrs=this.prevAttrs===void 0?this.attrs:this.prevAttrs,this.prevAttrs=void 0,this.dom=null)}setDeco(e){ps(this.attrs,e)||(this.dom&&(this.prevAttrs=this.attrs,this.markDirty()),this.attrs=e)}append(e,t){Ac(this,e,t)}addLineDeco(e){let t=e.spec.attributes,i=e.spec.class;t&&(this.attrs=so(t,this.attrs||{})),i&&(this.attrs=so({class:i},this.attrs||{}))}domAtPos(e){return Cc(this,e)}reuseDOM(e){e.nodeName=="DIV"&&(this.setDOM(e),this.flags|=6)}sync(e,t){var i;this.dom?this.flags&4&&(gc(this.dom),this.dom.className="cm-line",this.prevAttrs=this.attrs?null:void 0):(this.setDOM(document.createElement("div")),this.dom.className="cm-line",this.prevAttrs=this.attrs?null:void 0),this.prevAttrs!==void 0&&(ro(this.dom,this.prevAttrs,this.attrs),this.dom.classList.add("cm-line"),this.prevAttrs=void 0),super.sync(e,t);let s=this.dom.lastChild;for(;s&&J.get(s)instanceof Tt;)s=s.lastChild;if(!s||!this.length||s.nodeName!="BR"&&((i=J.get(s))===null||i===void 0?void 0:i.isEditable)==!1&&(!L.ios||!this.children.some(r=>r instanceof tt))){let r=document.createElement("BR");r.cmIgnore=!0,this.dom.appendChild(r)}}measureTextSize(){if(this.children.length==0||this.length>20)return null;let e=0,t;for(let i of this.children){if(!(i instanceof tt)||/[^ -~]/.test(i.text))return null;let s=Ci(i.dom);if(s.length!=1)return null;e+=s[0].width,t=s[0].height}return e?{lineHeight:this.dom.getBoundingClientRect().height,charWidth:e/this.length,textHeight:t}:null}coordsAt(e,t){let i=Tc(this,e,t);if(!this.children.length&&i&&this.parent){let{heightOracle:s}=this.parent.view.viewState,r=i.bottom-i.top;if(Math.abs(r-s.lineHeight)<2&&s.textHeight<r){let o=(r-s.textHeight)/2;return{top:i.top+o,bottom:i.bottom-o,left:i.left,right:i.left}}}return i}become(e){return e instanceof oe&&this.children.length==0&&e.children.length==0&&ps(this.attrs,e.attrs)&&this.breakAfter==e.breakAfter}covers(){return!0}static find(e,t){for(let i=0,s=0;i<e.children.length;i++){let r=e.children[i],o=s+r.length;if(o>=t){if(r instanceof oe)return r;if(o>t)break}s=o+r.breakAfter}return null}}class Ct extends J{constructor(e,t,i){super(),this.widget=e,this.length=t,this.deco=i,this.breakAfter=0,this.prevWidget=null}merge(e,t,i,s,r,o){return i&&(!(i instanceof Ct)||!this.widget.compare(i.widget)||e>0&&r<=0||t<this.length&&o<=0)?!1:(this.length=e+(i?i.length:0)+(this.length-t),!0)}domAtPos(e){return e==0?Ae.before(this.dom):Ae.after(this.dom,e==this.length)}split(e){let t=this.length-e;this.length=e;let i=new Ct(this.widget,t,this.deco);return i.breakAfter=this.breakAfter,i}get children(){return Ko}sync(e){(!this.dom||!this.widget.updateDOM(this.dom,e))&&(this.dom&&this.prevWidget&&this.prevWidget.destroy(this.dom),this.prevWidget=null,this.setDOM(this.widget.toDOM(e)),this.widget.editable||(this.dom.contentEditable="false"))}get overrideDOMText(){return this.parent?this.parent.view.state.doc.slice(this.posAtStart,this.posAtEnd):G.empty}domBoundsAround(){return null}become(e){return e instanceof Ct&&e.widget.constructor==this.widget.constructor?(e.widget.compare(this.widget)||this.markDirty(!0),this.dom&&!this.prevWidget&&(this.prevWidget=this.widget),this.widget=e.widget,this.length=e.length,this.deco=e.deco,this.breakAfter=e.breakAfter,!0):!1}ignoreMutation(){return!0}ignoreEvent(e){return this.widget.ignoreEvent(e)}get isEditable(){return!1}get isWidget(){return!0}coordsAt(e,t){let i=this.widget.coordsAt(this.dom,e,t);return i||(this.widget instanceof oo?null:yn(this.dom.getBoundingClientRect(),this.length?e==0:t<=0))}destroy(){super.destroy(),this.dom&&this.widget.destroy(this.dom)}covers(e){let{startSide:t,endSide:i}=this.deco;return t==i?!1:e<0?t<0:i>0}}class oo extends Et{constructor(e){super(),this.height=e}toDOM(){let e=document.createElement("div");return e.className="cm-gap",this.updateDOM(e),e}eq(e){return e.height==this.height}updateDOM(e){return e.style.height=this.height+"px",!0}get editable(){return!0}get estimatedHeight(){return this.height}ignoreEvent(){return!1}}class Qi{constructor(e,t,i,s){this.doc=e,this.pos=t,this.end=i,this.disallowBlockEffectsFor=s,this.content=[],this.curLine=null,this.breakAtStart=0,this.pendingBuffer=0,this.bufferMarks=[],this.atCursorPos=!0,this.openStart=-1,this.openEnd=-1,this.text="",this.textOff=0,this.cursor=e.iter(),this.skip=t}posCovered(){if(this.content.length==0)return!this.breakAtStart&&this.doc.lineAt(this.pos).from!=this.pos;let e=this.content[this.content.length-1];return!(e.breakAfter||e instanceof Ct&&e.deco.endSide<0)}getLine(){return this.curLine||(this.content.push(this.curLine=new oe),this.atCursorPos=!0),this.curLine}flushBuffer(e=this.bufferMarks){this.pendingBuffer&&(this.curLine.append(Mn(new Ai(-1),e),e.length),this.pendingBuffer=0)}addBlockWidget(e){this.flushBuffer(),this.curLine=null,this.content.push(e)}finish(e){this.pendingBuffer&&e<=this.bufferMarks.length?this.flushBuffer():this.pendingBuffer=0,!this.posCovered()&&!(e&&this.content.length&&this.content[this.content.length-1]instanceof Ct)&&this.getLine()}buildText(e,t,i){for(;e>0;){if(this.textOff==this.text.length){let{value:r,lineBreak:o,done:l}=this.cursor.next(this.skip);if(this.skip=0,l)throw new Error("Ran out of text content when drawing inline views");if(o){this.posCovered()||this.getLine(),this.content.length?this.content[this.content.length-1].breakAfter=1:this.breakAtStart=1,this.flushBuffer(),this.curLine=null,this.atCursorPos=!0,e--;continue}else this.text=r,this.textOff=0}let s=Math.min(this.text.length-this.textOff,e,512);this.flushBuffer(t.slice(t.length-i)),this.getLine().append(Mn(new tt(this.text.slice(this.textOff,this.textOff+s)),t),i),this.atCursorPos=!0,this.textOff+=s,e-=s,i=0}}span(e,t,i,s){this.buildText(t-e,i,s),this.pos=t,this.openStart<0&&(this.openStart=s)}point(e,t,i,s,r,o){if(this.disallowBlockEffectsFor[o]&&i instanceof Ht){if(i.block)throw new RangeError("Block decorations may not be specified via plugins");if(t>this.doc.lineAt(this.pos).to)throw new RangeError("Decorations that replace line breaks may not be specified via plugins")}let l=t-e;if(i instanceof Ht)if(i.block)i.startSide>0&&!this.posCovered()&&this.getLine(),this.addBlockWidget(new Ct(i.widget||Ti.block,l,i));else{let a=Ft.create(i.widget||Ti.inline,l,l?0:i.startSide),h=this.atCursorPos&&!a.isEditable&&r<=s.length&&(e<t||i.startSide>0),c=!a.isEditable&&(e<t||r>s.length||i.startSide<=0),f=this.getLine();this.pendingBuffer==2&&!h&&!a.isEditable&&(this.pendingBuffer=0),this.flushBuffer(s),h&&(f.append(Mn(new Ai(1),s),r),r=s.length+Math.max(0,r-s.length)),f.append(Mn(a,s),r),this.atCursorPos=c,this.pendingBuffer=c?e<t||r>s.length?1:2:0,this.pendingBuffer&&(this.bufferMarks=s.slice())}else this.doc.lineAt(this.pos).from==this.pos&&this.getLine().addLineDeco(i);l&&(this.textOff+l<=this.text.length?this.textOff+=l:(this.skip+=l-(this.text.length-this.textOff),this.text="",this.textOff=0),this.pos=t),this.openStart<0&&(this.openStart=r)}static build(e,t,i,s,r){let o=new Qi(e,t,i,r);return o.openEnd=U.spans(s,t,i,o),o.openStart<0&&(o.openStart=o.openEnd),o.finish(o.openEnd),o}}function Mn(n,e){for(let t of e)n=new Tt(t,[n],n.length);return n}class Ti extends Et{constructor(e){super(),this.tag=e}eq(e){return e.tag==this.tag}toDOM(){return document.createElement(this.tag)}updateDOM(e){return e.nodeName.toLowerCase()==this.tag}get isHidden(){return!0}}Ti.inline=new Ti("span");Ti.block=new Ti("div");var ee=function(n){return n[n.LTR=0]="LTR",n[n.RTL=1]="RTL",n}(ee||(ee={}));const ri=ee.LTR,Uo=ee.RTL;function Ec(n){let e=[];for(let t=0;t<n.length;t++)e.push(1<<+n[t]);return e}const op=Ec("88888888888888888888888888888888888666888888787833333333337888888000000000000000000000000008888880000000000000000000000000088888888888888888888888888888888888887866668888088888663380888308888800000000000000000000000800000000000000000000000000000008"),lp=Ec("4444448826627288999999999992222222222222222222222222222222222222222222222229999999999999999999994444444444644222822222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222999999949999999229989999223333333333"),lo=Object.create(null),ot=[];for(let n of["()","[]","{}"]){let e=n.charCodeAt(0),t=n.charCodeAt(1);lo[e]=t,lo[t]=-e}function Dc(n){return n<=247?op[n]:1424<=n&&n<=1524?2:1536<=n&&n<=1785?lp[n-1536]:1774<=n&&n<=2220?4:8192<=n&&n<=8204?256:64336<=n&&n<=65023?4:1}const ap=/[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac\ufb50-\ufdff]/;class Vt{get dir(){return this.level%2?Uo:ri}constructor(e,t,i){this.from=e,this.to=t,this.level=i}side(e,t){return this.dir==t==e?this.to:this.from}forward(e,t){return e==(this.dir==t)}static find(e,t,i,s){let r=-1;for(let o=0;o<e.length;o++){let l=e[o];if(l.from<=t&&l.to>=t){if(l.level==i)return o;(r<0||(s!=0?s<0?l.from<t:l.to>t:e[r].level>l.level))&&(r=o)}}if(r<0)throw new RangeError("Index out of range");return r}}function Pc(n,e){if(n.length!=e.length)return!1;for(let t=0;t<n.length;t++){let i=n[t],s=e[t];if(i.from!=s.from||i.to!=s.to||i.direction!=s.direction||!Pc(i.inner,s.inner))return!1}return!0}const Y=[];function hp(n,e,t,i,s){for(let r=0;r<=i.length;r++){let o=r?i[r-1].to:e,l=r<i.length?i[r].from:t,a=r?256:s;for(let h=o,c=a,f=a;h<l;h++){let u=Dc(n.charCodeAt(h));u==512?u=c:u==8&&f==4&&(u=16),Y[h]=u==4?2:u,u&7&&(f=u),c=u}for(let h=o,c=a,f=a;h<l;h++){let u=Y[h];if(u==128)h<l-1&&c==Y[h+1]&&c&24?u=Y[h]=c:Y[h]=256;else if(u==64){let d=h+1;for(;d<l&&Y[d]==64;)d++;let m=h&&c==8||d<t&&Y[d]==8?f==1?1:8:256;for(let x=h;x<d;x++)Y[x]=m;h=d-1}else u==8&&f==1&&(Y[h]=1);c=u,u&7&&(f=u)}}}function cp(n,e,t,i,s){let r=s==1?2:1;for(let o=0,l=0,a=0;o<=i.length;o++){let h=o?i[o-1].to:e,c=o<i.length?i[o].from:t;for(let f=h,u,d,m;f<c;f++)if(d=lo[u=n.charCodeAt(f)])if(d<0){for(let x=l-3;x>=0;x-=3)if(ot[x+1]==-d){let S=ot[x+2],y=S&2?s:S&4?S&1?r:s:0;y&&(Y[f]=Y[ot[x]]=y),l=x;break}}else{if(ot.length==189)break;ot[l++]=f,ot[l++]=u,ot[l++]=a}else if((m=Y[f])==2||m==1){let x=m==s;a=x?0:1;for(let S=l-3;S>=0;S-=3){let y=ot[S+2];if(y&2)break;if(x)ot[S+2]|=2;else{if(y&4)break;ot[S+2]|=4}}}}}function fp(n,e,t,i){for(let s=0,r=i;s<=t.length;s++){let o=s?t[s-1].to:n,l=s<t.length?t[s].from:e;for(let a=o;a<l;){let h=Y[a];if(h==256){let c=a+1;for(;;)if(c==l){if(s==t.length)break;c=t[s++].to,l=s<t.length?t[s].from:e}else if(Y[c]==256)c++;else break;let f=r==1,u=(c<e?Y[c]:i)==1,d=f==u?f?1:2:i;for(let m=c,x=s,S=x?t[x-1].to:n;m>a;)m==S&&(m=t[--x].from,S=x?t[x-1].to:n),Y[--m]=d;a=c}else r=h,a++}}}function ao(n,e,t,i,s,r,o){let l=i%2?2:1;if(i%2==s%2)for(let a=e,h=0;a<t;){let c=!0,f=!1;if(h==r.length||a<r[h].from){let x=Y[a];x!=l&&(c=!1,f=x==16)}let u=!c&&l==1?[]:null,d=c?i:i+1,m=a;e:for(;;)if(h<r.length&&m==r[h].from){if(f)break e;let x=r[h];if(!c)for(let S=x.to,y=h+1;;){if(S==t)break e;if(y<r.length&&r[y].from==S)S=r[y++].to;else{if(Y[S]==l)break e;break}}if(h++,u)u.push(x);else{x.from>a&&o.push(new Vt(a,x.from,d));let S=x.direction==ri!=!(d%2);ho(n,S?i+1:i,s,x.inner,x.from,x.to,o),a=x.to}m=x.to}else{if(m==t||(c?Y[m]!=l:Y[m]==l))break;m++}u?ao(n,a,m,i+1,s,u,o):a<m&&o.push(new Vt(a,m,d)),a=m}else for(let a=t,h=r.length;a>e;){let c=!0,f=!1;if(!h||a>r[h-1].to){let x=Y[a-1];x!=l&&(c=!1,f=x==16)}let u=!c&&l==1?[]:null,d=c?i:i+1,m=a;e:for(;;)if(h&&m==r[h-1].to){if(f)break e;let x=r[--h];if(!c)for(let S=x.from,y=h;;){if(S==e)break e;if(y&&r[y-1].to==S)S=r[--y].from;else{if(Y[S-1]==l)break e;break}}if(u)u.push(x);else{x.to<a&&o.push(new Vt(x.to,a,d));let S=x.direction==ri!=!(d%2);ho(n,S?i+1:i,s,x.inner,x.from,x.to,o),a=x.from}m=x.from}else{if(m==e||(c?Y[m-1]!=l:Y[m-1]==l))break;m--}u?ao(n,m,a,i+1,s,u,o):m<a&&o.push(new Vt(m,a,d)),a=m}}function ho(n,e,t,i,s,r,o){let l=e%2?2:1;hp(n,s,r,i,l),cp(n,s,r,i,l),fp(s,r,i,l),ao(n,s,r,e,t,i,o)}function up(n,e,t){if(!n)return[new Vt(0,0,e==Uo?1:0)];if(e==ri&&!t.length&&!ap.test(n))return Nc(n.length);if(t.length)for(;n.length>Y.length;)Y[Y.length]=256;let i=[],s=e==ri?0:1;return ho(n,s,s,t,0,n.length,i),i}function Nc(n){return[new Vt(0,n,0)]}let Rc="";function dp(n,e,t,i,s){var r;let o=i.head-n.from,l=Vt.find(e,o,(r=i.bidiLevel)!==null&&r!==void 0?r:-1,i.assoc),a=e[l],h=a.side(s,t);if(o==h){let u=l+=s?1:-1;if(u<0||u>=e.length)return null;a=e[l=u],o=a.side(!s,t),h=a.side(s,t)}let c=Se(n.text,o,a.forward(s,t));(c<a.from||c>a.to)&&(c=h),Rc=n.text.slice(Math.min(o,c),Math.max(o,c));let f=l==(s?e.length-1:0)?null:e[l+(s?1:-1)];return f&&c==h&&f.level+(s?0:1)<a.level?E.cursor(f.side(!s,t)+n.from,f.forward(s,t)?1:-1,f.level):E.cursor(c+n.from,a.forward(s,t)?-1:1,a.level)}function pp(n,e,t){for(let i=e;i<t;i++){let s=Dc(n.charCodeAt(i));if(s==1)return ri;if(s==2||s==4)return Uo}return ri}const Bc=F.define(),Ic=F.define(),Lc=F.define(),Fc=F.define(),co=F.define(),Vc=F.define(),Wc=F.define(),Go=F.define(),Xo=F.define(),_c=F.define({combine:n=>n.some(e=>e)}),$c=F.define({combine:n=>n.some(e=>e)}),qc=F.define();class vi{constructor(e,t="nearest",i="nearest",s=5,r=5,o=!1){this.range=e,this.y=t,this.x=i,this.yMargin=s,this.xMargin=r,this.isSnapshot=o}map(e){return e.empty?this:new vi(this.range.map(e),this.y,this.x,this.yMargin,this.xMargin,this.isSnapshot)}clip(e){return this.range.to<=e.doc.length?this:new vi(E.cursor(e.doc.length),this.y,this.x,this.yMargin,this.xMargin,this.isSnapshot)}}const En=_.define({map:(n,e)=>n.map(e)}),Hc=_.define();function De(n,e,t){let i=n.facet(Fc);i.length?i[0](e):window.onerror&&window.onerror(String(e),t,void 0,void 0,e)||(t?console.error(t+":",e):console.error(e))}const kt=F.define({combine:n=>n.length?n[0]:!0});let mp=0;const pi=F.define({combine(n){return n.filter((e,t)=>{for(let i=0;i<t;i++)if(n[i].plugin==e.plugin)return!1;return!0})}});class ne{constructor(e,t,i,s,r){this.id=e,this.create=t,this.domEventHandlers=i,this.domEventObservers=s,this.baseExtensions=r(this),this.extension=this.baseExtensions.concat(pi.of({plugin:this,arg:void 0}))}of(e){return this.baseExtensions.concat(pi.of({plugin:this,arg:e}))}static define(e,t){const{eventHandlers:i,eventObservers:s,provide:r,decorations:o}=t||{};return new ne(mp++,e,i,s,l=>{let a=[];return o&&a.push(sn.of(h=>{let c=h.plugin(l);return c?o(c):W.none})),r&&a.push(r(l)),a})}static fromClass(e,t){return ne.define((i,s)=>new e(i,s),t)}}class nr{constructor(e){this.spec=e,this.mustUpdate=null,this.value=null}get plugin(){return this.spec&&this.spec.plugin}update(e){if(this.value){if(this.mustUpdate){let t=this.mustUpdate;if(this.mustUpdate=null,this.value.update)try{this.value.update(t)}catch(i){if(De(t.state,i,"CodeMirror plugin crashed"),this.value.destroy)try{this.value.destroy()}catch{}this.deactivate()}}}else if(this.spec)try{this.value=this.spec.plugin.create(e,this.spec.arg)}catch(t){De(e.state,t,"CodeMirror plugin crashed"),this.deactivate()}return this}destroy(e){var t;if(!((t=this.value)===null||t===void 0)&&t.destroy)try{this.value.destroy()}catch(i){De(e.state,i,"CodeMirror plugin crashed")}}deactivate(){this.spec=this.value=null}}const zc=F.define(),Qo=F.define(),sn=F.define(),jc=F.define(),Yo=F.define(),Kc=F.define();function Xl(n,e){let t=n.state.facet(Kc);if(!t.length)return t;let i=t.map(r=>r instanceof Function?r(n):r),s=[];return U.spans(i,e.from,e.to,{point(){},span(r,o,l,a){let h=r-e.from,c=o-e.from,f=s;for(let u=l.length-1;u>=0;u--,a--){let d=l[u].spec.bidiIsolate,m;if(d==null&&(d=pp(e.text,h,c)),a>0&&f.length&&(m=f[f.length-1]).to==h&&m.direction==d)m.to=c,f=m.inner;else{let x={from:h,to:c,direction:d,inner:[]};f.push(x),f=x.inner}}}}),s}const Uc=F.define();function Jo(n){let e=0,t=0,i=0,s=0;for(let r of n.state.facet(Uc)){let o=r(n);o&&(o.left!=null&&(e=Math.max(e,o.left)),o.right!=null&&(t=Math.max(t,o.right)),o.top!=null&&(i=Math.max(i,o.top)),o.bottom!=null&&(s=Math.max(s,o.bottom)))}return{left:e,right:t,top:i,bottom:s}}const Hi=F.define();class Qe{constructor(e,t,i,s){this.fromA=e,this.toA=t,this.fromB=i,this.toB=s}join(e){return new Qe(Math.min(this.fromA,e.fromA),Math.max(this.toA,e.toA),Math.min(this.fromB,e.fromB),Math.max(this.toB,e.toB))}addToSet(e){let t=e.length,i=this;for(;t>0;t--){let s=e[t-1];if(!(s.fromA>i.toA)){if(s.toA<i.fromA)break;i=i.join(s),e.splice(t-1,1)}}return e.splice(t,0,i),e}static extendWithRanges(e,t){if(t.length==0)return e;let i=[];for(let s=0,r=0,o=0,l=0;;s++){let a=s==e.length?null:e[s],h=o-l,c=a?a.fromB:1e9;for(;r<t.length&&t[r]<c;){let f=t[r],u=t[r+1],d=Math.max(l,f),m=Math.min(c,u);if(d<=m&&new Qe(d+h,m+h,d,m).addToSet(i),u>c)break;r+=2}if(!a)return i;new Qe(a.fromA,a.toA,a.fromB,a.toB).addToSet(i),o=a.toA,l=a.toB}}}class ms{constructor(e,t,i){this.view=e,this.state=t,this.transactions=i,this.flags=0,this.startState=e.state,this.changes=ce.empty(this.startState.doc.length);for(let r of i)this.changes=this.changes.compose(r.changes);let s=[];this.changes.iterChangedRanges((r,o,l,a)=>s.push(new Qe(r,o,l,a))),this.changedRanges=s}static create(e,t,i){return new ms(e,t,i)}get viewportChanged(){return(this.flags&4)>0}get viewportMoved(){return(this.flags&8)>0}get heightChanged(){return(this.flags&2)>0}get geometryChanged(){return this.docChanged||(this.flags&18)>0}get focusChanged(){return(this.flags&1)>0}get docChanged(){return!this.changes.empty}get selectionSet(){return this.transactions.some(e=>e.selection)}get empty(){return this.flags==0&&this.transactions.length==0}}class Ql extends J{get length(){return this.view.state.doc.length}constructor(e){super(),this.view=e,this.decorations=[],this.dynamicDecorationMap=[!1],this.domChanged=null,this.hasComposition=null,this.markedForComposition=new Set,this.editContextFormatting=W.none,this.lastCompositionAfterCursor=!1,this.minWidth=0,this.minWidthFrom=0,this.minWidthTo=0,this.impreciseAnchor=null,this.impreciseHead=null,this.forceSelection=!1,this.lastUpdate=Date.now(),this.setDOM(e.contentDOM),this.children=[new oe],this.children[0].setParent(this),this.updateDeco(),this.updateInner([new Qe(0,0,0,e.state.doc.length)],0,null)}update(e){var t;let i=e.changedRanges;this.minWidth>0&&i.length&&(i.every(({fromA:h,toA:c})=>c<this.minWidthFrom||h>this.minWidthTo)?(this.minWidthFrom=e.changes.mapPos(this.minWidthFrom,1),this.minWidthTo=e.changes.mapPos(this.minWidthTo,1)):this.minWidth=this.minWidthFrom=this.minWidthTo=0),this.updateEditContextFormatting(e);let s=-1;this.view.inputState.composing>=0&&!this.view.observer.editContext&&(!((t=this.domChanged)===null||t===void 0)&&t.newSel?s=this.domChanged.newSel.head:!Sp(e.changes,this.hasComposition)&&!e.selectionSet&&(s=e.state.selection.main.head));let r=s>-1?yp(this.view,e.changes,s):null;if(this.domChanged=null,this.hasComposition){this.markedForComposition.clear();let{from:h,to:c}=this.hasComposition;i=new Qe(h,c,e.changes.mapPos(h,-1),e.changes.mapPos(c,1)).addToSet(i.slice())}this.hasComposition=r?{from:r.range.fromB,to:r.range.toB}:null,(L.ie||L.chrome)&&!r&&e&&e.state.doc.lines!=e.startState.doc.lines&&(this.forceSelection=!0);let o=this.decorations,l=this.updateDeco(),a=vp(o,l,e.changes);return i=Qe.extendWithRanges(i,a),!(this.flags&7)&&i.length==0?!1:(this.updateInner(i,e.startState.doc.length,r),e.transactions.length&&(this.lastUpdate=Date.now()),!0)}updateInner(e,t,i){this.view.viewState.mustMeasureContent=!0,this.updateChildren(e,t,i);let{observer:s}=this.view;s.ignore(()=>{this.dom.style.height=this.view.viewState.contentHeight/this.view.scaleY+"px",this.dom.style.flexBasis=this.minWidth?this.minWidth+"px":"";let o=L.chrome||L.ios?{node:s.selectionRange.focusNode,written:!1}:void 0;this.sync(this.view,o),this.flags&=-8,o&&(o.written||s.selectionRange.focusNode!=o.node)&&(this.forceSelection=!0),this.dom.style.height=""}),this.markedForComposition.forEach(o=>o.flags&=-9);let r=[];if(this.view.viewport.from||this.view.viewport.to<this.view.state.doc.length)for(let o of this.children)o instanceof Ct&&o.widget instanceof oo&&r.push(o.dom);s.updateGaps(r)}updateChildren(e,t,i){let s=i?i.range.addToSet(e.slice()):e,r=this.childCursor(t);for(let o=s.length-1;;o--){let l=o>=0?s[o]:null;if(!l)break;let{fromA:a,toA:h,fromB:c,toB:f}=l,u,d,m,x;if(i&&i.range.fromB<f&&i.range.toB>c){let g=Qi.build(this.view.state.doc,c,i.range.fromB,this.decorations,this.dynamicDecorationMap),k=Qi.build(this.view.state.doc,i.range.toB,f,this.decorations,this.dynamicDecorationMap);d=g.breakAtStart,m=g.openStart,x=k.openEnd;let T=this.compositionView(i);k.breakAtStart?T.breakAfter=1:k.content.length&&T.merge(T.length,T.length,k.content[0],!1,k.openStart,0)&&(T.breakAfter=k.content[0].breakAfter,k.content.shift()),g.content.length&&T.merge(0,0,g.content[g.content.length-1],!0,0,g.openEnd)&&g.content.pop(),u=g.content.concat(T).concat(k.content)}else({content:u,breakAtStart:d,openStart:m,openEnd:x}=Qi.build(this.view.state.doc,c,f,this.decorations,this.dynamicDecorationMap));let{i:S,off:y}=r.findPos(h,1),{i:O,off:v}=r.findPos(a,-1);wc(this,O,v,S,y,u,d,m,x)}i&&this.fixCompositionDOM(i)}updateEditContextFormatting(e){this.editContextFormatting=this.editContextFormatting.map(e.changes);for(let t of e.transactions)for(let i of t.effects)i.is(Hc)&&(this.editContextFormatting=i.value)}compositionView(e){let t=new tt(e.text.nodeValue);t.flags|=8;for(let{deco:s}of e.marks)t=new Tt(s,[t],t.length);let i=new oe;return i.append(t,0),i}fixCompositionDOM(e){let t=(r,o)=>{o.flags|=8|(o.children.some(a=>a.flags&7)?1:0),this.markedForComposition.add(o);let l=J.get(r);l&&l!=o&&(l.dom=null),o.setDOM(r)},i=this.childPos(e.range.fromB,1),s=this.children[i.i];t(e.line,s);for(let r=e.marks.length-1;r>=-1;r--)i=s.childPos(i.off,1),s=s.children[i.i],t(r>=0?e.marks[r].node:e.text,s)}updateSelection(e=!1,t=!1){(e||!this.view.observer.selectionRange.focusNode)&&this.view.observer.readSelectionRange();let i=this.view.root.activeElement,s=i==this.dom,r=!s&&!(this.view.state.facet(kt)||this.dom.tabIndex>-1)&&Jn(this.dom,this.view.observer.selectionRange)&&!(i&&this.dom.contains(i));if(!(s||t||r))return;let o=this.forceSelection;this.forceSelection=!1;let l=this.view.state.selection.main,a=this.moveToLine(this.domAtPos(l.anchor)),h=l.empty?a:this.moveToLine(this.domAtPos(l.head));if(L.gecko&&l.empty&&!this.hasComposition&&gp(a)){let f=document.createTextNode("");this.view.observer.ignore(()=>a.node.insertBefore(f,a.node.childNodes[a.offset]||null)),a=h=new Ae(f,0),o=!0}let c=this.view.observer.selectionRange;(o||!c.focusNode||(!Xi(a.node,a.offset,c.anchorNode,c.anchorOffset)||!Xi(h.node,h.offset,c.focusNode,c.focusOffset))&&!this.suppressWidgetCursorChange(c,l))&&(this.view.observer.ignore(()=>{L.android&&L.chrome&&this.dom.contains(c.focusNode)&&wp(c.focusNode,this.dom)&&(this.dom.blur(),this.dom.focus({preventScroll:!0}));let f=nn(this.view.root);if(f)if(l.empty){if(L.gecko){let u=bp(a.node,a.offset);if(u&&u!=3){let d=(u==1?bc:xc)(a.node,a.offset);d&&(a=new Ae(d.node,d.offset))}}f.collapse(a.node,a.offset),l.bidiLevel!=null&&f.caretBidiLevel!==void 0&&(f.caretBidiLevel=l.bidiLevel)}else if(f.extend){f.collapse(a.node,a.offset);try{f.extend(h.node,h.offset)}catch{}}else{let u=document.createRange();l.anchor>l.head&&([a,h]=[h,a]),u.setEnd(h.node,h.offset),u.setStart(a.node,a.offset),f.removeAllRanges(),f.addRange(u)}r&&this.view.root.activeElement==this.dom&&(this.dom.blur(),i&&i.focus())}),this.view.observer.setSelectionRange(a,h)),this.impreciseAnchor=a.precise?null:new Ae(c.anchorNode,c.anchorOffset),this.impreciseHead=h.precise?null:new Ae(c.focusNode,c.focusOffset)}suppressWidgetCursorChange(e,t){return this.hasComposition&&t.empty&&Xi(e.focusNode,e.focusOffset,e.anchorNode,e.anchorOffset)&&this.posFromDOM(e.focusNode,e.focusOffset)==t.head}enforceCursorAssoc(){if(this.hasComposition)return;let{view:e}=this,t=e.state.selection.main,i=nn(e.root),{anchorNode:s,anchorOffset:r}=e.observer.selectionRange;if(!i||!t.empty||!t.assoc||!i.modify)return;let o=oe.find(this,t.head);if(!o)return;let l=o.posAtStart;if(t.head==l||t.head==l+o.length)return;let a=this.coordsAt(t.head,-1),h=this.coordsAt(t.head,1);if(!a||!h||a.bottom>h.top)return;let c=this.domAtPos(t.head+t.assoc);i.collapse(c.node,c.offset),i.modify("move",t.assoc<0?"forward":"backward","lineboundary"),e.observer.readSelectionRange();let f=e.observer.selectionRange;e.docView.posFromDOM(f.anchorNode,f.anchorOffset)!=t.from&&i.collapse(s,r)}moveToLine(e){let t=this.dom,i;if(e.node!=t)return e;for(let s=e.offset;!i&&s<t.childNodes.length;s++){let r=J.get(t.childNodes[s]);r instanceof oe&&(i=r.domAtPos(0))}for(let s=e.offset-1;!i&&s>=0;s--){let r=J.get(t.childNodes[s]);r instanceof oe&&(i=r.domAtPos(r.length))}return i?new Ae(i.node,i.offset,!0):e}nearest(e){for(let t=e;t;){let i=J.get(t);if(i&&i.rootView==this)return i;t=t.parentNode}return null}posFromDOM(e,t){let i=this.nearest(e);if(!i)throw new RangeError("Trying to find position for a DOM position outside of the document");return i.localPosFromDOM(e,t)+i.posAtStart}domAtPos(e){let{i:t,off:i}=this.childCursor().findPos(e,-1);for(;t<this.children.length-1;){let s=this.children[t];if(i<s.length||s instanceof oe)break;t++,i=0}return this.children[t].domAtPos(i)}coordsAt(e,t){let i=null,s=0;for(let r=this.length,o=this.children.length-1;o>=0;o--){let l=this.children[o],a=r-l.breakAfter,h=a-l.length;if(a<e)break;if(h<=e&&(h<e||l.covers(-1))&&(a>e||l.covers(1))&&(!i||l instanceof oe&&!(i instanceof oe&&t>=0)))i=l,s=h;else if(i&&h==e&&a==e&&l instanceof Ct&&Math.abs(t)<2){if(l.deco.startSide<0)break;o&&(i=null)}r=h}return i?i.coordsAt(e-s,t):null}coordsForChar(e){let{i:t,off:i}=this.childPos(e,1),s=this.children[t];if(!(s instanceof oe))return null;for(;s.children.length;){let{i:l,off:a}=s.childPos(i,1);for(;;l++){if(l==s.children.length)return null;if((s=s.children[l]).length)break}i=a}if(!(s instanceof tt))return null;let r=Se(s.text,i);if(r==i)return null;let o=si(s.dom,i,r).getClientRects();for(let l=0;l<o.length;l++){let a=o[l];if(l==o.length-1||a.top<a.bottom&&a.left<a.right)return a}return null}measureVisibleLineHeights(e){let t=[],{from:i,to:s}=e,r=this.view.contentDOM.clientWidth,o=r>Math.max(this.view.scrollDOM.clientWidth,this.minWidth)+1,l=-1,a=this.view.textDirection==ee.LTR;for(let h=0,c=0;c<this.children.length;c++){let f=this.children[c],u=h+f.length;if(u>s)break;if(h>=i){let d=f.dom.getBoundingClientRect();if(t.push(d.height),o){let m=f.dom.lastChild,x=m?Ci(m):[];if(x.length){let S=x[x.length-1],y=a?S.right-d.left:d.right-S.left;y>l&&(l=y,this.minWidth=r,this.minWidthFrom=h,this.minWidthTo=u)}}}h=u+f.breakAfter}return t}textDirectionAt(e){let{i:t}=this.childPos(e,1);return getComputedStyle(this.children[t].dom).direction=="rtl"?ee.RTL:ee.LTR}measureTextSize(){for(let r of this.children)if(r instanceof oe){let o=r.measureTextSize();if(o)return o}let e=document.createElement("div"),t,i,s;return e.className="cm-line",e.style.width="99999px",e.style.position="absolute",e.textContent="abc def ghi jkl mno pqr stu",this.view.observer.ignore(()=>{this.dom.appendChild(e);let r=Ci(e.firstChild)[0];t=e.getBoundingClientRect().height,i=r?r.width/27:7,s=r?r.height:t,e.remove()}),{lineHeight:t,charWidth:i,textHeight:s}}childCursor(e=this.length){let t=this.children.length;return t&&(e-=this.children[--t].length),new vc(this.children,e,t)}computeBlockGapDeco(){let e=[],t=this.view.viewState;for(let i=0,s=0;;s++){let r=s==t.viewports.length?null:t.viewports[s],o=r?r.from-1:this.length;if(o>i){let l=(t.lineBlockAt(o).bottom-t.lineBlockAt(i).top)/this.view.scaleY;e.push(W.replace({widget:new oo(l),block:!0,inclusive:!0,isBlockGap:!0}).range(i,o))}if(!r)break;i=r.to+1}return W.set(e)}updateDeco(){let e=1,t=this.view.state.facet(sn).map(r=>(this.dynamicDecorationMap[e++]=typeof r=="function")?r(this.view):r),i=!1,s=this.view.state.facet(jc).map((r,o)=>{let l=typeof r=="function";return l&&(i=!0),l?r(this.view):r});for(s.length&&(this.dynamicDecorationMap[e++]=i,t.push(U.join(s))),this.decorations=[this.editContextFormatting,...t,this.computeBlockGapDeco(),this.view.viewState.lineGapDeco];e<this.decorations.length;)this.dynamicDecorationMap[e++]=!1;return this.decorations}scrollIntoView(e){if(e.isSnapshot){let h=this.view.viewState.lineBlockAt(e.range.head);this.view.scrollDOM.scrollTop=h.top-e.yMargin,this.view.scrollDOM.scrollLeft=e.xMargin;return}for(let h of this.view.state.facet(qc))try{if(h(this.view,e.range,e))return!0}catch(c){De(this.view.state,c,"scroll handler")}let{range:t}=e,i=this.coordsAt(t.head,t.empty?t.assoc:t.head>t.anchor?-1:1),s;if(!i)return;!t.empty&&(s=this.coordsAt(t.anchor,t.anchor>t.head?-1:1))&&(i={left:Math.min(i.left,s.left),top:Math.min(i.top,s.top),right:Math.max(i.right,s.right),bottom:Math.max(i.bottom,s.bottom)});let r=Jo(this.view),o={left:i.left-r.left,top:i.top-r.top,right:i.right+r.right,bottom:i.bottom+r.bottom},{offsetWidth:l,offsetHeight:a}=this.view.scrollDOM;Gd(this.view.scrollDOM,o,t.head<t.anchor?-1:1,e.x,e.y,Math.max(Math.min(e.xMargin,l),-l),Math.max(Math.min(e.yMargin,a),-a),this.view.textDirection==ee.LTR)}}function gp(n){return n.node.nodeType==1&&n.node.firstChild&&(n.offset==0||n.node.childNodes[n.offset-1].contentEditable=="false")&&(n.offset==n.node.childNodes.length||n.node.childNodes[n.offset].contentEditable=="false")}function Gc(n,e){let t=n.observer.selectionRange;if(!t.focusNode)return null;let i=bc(t.focusNode,t.focusOffset),s=xc(t.focusNode,t.focusOffset),r=i||s;if(s&&i&&s.node!=i.node){let l=J.get(s.node);if(!l||l instanceof tt&&l.text!=s.node.nodeValue)r=s;else if(n.docView.lastCompositionAfterCursor){let a=J.get(i.node);!a||a instanceof tt&&a.text!=i.node.nodeValue||(r=s)}}if(n.docView.lastCompositionAfterCursor=r!=i,!r)return null;let o=e-r.offset;return{from:o,to:o+r.node.nodeValue.length,node:r.node}}function yp(n,e,t){let i=Gc(n,t);if(!i)return null;let{node:s,from:r,to:o}=i,l=s.nodeValue;if(/[\n\r]/.test(l)||n.state.doc.sliceString(i.from,i.to)!=l)return null;let a=e.invertedDesc,h=new Qe(a.mapPos(r),a.mapPos(o),r,o),c=[];for(let f=s.parentNode;;f=f.parentNode){let u=J.get(f);if(u instanceof Tt)c.push({node:f,deco:u.mark});else{if(u instanceof oe||f.nodeName=="DIV"&&f.parentNode==n.contentDOM)return{range:h,text:s,marks:c,line:f};if(f!=n.contentDOM)c.push({node:f,deco:new bn({inclusive:!0,attributes:sp(f),tagName:f.tagName.toLowerCase()})});else return null}}}function bp(n,e){return n.nodeType!=1?0:(e&&n.childNodes[e-1].contentEditable=="false"?1:0)|(e<n.childNodes.length&&n.childNodes[e].contentEditable=="false"?2:0)}let xp=class{constructor(){this.changes=[]}compareRange(e,t){Zn(e,t,this.changes)}comparePoint(e,t){Zn(e,t,this.changes)}boundChange(e){Zn(e,e,this.changes)}};function vp(n,e,t){let i=new xp;return U.compare(n,e,t,i),i.changes}function wp(n,e){for(let t=n;t&&t!=e;t=t.assignedSlot||t.parentNode)if(t.nodeType==1&&t.contentEditable=="false")return!0;return!1}function Sp(n,e){let t=!1;return e&&n.iterChangedRanges((i,s)=>{i<e.to&&s>e.from&&(t=!0)}),t}function Op(n,e,t=1){let i=n.charCategorizer(e),s=n.doc.lineAt(e),r=e-s.from;if(s.length==0)return E.cursor(e);r==0?t=1:r==s.length&&(t=-1);let o=r,l=r;t<0?o=Se(s.text,r,!1):l=Se(s.text,r);let a=i(s.text.slice(o,l));for(;o>0;){let h=Se(s.text,o,!1);if(i(s.text.slice(h,o))!=a)break;o=h}for(;l<s.length;){let h=Se(s.text,l);if(i(s.text.slice(l,h))!=a)break;l=h}return E.range(o+s.from,l+s.from)}function kp(n,e){return e.left>n?e.left-n:Math.max(0,n-e.right)}function Cp(n,e){return e.top>n?e.top-n:Math.max(0,n-e.bottom)}function sr(n,e){return n.top<e.bottom-1&&n.bottom>e.top+1}function Yl(n,e){return e<n.top?{top:e,left:n.left,right:n.right,bottom:n.bottom}:n}function Jl(n,e){return e>n.bottom?{top:n.top,left:n.left,right:n.right,bottom:e}:n}function fo(n,e,t){let i,s,r,o,l=!1,a,h,c,f;for(let m=n.firstChild;m;m=m.nextSibling){let x=Ci(m);for(let S=0;S<x.length;S++){let y=x[S];s&&sr(s,y)&&(y=Yl(Jl(y,s.bottom),s.top));let O=kp(e,y),v=Cp(t,y);if(O==0&&v==0)return m.nodeType==3?Zl(m,e,t):fo(m,e,t);(!i||o>v||o==v&&r>O)&&(i=m,s=y,r=O,o=v,l=O?e<y.left?S>0:S<x.length-1:!0),O==0?t>y.bottom&&(!c||c.bottom<y.bottom)?(a=m,c=y):t<y.top&&(!f||f.top>y.top)&&(h=m,f=y):c&&sr(c,y)?c=Jl(c,y.bottom):f&&sr(f,y)&&(f=Yl(f,y.top))}}if(c&&c.bottom>=t?(i=a,s=c):f&&f.top<=t&&(i=h,s=f),!i)return{node:n,offset:0};let u=Math.max(s.left,Math.min(s.right,e));if(i.nodeType==3)return Zl(i,u,t);if(l&&i.contentEditable!="false")return fo(i,u,t);let d=Array.prototype.indexOf.call(n.childNodes,i)+(e>=(s.left+s.right)/2?1:0);return{node:n,offset:d}}function Zl(n,e,t){let i=n.nodeValue.length,s=-1,r=1e9,o=0;for(let l=0;l<i;l++){let a=si(n,l,l+1).getClientRects();for(let h=0;h<a.length;h++){let c=a[h];if(c.top==c.bottom)continue;o||(o=e-c.left);let f=(c.top>t?c.top-t:t-c.bottom)-1;if(c.left-1<=e&&c.right+1>=e&&f<r){let u=e>=(c.left+c.right)/2,d=u;if((L.chrome||L.gecko)&&si(n,l).getBoundingClientRect().left==c.right&&(d=!u),f<=0)return{node:n,offset:l+(d?1:0)};s=l+(d?1:0),r=f}}}return{node:n,offset:s>-1?s:o>0?n.nodeValue.length:0}}function Xc(n,e,t,i=-1){var s,r;let o=n.contentDOM.getBoundingClientRect(),l=o.top+n.viewState.paddingTop,a,{docHeight:h}=n.viewState,{x:c,y:f}=e,u=f-l;if(u<0)return 0;if(u>h)return n.state.doc.length;for(let g=n.viewState.heightOracle.textHeight/2,k=!1;a=n.elementAtHeight(u),a.type!=Pe.Text;)for(;u=i>0?a.bottom+g:a.top-g,!(u>=0&&u<=h);){if(k)return t?null:0;k=!0,i=-i}f=l+u;let d=a.from;if(d<n.viewport.from)return n.viewport.from==0?0:t?null:ea(n,o,a,c,f);if(d>n.viewport.to)return n.viewport.to==n.state.doc.length?n.state.doc.length:t?null:ea(n,o,a,c,f);let m=n.dom.ownerDocument,x=n.root.elementFromPoint?n.root:m,S=x.elementFromPoint(c,f);S&&!n.contentDOM.contains(S)&&(S=null),S||(c=Math.max(o.left+1,Math.min(o.right-1,c)),S=x.elementFromPoint(c,f),S&&!n.contentDOM.contains(S)&&(S=null));let y,O=-1;if(S&&((s=n.docView.nearest(S))===null||s===void 0?void 0:s.isEditable)!=!1){if(m.caretPositionFromPoint){let g=m.caretPositionFromPoint(c,f);g&&({offsetNode:y,offset:O}=g)}else if(m.caretRangeFromPoint){let g=m.caretRangeFromPoint(c,f);g&&({startContainer:y,startOffset:O}=g,(!n.contentDOM.contains(y)||L.safari&&Ap(y,O,c)||L.chrome&&Tp(y,O,c))&&(y=void 0))}y&&(O=Math.min(yt(y),O))}if(!y||!n.docView.dom.contains(y)){let g=oe.find(n.docView,d);if(!g)return u>a.top+a.height/2?a.to:a.from;({node:y,offset:O}=fo(g.dom,c,f))}let v=n.docView.nearest(y);if(!v)return null;if(v.isWidget&&((r=v.dom)===null||r===void 0?void 0:r.nodeType)==1){let g=v.dom.getBoundingClientRect();return e.y<g.top||e.y<=g.bottom&&e.x<=(g.left+g.right)/2?v.posAtStart:v.posAtEnd}else return v.localPosFromDOM(y,O)+v.posAtStart}function ea(n,e,t,i,s){let r=Math.round((i-e.left)*n.defaultCharacterWidth);if(n.lineWrapping&&t.height>n.defaultLineHeight*1.5){let l=n.viewState.heightOracle.textHeight,a=Math.floor((s-t.top-(n.defaultLineHeight-l)*.5)/l);r+=a*n.viewState.heightOracle.lineLength}let o=n.state.sliceDoc(t.from,t.to);return t.from+Yr(o,r,n.state.tabSize)}function Ap(n,e,t){let i,s=n;if(n.nodeType!=3||e!=(i=n.nodeValue.length))return!1;for(;;){let r=s.nextSibling;if(r){if(r.nodeName=="BR")break;return!1}else{let o=s.parentNode;if(!o||o.nodeName=="DIV")break;s=o}}return si(n,i-1,i).getBoundingClientRect().right>t}function Tp(n,e,t){if(e!=0)return!1;for(let s=n;;){let r=s.parentNode;if(!r||r.nodeType!=1||r.firstChild!=s)return!1;if(r.classList.contains("cm-line"))break;s=r}let i=n.nodeType==1?n.getBoundingClientRect():si(n,0,Math.max(n.nodeValue.length,1)).getBoundingClientRect();return t-i.left>5}function uo(n,e,t){let i=n.lineBlockAt(e);if(Array.isArray(i.type)){let s;for(let r of i.type){if(r.from>e)break;if(!(r.to<e)){if(r.from<e&&r.to>e)return r;(!s||r.type==Pe.Text&&(s.type!=r.type||(t<0?r.from<e:r.to>e)))&&(s=r)}}return s||i}return i}function Mp(n,e,t,i){let s=uo(n,e.head,e.assoc||-1),r=!i||s.type!=Pe.Text||!(n.lineWrapping||s.widgetLineBreaks)?null:n.coordsAtPos(e.assoc<0&&e.head>s.from?e.head-1:e.head);if(r){let o=n.dom.getBoundingClientRect(),l=n.textDirectionAt(s.from),a=n.posAtCoords({x:t==(l==ee.LTR)?o.right-1:o.left+1,y:(r.top+r.bottom)/2});if(a!=null)return E.cursor(a,t?-1:1)}return E.cursor(t?s.to:s.from,t?-1:1)}function ta(n,e,t,i){let s=n.state.doc.lineAt(e.head),r=n.bidiSpans(s),o=n.textDirectionAt(s.from);for(let l=e,a=null;;){let h=dp(s,r,o,l,t),c=Rc;if(!h){if(s.number==(t?n.state.doc.lines:1))return l;c=`
`,s=n.state.doc.line(s.number+(t?1:-1)),r=n.bidiSpans(s),h=n.visualLineSide(s,!t)}if(a){if(!a(c))return l}else{if(!i)return h;a=i(c)}l=h}}function Ep(n,e,t){let i=n.state.charCategorizer(e),s=i(t);return r=>{let o=i(r);return s==te.Space&&(s=o),s==o}}function Dp(n,e,t,i){let s=e.head,r=t?1:-1;if(s==(t?n.state.doc.length:0))return E.cursor(s,e.assoc);let o=e.goalColumn,l,a=n.contentDOM.getBoundingClientRect(),h=n.coordsAtPos(s,e.assoc||-1),c=n.documentTop;if(h)o==null&&(o=h.left-a.left),l=r<0?h.top:h.bottom;else{let d=n.viewState.lineBlockAt(s);o==null&&(o=Math.min(a.right-a.left,n.defaultCharacterWidth*(s-d.from))),l=(r<0?d.top:d.bottom)+c}let f=a.left+o,u=i??n.viewState.heightOracle.textHeight>>1;for(let d=0;;d+=10){let m=l+(u+d)*r,x=Xc(n,{x:f,y:m},!1,r);if(m<a.top||m>a.bottom||(r<0?x<s:x>s)){let S=n.docView.coordsForChar(x),y=!S||m<S.top?-1:1;return E.cursor(x,y,void 0,o)}}}function es(n,e,t){for(;;){let i=0;for(let s of n)s.between(e-1,e+1,(r,o,l)=>{if(e>r&&e<o){let a=i||t||(e-r<o-e?-1:1);e=a<0?r:o,i=a}});if(!i)return e}}function rr(n,e,t){let i=es(n.state.facet(Yo).map(s=>s(n)),t.from,e.head>t.from?-1:1);return i==t.from?t:E.cursor(i,i<t.from?1:-1)}const zi="￿";class Pp{constructor(e,t){this.points=e,this.text="",this.lineSeparator=t.facet(j.lineSeparator)}append(e){this.text+=e}lineBreak(){this.text+=zi}readRange(e,t){if(!e)return this;let i=e.parentNode;for(let s=e;;){this.findPointBefore(i,s);let r=this.text.length;this.readNode(s);let o=s.nextSibling;if(o==t)break;let l=J.get(s),a=J.get(o);(l&&a?l.breakAfter:(l?l.breakAfter:ds(s))||ds(o)&&(s.nodeName!="BR"||s.cmIgnore)&&this.text.length>r)&&this.lineBreak(),s=o}return this.findPointBefore(i,t),this}readTextNode(e){let t=e.nodeValue;for(let i of this.points)i.node==e&&(i.pos=this.text.length+Math.min(i.offset,t.length));for(let i=0,s=this.lineSeparator?null:/\r\n?|\n/g;;){let r=-1,o=1,l;if(this.lineSeparator?(r=t.indexOf(this.lineSeparator,i),o=this.lineSeparator.length):(l=s.exec(t))&&(r=l.index,o=l[0].length),this.append(t.slice(i,r<0?t.length:r)),r<0)break;if(this.lineBreak(),o>1)for(let a of this.points)a.node==e&&a.pos>this.text.length&&(a.pos-=o-1);i=r+o}}readNode(e){if(e.cmIgnore)return;let t=J.get(e),i=t&&t.overrideDOMText;if(i!=null){this.findPointInside(e,i.length);for(let s=i.iter();!s.next().done;)s.lineBreak?this.lineBreak():this.append(s.value)}else e.nodeType==3?this.readTextNode(e):e.nodeName=="BR"?e.nextSibling&&this.lineBreak():e.nodeType==1&&this.readRange(e.firstChild,null)}findPointBefore(e,t){for(let i of this.points)i.node==e&&e.childNodes[i.offset]==t&&(i.pos=this.text.length)}findPointInside(e,t){for(let i of this.points)(e.nodeType==3?i.node==e:e.contains(i.node))&&(i.pos=this.text.length+(Np(e,i.node,i.offset)?t:0))}}function Np(n,e,t){for(;;){if(!e||t<yt(e))return!1;if(e==n)return!0;t=ni(e)+1,e=e.parentNode}}class ia{constructor(e,t){this.node=e,this.offset=t,this.pos=-1}}class Rp{constructor(e,t,i,s){this.typeOver=s,this.bounds=null,this.text="",this.domChanged=t>-1;let{impreciseHead:r,impreciseAnchor:o}=e.docView;if(e.state.readOnly&&t>-1)this.newSel=null;else if(t>-1&&(this.bounds=e.docView.domBoundsAround(t,i,0))){let l=r||o?[]:Lp(e),a=new Pp(l,e.state);a.readRange(this.bounds.startDOM,this.bounds.endDOM),this.text=a.text,this.newSel=Fp(l,this.bounds.from)}else{let l=e.observer.selectionRange,a=r&&r.node==l.focusNode&&r.offset==l.focusOffset||!eo(e.contentDOM,l.focusNode)?e.state.selection.main.head:e.docView.posFromDOM(l.focusNode,l.focusOffset),h=o&&o.node==l.anchorNode&&o.offset==l.anchorOffset||!eo(e.contentDOM,l.anchorNode)?e.state.selection.main.anchor:e.docView.posFromDOM(l.anchorNode,l.anchorOffset),c=e.viewport;if((L.ios||L.chrome)&&e.state.selection.main.empty&&a!=h&&(c.from>0||c.to<e.state.doc.length)){let f=Math.min(a,h),u=Math.max(a,h),d=c.from-f,m=c.to-u;(d==0||d==1||f==0)&&(m==0||m==-1||u==e.state.doc.length)&&(a=0,h=e.state.doc.length)}this.newSel=E.single(h,a)}}}function Qc(n,e){let t,{newSel:i}=e,s=n.state.selection.main,r=n.inputState.lastKeyTime>Date.now()-100?n.inputState.lastKeyCode:-1;if(e.bounds){let{from:o,to:l}=e.bounds,a=s.from,h=null;(r===8||L.android&&e.text.length<l-o)&&(a=s.to,h="end");let c=Ip(n.state.doc.sliceString(o,l,zi),e.text,a-o,h);c&&(L.chrome&&r==13&&c.toB==c.from+2&&e.text.slice(c.from,c.toB)==zi+zi&&c.toB--,t={from:o+c.from,to:o+c.toA,insert:G.of(e.text.slice(c.from,c.toB).split(zi))})}else i&&(!n.hasFocus&&n.state.facet(kt)||i.main.eq(s))&&(i=null);if(!t&&!i)return!1;if(!t&&e.typeOver&&!s.empty&&i&&i.main.empty?t={from:s.from,to:s.to,insert:n.state.doc.slice(s.from,s.to)}:(L.mac||L.android)&&t&&t.from==t.to&&t.from==s.head-1&&/^\. ?$/.test(t.insert.toString())&&n.contentDOM.getAttribute("autocorrect")=="off"?(i&&t.insert.length==2&&(i=E.single(i.main.anchor-1,i.main.head-1)),t={from:t.from,to:t.to,insert:G.of([t.insert.toString().replace("."," ")])}):t&&t.from>=s.from&&t.to<=s.to&&(t.from!=s.from||t.to!=s.to)&&s.to-s.from-(t.to-t.from)<=4?t={from:s.from,to:s.to,insert:n.state.doc.slice(s.from,t.from).append(t.insert).append(n.state.doc.slice(t.to,s.to))}:L.chrome&&t&&t.from==t.to&&t.from==s.head&&t.insert.toString()==`
 `&&n.lineWrapping&&(i&&(i=E.single(i.main.anchor-1,i.main.head-1)),t={from:s.from,to:s.to,insert:G.of([" "])}),t)return Zo(n,t,i,r);if(i&&!i.main.eq(s)){let o=!1,l="select";return n.inputState.lastSelectionTime>Date.now()-50&&(n.inputState.lastSelectionOrigin=="select"&&(o=!0),l=n.inputState.lastSelectionOrigin),n.dispatch({selection:i,scrollIntoView:o,userEvent:l}),!0}else return!1}function Zo(n,e,t,i=-1){if(L.ios&&n.inputState.flushIOSKey(e))return!0;let s=n.state.selection.main;if(L.android&&(e.to==s.to&&(e.from==s.from||e.from==s.from-1&&n.state.sliceDoc(e.from,s.from)==" ")&&e.insert.length==1&&e.insert.lines==2&&xi(n.contentDOM,"Enter",13)||(e.from==s.from-1&&e.to==s.to&&e.insert.length==0||i==8&&e.insert.length<e.to-e.from&&e.to>s.head)&&xi(n.contentDOM,"Backspace",8)||e.from==s.from&&e.to==s.to+1&&e.insert.length==0&&xi(n.contentDOM,"Delete",46)))return!0;let r=e.insert.toString();n.inputState.composing>=0&&n.inputState.composing++;let o,l=()=>o||(o=Bp(n,e,t));return n.state.facet(Vc).some(a=>a(n,e.from,e.to,r,l))||n.dispatch(l()),!0}function Bp(n,e,t){let i,s=n.state,r=s.selection.main;if(e.from>=r.from&&e.to<=r.to&&e.to-e.from>=(r.to-r.from)/3&&(!t||t.main.empty&&t.main.from==e.from+e.insert.length)&&n.inputState.composing<0){let l=r.from<e.from?s.sliceDoc(r.from,e.from):"",a=r.to>e.to?s.sliceDoc(e.to,r.to):"";i=s.replaceSelection(n.state.toText(l+e.insert.sliceString(0,void 0,n.state.lineBreak)+a))}else{let l=s.changes(e),a=t&&t.main.to<=l.newLength?t.main:void 0;if(s.selection.ranges.length>1&&n.inputState.composing>=0&&e.to<=r.to&&e.to>=r.to-10){let h=n.state.sliceDoc(e.from,e.to),c,f=t&&Gc(n,t.main.head);if(f){let m=e.insert.length-(e.to-e.from);c={from:f.from,to:f.to-m}}else c=n.state.doc.lineAt(r.head);let u=r.to-e.to,d=r.to-r.from;i=s.changeByRange(m=>{if(m.from==r.from&&m.to==r.to)return{changes:l,range:a||m.map(l)};let x=m.to-u,S=x-h.length;if(m.to-m.from!=d||n.state.sliceDoc(S,x)!=h||m.to>=c.from&&m.from<=c.to)return{range:m};let y=s.changes({from:S,to:x,insert:e.insert}),O=m.to-r.to;return{changes:y,range:a?E.range(Math.max(0,a.anchor+O),Math.max(0,a.head+O)):m.map(y)}})}else i={changes:l,selection:a&&s.selection.replaceRange(a)}}let o="input.type";return(n.composing||n.inputState.compositionPendingChange&&n.inputState.compositionEndedAt>Date.now()-50)&&(n.inputState.compositionPendingChange=!1,o+=".compose",n.inputState.compositionFirstChange&&(o+=".start",n.inputState.compositionFirstChange=!1)),s.update(i,{userEvent:o,scrollIntoView:!0})}function Ip(n,e,t,i){let s=Math.min(n.length,e.length),r=0;for(;r<s&&n.charCodeAt(r)==e.charCodeAt(r);)r++;if(r==s&&n.length==e.length)return null;let o=n.length,l=e.length;for(;o>0&&l>0&&n.charCodeAt(o-1)==e.charCodeAt(l-1);)o--,l--;if(i=="end"){let a=Math.max(0,r-Math.min(o,l));t-=o+a-r}if(o<r&&n.length<e.length){let a=t<=r&&t>=o?r-t:0;r-=a,l=r+(l-o),o=r}else if(l<r){let a=t<=r&&t>=l?r-t:0;r-=a,o=r+(o-l),l=r}return{from:r,toA:o,toB:l}}function Lp(n){let e=[];if(n.root.activeElement!=n.contentDOM)return e;let{anchorNode:t,anchorOffset:i,focusNode:s,focusOffset:r}=n.observer.selectionRange;return t&&(e.push(new ia(t,i)),(s!=t||r!=i)&&e.push(new ia(s,r))),e}function Fp(n,e){if(n.length==0)return null;let t=n[0].pos,i=n.length==2?n[1].pos:t;return t>-1&&i>-1?E.single(t+e,i+e):null}class Vp{setSelectionOrigin(e){this.lastSelectionOrigin=e,this.lastSelectionTime=Date.now()}constructor(e){this.view=e,this.lastKeyCode=0,this.lastKeyTime=0,this.lastTouchTime=0,this.lastFocusTime=0,this.lastScrollTop=0,this.lastScrollLeft=0,this.pendingIOSKey=void 0,this.tabFocusMode=-1,this.lastSelectionOrigin=null,this.lastSelectionTime=0,this.lastContextMenu=0,this.scrollHandlers=[],this.handlers=Object.create(null),this.composing=-1,this.compositionFirstChange=null,this.compositionEndedAt=0,this.compositionPendingKey=!1,this.compositionPendingChange=!1,this.mouseSelection=null,this.draggedContent=null,this.handleEvent=this.handleEvent.bind(this),this.notifiedFocused=e.hasFocus,L.safari&&e.contentDOM.addEventListener("input",()=>null),L.gecko&&em(e.contentDOM.ownerDocument)}handleEvent(e){!Kp(this.view,e)||this.ignoreDuringComposition(e)||e.type=="keydown"&&this.keydown(e)||(this.view.updateState!=0?Promise.resolve().then(()=>this.runHandlers(e.type,e)):this.runHandlers(e.type,e))}runHandlers(e,t){let i=this.handlers[e];if(i){for(let s of i.observers)s(this.view,t);for(let s of i.handlers){if(t.defaultPrevented)break;if(s(this.view,t)){t.preventDefault();break}}}}ensureHandlers(e){let t=Wp(e),i=this.handlers,s=this.view.contentDOM;for(let r in t)if(r!="scroll"){let o=!t[r].handlers.length,l=i[r];l&&o!=!l.handlers.length&&(s.removeEventListener(r,this.handleEvent),l=null),l||s.addEventListener(r,this.handleEvent,{passive:o})}for(let r in i)r!="scroll"&&!t[r]&&s.removeEventListener(r,this.handleEvent);this.handlers=t}keydown(e){if(this.lastKeyCode=e.keyCode,this.lastKeyTime=Date.now(),e.keyCode==9&&this.tabFocusMode>-1&&(!this.tabFocusMode||Date.now()<=this.tabFocusMode))return!0;if(this.tabFocusMode>0&&e.keyCode!=27&&Jc.indexOf(e.keyCode)<0&&(this.tabFocusMode=-1),L.android&&L.chrome&&!e.synthetic&&(e.keyCode==13||e.keyCode==8))return this.view.observer.delayAndroidKey(e.key,e.keyCode),!0;let t;return L.ios&&!e.synthetic&&!e.altKey&&!e.metaKey&&((t=Yc.find(i=>i.keyCode==e.keyCode))&&!e.ctrlKey||_p.indexOf(e.key)>-1&&e.ctrlKey&&!e.shiftKey)?(this.pendingIOSKey=t||e,setTimeout(()=>this.flushIOSKey(),250),!0):(e.keyCode!=229&&this.view.observer.forceFlush(),!1)}flushIOSKey(e){let t=this.pendingIOSKey;return!t||t.key=="Enter"&&e&&e.from<e.to&&/^\S+$/.test(e.insert.toString())?!1:(this.pendingIOSKey=void 0,xi(this.view.contentDOM,t.key,t.keyCode,t instanceof KeyboardEvent?t:void 0))}ignoreDuringComposition(e){return/^key/.test(e.type)?this.composing>0?!0:L.safari&&!L.ios&&this.compositionPendingKey&&Date.now()-this.compositionEndedAt<100?(this.compositionPendingKey=!1,!0):!1:!1}startMouseSelection(e){this.mouseSelection&&this.mouseSelection.destroy(),this.mouseSelection=e}update(e){this.view.observer.update(e),this.mouseSelection&&this.mouseSelection.update(e),this.draggedContent&&e.docChanged&&(this.draggedContent=this.draggedContent.map(e.changes)),e.transactions.length&&(this.lastKeyCode=this.lastSelectionTime=0)}destroy(){this.mouseSelection&&this.mouseSelection.destroy()}}function na(n,e){return(t,i)=>{try{return e.call(n,i,t)}catch(s){De(t.state,s)}}}function Wp(n){let e=Object.create(null);function t(i){return e[i]||(e[i]={observers:[],handlers:[]})}for(let i of n){let s=i.spec,r=s&&s.plugin.domEventHandlers,o=s&&s.plugin.domEventObservers;if(r)for(let l in r){let a=r[l];a&&t(l).handlers.push(na(i.value,a))}if(o)for(let l in o){let a=o[l];a&&t(l).observers.push(na(i.value,a))}}for(let i in it)t(i).handlers.push(it[i]);for(let i in Je)t(i).observers.push(Je[i]);return e}const Yc=[{key:"Backspace",keyCode:8,inputType:"deleteContentBackward"},{key:"Enter",keyCode:13,inputType:"insertParagraph"},{key:"Enter",keyCode:13,inputType:"insertLineBreak"},{key:"Delete",keyCode:46,inputType:"deleteContentForward"}],_p="dthko",Jc=[16,17,18,20,91,92,224,225],Dn=6;function Pn(n){return Math.max(0,n)*.7+8}function $p(n,e){return Math.max(Math.abs(n.clientX-e.clientX),Math.abs(n.clientY-e.clientY))}class qp{constructor(e,t,i,s){this.view=e,this.startEvent=t,this.style=i,this.mustSelect=s,this.scrollSpeed={x:0,y:0},this.scrolling=-1,this.lastEvent=t,this.scrollParents=Xd(e.contentDOM),this.atoms=e.state.facet(Yo).map(o=>o(e));let r=e.contentDOM.ownerDocument;r.addEventListener("mousemove",this.move=this.move.bind(this)),r.addEventListener("mouseup",this.up=this.up.bind(this)),this.extend=t.shiftKey,this.multiple=e.state.facet(j.allowMultipleSelections)&&Hp(e,t),this.dragging=jp(e,t)&&tf(t)==1?null:!1}start(e){this.dragging===!1&&this.select(e)}move(e){if(e.buttons==0)return this.destroy();if(this.dragging||this.dragging==null&&$p(this.startEvent,e)<10)return;this.select(this.lastEvent=e);let t=0,i=0,s=0,r=0,o=this.view.win.innerWidth,l=this.view.win.innerHeight;this.scrollParents.x&&({left:s,right:o}=this.scrollParents.x.getBoundingClientRect()),this.scrollParents.y&&({top:r,bottom:l}=this.scrollParents.y.getBoundingClientRect());let a=Jo(this.view);e.clientX-a.left<=s+Dn?t=-Pn(s-e.clientX):e.clientX+a.right>=o-Dn&&(t=Pn(e.clientX-o)),e.clientY-a.top<=r+Dn?i=-Pn(r-e.clientY):e.clientY+a.bottom>=l-Dn&&(i=Pn(e.clientY-l)),this.setScrollSpeed(t,i)}up(e){this.dragging==null&&this.select(this.lastEvent),this.dragging||e.preventDefault(),this.destroy()}destroy(){this.setScrollSpeed(0,0);let e=this.view.contentDOM.ownerDocument;e.removeEventListener("mousemove",this.move),e.removeEventListener("mouseup",this.up),this.view.inputState.mouseSelection=this.view.inputState.draggedContent=null}setScrollSpeed(e,t){this.scrollSpeed={x:e,y:t},e||t?this.scrolling<0&&(this.scrolling=setInterval(()=>this.scroll(),50)):this.scrolling>-1&&(clearInterval(this.scrolling),this.scrolling=-1)}scroll(){let{x:e,y:t}=this.scrollSpeed;e&&this.scrollParents.x&&(this.scrollParents.x.scrollLeft+=e,e=0),t&&this.scrollParents.y&&(this.scrollParents.y.scrollTop+=t,t=0),(e||t)&&this.view.win.scrollBy(e,t),this.dragging===!1&&this.select(this.lastEvent)}skipAtoms(e){let t=null;for(let i=0;i<e.ranges.length;i++){let s=e.ranges[i],r=null;if(s.empty){let o=es(this.atoms,s.from,0);o!=s.from&&(r=E.cursor(o,-1))}else{let o=es(this.atoms,s.from,-1),l=es(this.atoms,s.to,1);(o!=s.from||l!=s.to)&&(r=E.range(s.from==s.anchor?o:l,s.from==s.head?o:l))}r&&(t||(t=e.ranges.slice()),t[i]=r)}return t?E.create(t,e.mainIndex):e}select(e){let{view:t}=this,i=this.skipAtoms(this.style.get(e,this.extend,this.multiple));(this.mustSelect||!i.eq(t.state.selection,this.dragging===!1))&&this.view.dispatch({selection:i,userEvent:"select.pointer"}),this.mustSelect=!1}update(e){e.transactions.some(t=>t.isUserEvent("input.type"))?this.destroy():this.style.update(e)&&setTimeout(()=>this.select(this.lastEvent),20)}}function Hp(n,e){let t=n.state.facet(Bc);return t.length?t[0](e):L.mac?e.metaKey:e.ctrlKey}function zp(n,e){let t=n.state.facet(Ic);return t.length?t[0](e):L.mac?!e.altKey:!e.ctrlKey}function jp(n,e){let{main:t}=n.state.selection;if(t.empty)return!1;let i=nn(n.root);if(!i||i.rangeCount==0)return!0;let s=i.getRangeAt(0).getClientRects();for(let r=0;r<s.length;r++){let o=s[r];if(o.left<=e.clientX&&o.right>=e.clientX&&o.top<=e.clientY&&o.bottom>=e.clientY)return!0}return!1}function Kp(n,e){if(!e.bubbles)return!0;if(e.defaultPrevented)return!1;for(let t=e.target,i;t!=n.contentDOM;t=t.parentNode)if(!t||t.nodeType==11||(i=J.get(t))&&i.ignoreEvent(e))return!1;return!0}const it=Object.create(null),Je=Object.create(null),Zc=L.ie&&L.ie_version<15||L.ios&&L.webkit_version<604;function Up(n){let e=n.dom.parentNode;if(!e)return;let t=e.appendChild(document.createElement("textarea"));t.style.cssText="position: fixed; left: -10000px; top: 10px",t.focus(),setTimeout(()=>{n.focus(),t.remove(),ef(n,t.value)},50)}function Fs(n,e,t){for(let i of n.facet(e))t=i(t,n);return t}function ef(n,e){e=Fs(n.state,Go,e);let{state:t}=n,i,s=1,r=t.toText(e),o=r.lines==t.selection.ranges.length;if(po!=null&&t.selection.ranges.every(a=>a.empty)&&po==r.toString()){let a=-1;i=t.changeByRange(h=>{let c=t.doc.lineAt(h.from);if(c.from==a)return{range:h};a=c.from;let f=t.toText((o?r.line(s++).text:e)+t.lineBreak);return{changes:{from:c.from,insert:f},range:E.cursor(h.from+f.length)}})}else o?i=t.changeByRange(a=>{let h=r.line(s++);return{changes:{from:a.from,to:a.to,insert:h.text},range:E.cursor(a.from+h.length)}}):i=t.replaceSelection(r);n.dispatch(i,{userEvent:"input.paste",scrollIntoView:!0})}Je.scroll=n=>{n.inputState.lastScrollTop=n.scrollDOM.scrollTop,n.inputState.lastScrollLeft=n.scrollDOM.scrollLeft};it.keydown=(n,e)=>(n.inputState.setSelectionOrigin("select"),e.keyCode==27&&n.inputState.tabFocusMode!=0&&(n.inputState.tabFocusMode=Date.now()+2e3),!1);Je.touchstart=(n,e)=>{n.inputState.lastTouchTime=Date.now(),n.inputState.setSelectionOrigin("select.pointer")};Je.touchmove=n=>{n.inputState.setSelectionOrigin("select.pointer")};it.mousedown=(n,e)=>{if(n.observer.flush(),n.inputState.lastTouchTime>Date.now()-2e3)return!1;let t=null;for(let i of n.state.facet(Lc))if(t=i(n,e),t)break;if(!t&&e.button==0&&(t=Qp(n,e)),t){let i=!n.hasFocus;n.inputState.startMouseSelection(new qp(n,e,t,i)),i&&n.observer.ignore(()=>{mc(n.contentDOM);let r=n.root.activeElement;r&&!r.contains(n.contentDOM)&&r.blur()});let s=n.inputState.mouseSelection;if(s)return s.start(e),s.dragging===!1}return!1};function sa(n,e,t,i){if(i==1)return E.cursor(e,t);if(i==2)return Op(n.state,e,t);{let s=oe.find(n.docView,e),r=n.state.doc.lineAt(s?s.posAtEnd:e),o=s?s.posAtStart:r.from,l=s?s.posAtEnd:r.to;return l<n.state.doc.length&&l==r.to&&l++,E.range(o,l)}}let ra=(n,e,t)=>e>=t.top&&e<=t.bottom&&n>=t.left&&n<=t.right;function Gp(n,e,t,i){let s=oe.find(n.docView,e);if(!s)return 1;let r=e-s.posAtStart;if(r==0)return 1;if(r==s.length)return-1;let o=s.coordsAt(r,-1);if(o&&ra(t,i,o))return-1;let l=s.coordsAt(r,1);return l&&ra(t,i,l)?1:o&&o.bottom>=i?-1:1}function oa(n,e){let t=n.posAtCoords({x:e.clientX,y:e.clientY},!1);return{pos:t,bias:Gp(n,t,e.clientX,e.clientY)}}const Xp=L.ie&&L.ie_version<=11;let la=null,aa=0,ha=0;function tf(n){if(!Xp)return n.detail;let e=la,t=ha;return la=n,ha=Date.now(),aa=!e||t>Date.now()-400&&Math.abs(e.clientX-n.clientX)<2&&Math.abs(e.clientY-n.clientY)<2?(aa+1)%3:1}function Qp(n,e){let t=oa(n,e),i=tf(e),s=n.state.selection;return{update(r){r.docChanged&&(t.pos=r.changes.mapPos(t.pos),s=s.map(r.changes))},get(r,o,l){let a=oa(n,r),h,c=sa(n,a.pos,a.bias,i);if(t.pos!=a.pos&&!o){let f=sa(n,t.pos,t.bias,i),u=Math.min(f.from,c.from),d=Math.max(f.to,c.to);c=u<c.from?E.range(u,d):E.range(d,u)}return o?s.replaceRange(s.main.extend(c.from,c.to)):l&&i==1&&s.ranges.length>1&&(h=Yp(s,a.pos))?h:l?s.addRange(c):E.create([c])}}}function Yp(n,e){for(let t=0;t<n.ranges.length;t++){let{from:i,to:s}=n.ranges[t];if(i<=e&&s>=e)return E.create(n.ranges.slice(0,t).concat(n.ranges.slice(t+1)),n.mainIndex==t?0:n.mainIndex-(n.mainIndex>t?1:0))}return null}it.dragstart=(n,e)=>{let{selection:{main:t}}=n.state;if(e.target.draggable){let s=n.docView.nearest(e.target);if(s&&s.isWidget){let r=s.posAtStart,o=r+s.length;(r>=t.to||o<=t.from)&&(t=E.range(r,o))}}let{inputState:i}=n;return i.mouseSelection&&(i.mouseSelection.dragging=!0),i.draggedContent=t,e.dataTransfer&&(e.dataTransfer.setData("Text",Fs(n.state,Xo,n.state.sliceDoc(t.from,t.to))),e.dataTransfer.effectAllowed="copyMove"),!1};it.dragend=n=>(n.inputState.draggedContent=null,!1);function ca(n,e,t,i){if(t=Fs(n.state,Go,t),!t)return;let s=n.posAtCoords({x:e.clientX,y:e.clientY},!1),{draggedContent:r}=n.inputState,o=i&&r&&zp(n,e)?{from:r.from,to:r.to}:null,l={from:s,insert:t},a=n.state.changes(o?[o,l]:l);n.focus(),n.dispatch({changes:a,selection:{anchor:a.mapPos(s,-1),head:a.mapPos(s,1)},userEvent:o?"move.drop":"input.drop"}),n.inputState.draggedContent=null}it.drop=(n,e)=>{if(!e.dataTransfer)return!1;if(n.state.readOnly)return!0;let t=e.dataTransfer.files;if(t&&t.length){let i=Array(t.length),s=0,r=()=>{++s==t.length&&ca(n,e,i.filter(o=>o!=null).join(n.state.lineBreak),!1)};for(let o=0;o<t.length;o++){let l=new FileReader;l.onerror=r,l.onload=()=>{/[\x00-\x08\x0e-\x1f]{2}/.test(l.result)||(i[o]=l.result),r()},l.readAsText(t[o])}return!0}else{let i=e.dataTransfer.getData("Text");if(i)return ca(n,e,i,!0),!0}return!1};it.paste=(n,e)=>{if(n.state.readOnly)return!0;n.observer.flush();let t=Zc?null:e.clipboardData;return t?(ef(n,t.getData("text/plain")||t.getData("text/uri-list")),!0):(Up(n),!1)};function Jp(n,e){let t=n.dom.parentNode;if(!t)return;let i=t.appendChild(document.createElement("textarea"));i.style.cssText="position: fixed; left: -10000px; top: 10px",i.value=e,i.focus(),i.selectionEnd=e.length,i.selectionStart=0,setTimeout(()=>{i.remove(),n.focus()},50)}function Zp(n){let e=[],t=[],i=!1;for(let s of n.selection.ranges)s.empty||(e.push(n.sliceDoc(s.from,s.to)),t.push(s));if(!e.length){let s=-1;for(let{from:r}of n.selection.ranges){let o=n.doc.lineAt(r);o.number>s&&(e.push(o.text),t.push({from:o.from,to:Math.min(n.doc.length,o.to+1)})),s=o.number}i=!0}return{text:Fs(n,Xo,e.join(n.lineBreak)),ranges:t,linewise:i}}let po=null;it.copy=it.cut=(n,e)=>{let{text:t,ranges:i,linewise:s}=Zp(n.state);if(!t&&!s)return!1;po=s?t:null,e.type=="cut"&&!n.state.readOnly&&n.dispatch({changes:i,scrollIntoView:!0,userEvent:"delete.cut"});let r=Zc?null:e.clipboardData;return r?(r.clearData(),r.setData("text/plain",t),!0):(Jp(n,t),!1)};const nf=Mt.define();function sf(n,e){let t=[];for(let i of n.facet(Wc)){let s=i(n,e);s&&t.push(s)}return t.length?n.update({effects:t,annotations:nf.of(!0)}):null}function rf(n){setTimeout(()=>{let e=n.hasFocus;if(e!=n.inputState.notifiedFocused){let t=sf(n.state,e);t?n.dispatch(t):n.update([])}},10)}Je.focus=n=>{n.inputState.lastFocusTime=Date.now(),!n.scrollDOM.scrollTop&&(n.inputState.lastScrollTop||n.inputState.lastScrollLeft)&&(n.scrollDOM.scrollTop=n.inputState.lastScrollTop,n.scrollDOM.scrollLeft=n.inputState.lastScrollLeft),rf(n)};Je.blur=n=>{n.observer.clearSelectionRange(),rf(n)};Je.compositionstart=Je.compositionupdate=n=>{n.observer.editContext||(n.inputState.compositionFirstChange==null&&(n.inputState.compositionFirstChange=!0),n.inputState.composing<0&&(n.inputState.composing=0))};Je.compositionend=n=>{n.observer.editContext||(n.inputState.composing=-1,n.inputState.compositionEndedAt=Date.now(),n.inputState.compositionPendingKey=!0,n.inputState.compositionPendingChange=n.observer.pendingRecords().length>0,n.inputState.compositionFirstChange=null,L.chrome&&L.android?n.observer.flushSoon():n.inputState.compositionPendingChange?Promise.resolve().then(()=>n.observer.flush()):setTimeout(()=>{n.inputState.composing<0&&n.docView.hasComposition&&n.update([])},50))};Je.contextmenu=n=>{n.inputState.lastContextMenu=Date.now()};it.beforeinput=(n,e)=>{var t,i;if(e.inputType=="insertReplacementText"&&n.observer.editContext){let r=(t=e.dataTransfer)===null||t===void 0?void 0:t.getData("text/plain"),o=e.getTargetRanges();if(r&&o.length){let l=o[0],a=n.posAtDOM(l.startContainer,l.startOffset),h=n.posAtDOM(l.endContainer,l.endOffset);return Zo(n,{from:a,to:h,insert:n.state.toText(r)},null),!0}}let s;if(L.chrome&&L.android&&(s=Yc.find(r=>r.inputType==e.inputType))&&(n.observer.delayAndroidKey(s.key,s.keyCode),s.key=="Backspace"||s.key=="Delete")){let r=((i=window.visualViewport)===null||i===void 0?void 0:i.height)||0;setTimeout(()=>{var o;(((o=window.visualViewport)===null||o===void 0?void 0:o.height)||0)>r+10&&n.hasFocus&&(n.contentDOM.blur(),n.focus())},100)}return L.ios&&e.inputType=="deleteContentForward"&&n.observer.flushSoon(),L.safari&&e.inputType=="insertText"&&n.inputState.composing>=0&&setTimeout(()=>Je.compositionend(n,e),20),!1};const fa=new Set;function em(n){fa.has(n)||(fa.add(n),n.addEventListener("copy",()=>{}),n.addEventListener("cut",()=>{}))}const ua=["pre-wrap","normal","pre-line","break-spaces"];let Mi=!1;function da(){Mi=!1}class tm{constructor(e){this.lineWrapping=e,this.doc=G.empty,this.heightSamples={},this.lineHeight=14,this.charWidth=7,this.textHeight=14,this.lineLength=30}heightForGap(e,t){let i=this.doc.lineAt(t).number-this.doc.lineAt(e).number+1;return this.lineWrapping&&(i+=Math.max(0,Math.ceil((t-e-i*this.lineLength*.5)/this.lineLength))),this.lineHeight*i}heightForLine(e){return this.lineWrapping?(1+Math.max(0,Math.ceil((e-this.lineLength)/Math.max(1,this.lineLength-5))))*this.lineHeight:this.lineHeight}setDoc(e){return this.doc=e,this}mustRefreshForWrapping(e){return ua.indexOf(e)>-1!=this.lineWrapping}mustRefreshForHeights(e){let t=!1;for(let i=0;i<e.length;i++){let s=e[i];s<0?i++:this.heightSamples[Math.floor(s*10)]||(t=!0,this.heightSamples[Math.floor(s*10)]=!0)}return t}refresh(e,t,i,s,r,o){let l=ua.indexOf(e)>-1,a=Math.round(t)!=Math.round(this.lineHeight)||this.lineWrapping!=l;if(this.lineWrapping=l,this.lineHeight=t,this.charWidth=i,this.textHeight=s,this.lineLength=r,a){this.heightSamples={};for(let h=0;h<o.length;h++){let c=o[h];c<0?h++:this.heightSamples[Math.floor(c*10)]=!0}}return a}}class im{constructor(e,t){this.from=e,this.heights=t,this.index=0}get more(){return this.index<this.heights.length}}class ut{constructor(e,t,i,s,r){this.from=e,this.length=t,this.top=i,this.height=s,this._content=r}get type(){return typeof this._content=="number"?Pe.Text:Array.isArray(this._content)?this._content:this._content.type}get to(){return this.from+this.length}get bottom(){return this.top+this.height}get widget(){return this._content instanceof Ht?this._content.widget:null}get widgetLineBreaks(){return typeof this._content=="number"?this._content:0}join(e){let t=(Array.isArray(this._content)?this._content:[this]).concat(Array.isArray(e._content)?e._content:[e]);return new ut(this.from,this.length+e.length,this.top,this.height+e.height,t)}}var Z=function(n){return n[n.ByPos=0]="ByPos",n[n.ByHeight=1]="ByHeight",n[n.ByPosNoHeight=2]="ByPosNoHeight",n}(Z||(Z={}));const ts=.001;class Ne{constructor(e,t,i=2){this.length=e,this.height=t,this.flags=i}get outdated(){return(this.flags&2)>0}set outdated(e){this.flags=(e?2:0)|this.flags&-3}setHeight(e){this.height!=e&&(Math.abs(this.height-e)>ts&&(Mi=!0),this.height=e)}replace(e,t,i){return Ne.of(i)}decomposeLeft(e,t){t.push(this)}decomposeRight(e,t){t.push(this)}applyChanges(e,t,i,s){let r=this,o=i.doc;for(let l=s.length-1;l>=0;l--){let{fromA:a,toA:h,fromB:c,toB:f}=s[l],u=r.lineAt(a,Z.ByPosNoHeight,i.setDoc(t),0,0),d=u.to>=h?u:r.lineAt(h,Z.ByPosNoHeight,i,0,0);for(f+=d.to-h,h=d.to;l>0&&u.from<=s[l-1].toA;)a=s[l-1].fromA,c=s[l-1].fromB,l--,a<u.from&&(u=r.lineAt(a,Z.ByPosNoHeight,i,0,0));c+=u.from-a,a=u.from;let m=el.build(i.setDoc(o),e,c,f);r=gs(r,r.replace(a,h,m))}return r.updateHeight(i,0)}static empty(){return new qe(0,0)}static of(e){if(e.length==1)return e[0];let t=0,i=e.length,s=0,r=0;for(;;)if(t==i)if(s>r*2){let l=e[t-1];l.break?e.splice(--t,1,l.left,null,l.right):e.splice(--t,1,l.left,l.right),i+=1+l.break,s-=l.size}else if(r>s*2){let l=e[i];l.break?e.splice(i,1,l.left,null,l.right):e.splice(i,1,l.left,l.right),i+=2+l.break,r-=l.size}else break;else if(s<r){let l=e[t++];l&&(s+=l.size)}else{let l=e[--i];l&&(r+=l.size)}let o=0;return e[t-1]==null?(o=1,t--):e[t]==null&&(o=1,i++),new nm(Ne.of(e.slice(0,t)),o,Ne.of(e.slice(i)))}}function gs(n,e){return n==e?n:(n.constructor!=e.constructor&&(Mi=!0),e)}Ne.prototype.size=1;class of extends Ne{constructor(e,t,i){super(e,t),this.deco=i}blockAt(e,t,i,s){return new ut(s,this.length,i,this.height,this.deco||0)}lineAt(e,t,i,s,r){return this.blockAt(0,i,s,r)}forEachLine(e,t,i,s,r,o){e<=r+this.length&&t>=r&&o(this.blockAt(0,i,s,r))}updateHeight(e,t=0,i=!1,s){return s&&s.from<=t&&s.more&&this.setHeight(s.heights[s.index++]),this.outdated=!1,this}toString(){return`block(${this.length})`}}class qe extends of{constructor(e,t){super(e,t,null),this.collapsed=0,this.widgetHeight=0,this.breaks=0}blockAt(e,t,i,s){return new ut(s,this.length,i,this.height,this.breaks)}replace(e,t,i){let s=i[0];return i.length==1&&(s instanceof qe||s instanceof ve&&s.flags&4)&&Math.abs(this.length-s.length)<10?(s instanceof ve?s=new qe(s.length,this.height):s.height=this.height,this.outdated||(s.outdated=!1),s):Ne.of(i)}updateHeight(e,t=0,i=!1,s){return s&&s.from<=t&&s.more?this.setHeight(s.heights[s.index++]):(i||this.outdated)&&this.setHeight(Math.max(this.widgetHeight,e.heightForLine(this.length-this.collapsed))+this.breaks*e.lineHeight),this.outdated=!1,this}toString(){return`line(${this.length}${this.collapsed?-this.collapsed:""}${this.widgetHeight?":"+this.widgetHeight:""})`}}class ve extends Ne{constructor(e){super(e,0)}heightMetrics(e,t){let i=e.doc.lineAt(t).number,s=e.doc.lineAt(t+this.length).number,r=s-i+1,o,l=0;if(e.lineWrapping){let a=Math.min(this.height,e.lineHeight*r);o=a/r,this.length>r+1&&(l=(this.height-a)/(this.length-r-1))}else o=this.height/r;return{firstLine:i,lastLine:s,perLine:o,perChar:l}}blockAt(e,t,i,s){let{firstLine:r,lastLine:o,perLine:l,perChar:a}=this.heightMetrics(t,s);if(t.lineWrapping){let h=s+(e<t.lineHeight?0:Math.round(Math.max(0,Math.min(1,(e-i)/this.height))*this.length)),c=t.doc.lineAt(h),f=l+c.length*a,u=Math.max(i,e-f/2);return new ut(c.from,c.length,u,f,0)}else{let h=Math.max(0,Math.min(o-r,Math.floor((e-i)/l))),{from:c,length:f}=t.doc.line(r+h);return new ut(c,f,i+l*h,l,0)}}lineAt(e,t,i,s,r){if(t==Z.ByHeight)return this.blockAt(e,i,s,r);if(t==Z.ByPosNoHeight){let{from:d,to:m}=i.doc.lineAt(e);return new ut(d,m-d,0,0,0)}let{firstLine:o,perLine:l,perChar:a}=this.heightMetrics(i,r),h=i.doc.lineAt(e),c=l+h.length*a,f=h.number-o,u=s+l*f+a*(h.from-r-f);return new ut(h.from,h.length,Math.max(s,Math.min(u,s+this.height-c)),c,0)}forEachLine(e,t,i,s,r,o){e=Math.max(e,r),t=Math.min(t,r+this.length);let{firstLine:l,perLine:a,perChar:h}=this.heightMetrics(i,r);for(let c=e,f=s;c<=t;){let u=i.doc.lineAt(c);if(c==e){let m=u.number-l;f+=a*m+h*(e-r-m)}let d=a+h*u.length;o(new ut(u.from,u.length,f,d,0)),f+=d,c=u.to+1}}replace(e,t,i){let s=this.length-t;if(s>0){let r=i[i.length-1];r instanceof ve?i[i.length-1]=new ve(r.length+s):i.push(null,new ve(s-1))}if(e>0){let r=i[0];r instanceof ve?i[0]=new ve(e+r.length):i.unshift(new ve(e-1),null)}return Ne.of(i)}decomposeLeft(e,t){t.push(new ve(e-1),null)}decomposeRight(e,t){t.push(null,new ve(this.length-e-1))}updateHeight(e,t=0,i=!1,s){let r=t+this.length;if(s&&s.from<=t+this.length&&s.more){let o=[],l=Math.max(t,s.from),a=-1;for(s.from>t&&o.push(new ve(s.from-t-1).updateHeight(e,t));l<=r&&s.more;){let c=e.doc.lineAt(l).length;o.length&&o.push(null);let f=s.heights[s.index++];a==-1?a=f:Math.abs(f-a)>=ts&&(a=-2);let u=new qe(c,f);u.outdated=!1,o.push(u),l+=c+1}l<=r&&o.push(null,new ve(r-l).updateHeight(e,l));let h=Ne.of(o);return(a<0||Math.abs(h.height-this.height)>=ts||Math.abs(a-this.heightMetrics(e,t).perLine)>=ts)&&(Mi=!0),gs(this,h)}else(i||this.outdated)&&(this.setHeight(e.heightForGap(t,t+this.length)),this.outdated=!1);return this}toString(){return`gap(${this.length})`}}class nm extends Ne{constructor(e,t,i){super(e.length+t+i.length,e.height+i.height,t|(e.outdated||i.outdated?2:0)),this.left=e,this.right=i,this.size=e.size+i.size}get break(){return this.flags&1}blockAt(e,t,i,s){let r=i+this.left.height;return e<r?this.left.blockAt(e,t,i,s):this.right.blockAt(e,t,r,s+this.left.length+this.break)}lineAt(e,t,i,s,r){let o=s+this.left.height,l=r+this.left.length+this.break,a=t==Z.ByHeight?e<o:e<l,h=a?this.left.lineAt(e,t,i,s,r):this.right.lineAt(e,t,i,o,l);if(this.break||(a?h.to<l:h.from>l))return h;let c=t==Z.ByPosNoHeight?Z.ByPosNoHeight:Z.ByPos;return a?h.join(this.right.lineAt(l,c,i,o,l)):this.left.lineAt(l,c,i,s,r).join(h)}forEachLine(e,t,i,s,r,o){let l=s+this.left.height,a=r+this.left.length+this.break;if(this.break)e<a&&this.left.forEachLine(e,t,i,s,r,o),t>=a&&this.right.forEachLine(e,t,i,l,a,o);else{let h=this.lineAt(a,Z.ByPos,i,s,r);e<h.from&&this.left.forEachLine(e,h.from-1,i,s,r,o),h.to>=e&&h.from<=t&&o(h),t>h.to&&this.right.forEachLine(h.to+1,t,i,l,a,o)}}replace(e,t,i){let s=this.left.length+this.break;if(t<s)return this.balanced(this.left.replace(e,t,i),this.right);if(e>this.left.length)return this.balanced(this.left,this.right.replace(e-s,t-s,i));let r=[];e>0&&this.decomposeLeft(e,r);let o=r.length;for(let l of i)r.push(l);if(e>0&&pa(r,o-1),t<this.length){let l=r.length;this.decomposeRight(t,r),pa(r,l)}return Ne.of(r)}decomposeLeft(e,t){let i=this.left.length;if(e<=i)return this.left.decomposeLeft(e,t);t.push(this.left),this.break&&(i++,e>=i&&t.push(null)),e>i&&this.right.decomposeLeft(e-i,t)}decomposeRight(e,t){let i=this.left.length,s=i+this.break;if(e>=s)return this.right.decomposeRight(e-s,t);e<i&&this.left.decomposeRight(e,t),this.break&&e<s&&t.push(null),t.push(this.right)}balanced(e,t){return e.size>2*t.size||t.size>2*e.size?Ne.of(this.break?[e,null,t]:[e,t]):(this.left=gs(this.left,e),this.right=gs(this.right,t),this.setHeight(e.height+t.height),this.outdated=e.outdated||t.outdated,this.size=e.size+t.size,this.length=e.length+this.break+t.length,this)}updateHeight(e,t=0,i=!1,s){let{left:r,right:o}=this,l=t+r.length+this.break,a=null;return s&&s.from<=t+r.length&&s.more?a=r=r.updateHeight(e,t,i,s):r.updateHeight(e,t,i),s&&s.from<=l+o.length&&s.more?a=o=o.updateHeight(e,l,i,s):o.updateHeight(e,l,i),a?this.balanced(r,o):(this.height=this.left.height+this.right.height,this.outdated=!1,this)}toString(){return this.left+(this.break?" ":"-")+this.right}}function pa(n,e){let t,i;n[e]==null&&(t=n[e-1])instanceof ve&&(i=n[e+1])instanceof ve&&n.splice(e-1,3,new ve(t.length+1+i.length))}const sm=5;class el{constructor(e,t){this.pos=e,this.oracle=t,this.nodes=[],this.lineStart=-1,this.lineEnd=-1,this.covering=null,this.writtenTo=e}get isCovered(){return this.covering&&this.nodes[this.nodes.length-1]==this.covering}span(e,t){if(this.lineStart>-1){let i=Math.min(t,this.lineEnd),s=this.nodes[this.nodes.length-1];s instanceof qe?s.length+=i-this.pos:(i>this.pos||!this.isCovered)&&this.nodes.push(new qe(i-this.pos,-1)),this.writtenTo=i,t>i&&(this.nodes.push(null),this.writtenTo++,this.lineStart=-1)}this.pos=t}point(e,t,i){if(e<t||i.heightRelevant){let s=i.widget?i.widget.estimatedHeight:0,r=i.widget?i.widget.lineBreaks:0;s<0&&(s=this.oracle.lineHeight);let o=t-e;i.block?this.addBlock(new of(o,s,i)):(o||r||s>=sm)&&this.addLineDeco(s,r,o)}else t>e&&this.span(e,t);this.lineEnd>-1&&this.lineEnd<this.pos&&(this.lineEnd=this.oracle.doc.lineAt(this.pos).to)}enterLine(){if(this.lineStart>-1)return;let{from:e,to:t}=this.oracle.doc.lineAt(this.pos);this.lineStart=e,this.lineEnd=t,this.writtenTo<e&&((this.writtenTo<e-1||this.nodes[this.nodes.length-1]==null)&&this.nodes.push(this.blankContent(this.writtenTo,e-1)),this.nodes.push(null)),this.pos>e&&this.nodes.push(new qe(this.pos-e,-1)),this.writtenTo=this.pos}blankContent(e,t){let i=new ve(t-e);return this.oracle.doc.lineAt(e).to==t&&(i.flags|=4),i}ensureLine(){this.enterLine();let e=this.nodes.length?this.nodes[this.nodes.length-1]:null;if(e instanceof qe)return e;let t=new qe(0,-1);return this.nodes.push(t),t}addBlock(e){this.enterLine();let t=e.deco;t&&t.startSide>0&&!this.isCovered&&this.ensureLine(),this.nodes.push(e),this.writtenTo=this.pos=this.pos+e.length,t&&t.endSide>0&&(this.covering=e)}addLineDeco(e,t,i){let s=this.ensureLine();s.length+=i,s.collapsed+=i,s.widgetHeight=Math.max(s.widgetHeight,e),s.breaks+=t,this.writtenTo=this.pos=this.pos+i}finish(e){let t=this.nodes.length==0?null:this.nodes[this.nodes.length-1];this.lineStart>-1&&!(t instanceof qe)&&!this.isCovered?this.nodes.push(new qe(0,-1)):(this.writtenTo<this.pos||t==null)&&this.nodes.push(this.blankContent(this.writtenTo,this.pos));let i=e;for(let s of this.nodes)s instanceof qe&&s.updateHeight(this.oracle,i),i+=s?s.length:1;return this.nodes}static build(e,t,i,s){let r=new el(i,e);return U.spans(t,i,s,r,0),r.finish(i)}}function rm(n,e,t){let i=new om;return U.compare(n,e,t,i,0),i.changes}class om{constructor(){this.changes=[]}compareRange(){}comparePoint(e,t,i,s){(e<t||i&&i.heightRelevant||s&&s.heightRelevant)&&Zn(e,t,this.changes,5)}}function lm(n,e){let t=n.getBoundingClientRect(),i=n.ownerDocument,s=i.defaultView||window,r=Math.max(0,t.left),o=Math.min(s.innerWidth,t.right),l=Math.max(0,t.top),a=Math.min(s.innerHeight,t.bottom);for(let h=n.parentNode;h&&h!=i.body;)if(h.nodeType==1){let c=h,f=window.getComputedStyle(c);if((c.scrollHeight>c.clientHeight||c.scrollWidth>c.clientWidth)&&f.overflow!="visible"){let u=c.getBoundingClientRect();r=Math.max(r,u.left),o=Math.min(o,u.right),l=Math.max(l,u.top),a=Math.min(h==n.parentNode?s.innerHeight:a,u.bottom)}h=f.position=="absolute"||f.position=="fixed"?c.offsetParent:c.parentNode}else if(h.nodeType==11)h=h.host;else break;return{left:r-t.left,right:Math.max(r,o)-t.left,top:l-(t.top+e),bottom:Math.max(l,a)-(t.top+e)}}function am(n){let e=n.getBoundingClientRect(),t=n.ownerDocument.defaultView||window;return e.left<t.innerWidth&&e.right>0&&e.top<t.innerHeight&&e.bottom>0}function hm(n,e){let t=n.getBoundingClientRect();return{left:0,right:t.right-t.left,top:e,bottom:t.bottom-(t.top+e)}}class or{constructor(e,t,i,s){this.from=e,this.to=t,this.size=i,this.displaySize=s}static same(e,t){if(e.length!=t.length)return!1;for(let i=0;i<e.length;i++){let s=e[i],r=t[i];if(s.from!=r.from||s.to!=r.to||s.size!=r.size)return!1}return!0}draw(e,t){return W.replace({widget:new cm(this.displaySize*(t?e.scaleY:e.scaleX),t)}).range(this.from,this.to)}}class cm extends Et{constructor(e,t){super(),this.size=e,this.vertical=t}eq(e){return e.size==this.size&&e.vertical==this.vertical}toDOM(){let e=document.createElement("div");return this.vertical?e.style.height=this.size+"px":(e.style.width=this.size+"px",e.style.height="2px",e.style.display="inline-block"),e}get estimatedHeight(){return this.vertical?this.size:-1}}class ma{constructor(e){this.state=e,this.pixelViewport={left:0,right:window.innerWidth,top:0,bottom:0},this.inView=!0,this.paddingTop=0,this.paddingBottom=0,this.contentDOMWidth=0,this.contentDOMHeight=0,this.editorHeight=0,this.editorWidth=0,this.scrollTop=0,this.scrolledToBottom=!1,this.scaleX=1,this.scaleY=1,this.scrollAnchorPos=0,this.scrollAnchorHeight=-1,this.scaler=ga,this.scrollTarget=null,this.printing=!1,this.mustMeasureContent=!0,this.defaultTextDirection=ee.LTR,this.visibleRanges=[],this.mustEnforceCursorAssoc=!1;let t=e.facet(Qo).some(i=>typeof i!="function"&&i.class=="cm-lineWrapping");this.heightOracle=new tm(t),this.stateDeco=e.facet(sn).filter(i=>typeof i!="function"),this.heightMap=Ne.empty().applyChanges(this.stateDeco,G.empty,this.heightOracle.setDoc(e.doc),[new Qe(0,0,0,e.doc.length)]);for(let i=0;i<2&&(this.viewport=this.getViewport(0,null),!!this.updateForViewport());i++);this.updateViewportLines(),this.lineGaps=this.ensureLineGaps([]),this.lineGapDeco=W.set(this.lineGaps.map(i=>i.draw(this,!1))),this.computeVisibleRanges()}updateForViewport(){let e=[this.viewport],{main:t}=this.state.selection;for(let i=0;i<=1;i++){let s=i?t.head:t.anchor;if(!e.some(({from:r,to:o})=>s>=r&&s<=o)){let{from:r,to:o}=this.lineBlockAt(s);e.push(new Nn(r,o))}}return this.viewports=e.sort((i,s)=>i.from-s.from),this.updateScaler()}updateScaler(){let e=this.scaler;return this.scaler=this.heightMap.height<=7e6?ga:new tl(this.heightOracle,this.heightMap,this.viewports),e.eq(this.scaler)?0:2}updateViewportLines(){this.viewportLines=[],this.heightMap.forEachLine(this.viewport.from,this.viewport.to,this.heightOracle.setDoc(this.state.doc),0,0,e=>{this.viewportLines.push(ji(e,this.scaler))})}update(e,t=null){this.state=e.state;let i=this.stateDeco;this.stateDeco=this.state.facet(sn).filter(c=>typeof c!="function");let s=e.changedRanges,r=Qe.extendWithRanges(s,rm(i,this.stateDeco,e?e.changes:ce.empty(this.state.doc.length))),o=this.heightMap.height,l=this.scrolledToBottom?null:this.scrollAnchorAt(this.scrollTop);da(),this.heightMap=this.heightMap.applyChanges(this.stateDeco,e.startState.doc,this.heightOracle.setDoc(this.state.doc),r),(this.heightMap.height!=o||Mi)&&(e.flags|=2),l?(this.scrollAnchorPos=e.changes.mapPos(l.from,-1),this.scrollAnchorHeight=l.top):(this.scrollAnchorPos=-1,this.scrollAnchorHeight=o);let a=r.length?this.mapViewport(this.viewport,e.changes):this.viewport;(t&&(t.range.head<a.from||t.range.head>a.to)||!this.viewportIsAppropriate(a))&&(a=this.getViewport(0,t));let h=a.from!=this.viewport.from||a.to!=this.viewport.to;this.viewport=a,e.flags|=this.updateForViewport(),(h||!e.changes.empty||e.flags&2)&&this.updateViewportLines(),(this.lineGaps.length||this.viewport.to-this.viewport.from>4e3)&&this.updateLineGaps(this.ensureLineGaps(this.mapLineGaps(this.lineGaps,e.changes))),e.flags|=this.computeVisibleRanges(e.changes),t&&(this.scrollTarget=t),!this.mustEnforceCursorAssoc&&e.selectionSet&&e.view.lineWrapping&&e.state.selection.main.empty&&e.state.selection.main.assoc&&!e.state.facet($c)&&(this.mustEnforceCursorAssoc=!0)}measure(e){let t=e.contentDOM,i=window.getComputedStyle(t),s=this.heightOracle,r=i.whiteSpace;this.defaultTextDirection=i.direction=="rtl"?ee.RTL:ee.LTR;let o=this.heightOracle.mustRefreshForWrapping(r),l=t.getBoundingClientRect(),a=o||this.mustMeasureContent||this.contentDOMHeight!=l.height;this.contentDOMHeight=l.height,this.mustMeasureContent=!1;let h=0,c=0;if(l.width&&l.height){let{scaleX:g,scaleY:k}=pc(t,l);(g>.005&&Math.abs(this.scaleX-g)>.005||k>.005&&Math.abs(this.scaleY-k)>.005)&&(this.scaleX=g,this.scaleY=k,h|=16,o=a=!0)}let f=(parseInt(i.paddingTop)||0)*this.scaleY,u=(parseInt(i.paddingBottom)||0)*this.scaleY;(this.paddingTop!=f||this.paddingBottom!=u)&&(this.paddingTop=f,this.paddingBottom=u,h|=18),this.editorWidth!=e.scrollDOM.clientWidth&&(s.lineWrapping&&(a=!0),this.editorWidth=e.scrollDOM.clientWidth,h|=16);let d=e.scrollDOM.scrollTop*this.scaleY;this.scrollTop!=d&&(this.scrollAnchorHeight=-1,this.scrollTop=d),this.scrolledToBottom=yc(e.scrollDOM);let m=(this.printing?hm:lm)(t,this.paddingTop),x=m.top-this.pixelViewport.top,S=m.bottom-this.pixelViewport.bottom;this.pixelViewport=m;let y=this.pixelViewport.bottom>this.pixelViewport.top&&this.pixelViewport.right>this.pixelViewport.left;if(y!=this.inView&&(this.inView=y,y&&(a=!0)),!this.inView&&!this.scrollTarget&&!am(e.dom))return 0;let O=l.width;if((this.contentDOMWidth!=O||this.editorHeight!=e.scrollDOM.clientHeight)&&(this.contentDOMWidth=l.width,this.editorHeight=e.scrollDOM.clientHeight,h|=16),a){let g=e.docView.measureVisibleLineHeights(this.viewport);if(s.mustRefreshForHeights(g)&&(o=!0),o||s.lineWrapping&&Math.abs(O-this.contentDOMWidth)>s.charWidth){let{lineHeight:k,charWidth:T,textHeight:M}=e.docView.measureTextSize();o=k>0&&s.refresh(r,k,T,M,Math.max(5,O/T),g),o&&(e.docView.minWidth=0,h|=16)}x>0&&S>0?c=Math.max(x,S):x<0&&S<0&&(c=Math.min(x,S)),da();for(let k of this.viewports){let T=k.from==this.viewport.from?g:e.docView.measureVisibleLineHeights(k);this.heightMap=(o?Ne.empty().applyChanges(this.stateDeco,G.empty,this.heightOracle,[new Qe(0,0,0,e.state.doc.length)]):this.heightMap).updateHeight(s,0,o,new im(k.from,T))}Mi&&(h|=2)}let v=!this.viewportIsAppropriate(this.viewport,c)||this.scrollTarget&&(this.scrollTarget.range.head<this.viewport.from||this.scrollTarget.range.head>this.viewport.to);return v&&(h&2&&(h|=this.updateScaler()),this.viewport=this.getViewport(c,this.scrollTarget),h|=this.updateForViewport()),(h&2||v)&&this.updateViewportLines(),(this.lineGaps.length||this.viewport.to-this.viewport.from>4e3)&&this.updateLineGaps(this.ensureLineGaps(o?[]:this.lineGaps,e)),h|=this.computeVisibleRanges(),this.mustEnforceCursorAssoc&&(this.mustEnforceCursorAssoc=!1,e.docView.enforceCursorAssoc()),h}get visibleTop(){return this.scaler.fromDOM(this.pixelViewport.top)}get visibleBottom(){return this.scaler.fromDOM(this.pixelViewport.bottom)}getViewport(e,t){let i=.5-Math.max(-.5,Math.min(.5,e/1e3/2)),s=this.heightMap,r=this.heightOracle,{visibleTop:o,visibleBottom:l}=this,a=new Nn(s.lineAt(o-i*1e3,Z.ByHeight,r,0,0).from,s.lineAt(l+(1-i)*1e3,Z.ByHeight,r,0,0).to);if(t){let{head:h}=t.range;if(h<a.from||h>a.to){let c=Math.min(this.editorHeight,this.pixelViewport.bottom-this.pixelViewport.top),f=s.lineAt(h,Z.ByPos,r,0,0),u;t.y=="center"?u=(f.top+f.bottom)/2-c/2:t.y=="start"||t.y=="nearest"&&h<a.from?u=f.top:u=f.bottom-c,a=new Nn(s.lineAt(u-1e3/2,Z.ByHeight,r,0,0).from,s.lineAt(u+c+1e3/2,Z.ByHeight,r,0,0).to)}}return a}mapViewport(e,t){let i=t.mapPos(e.from,-1),s=t.mapPos(e.to,1);return new Nn(this.heightMap.lineAt(i,Z.ByPos,this.heightOracle,0,0).from,this.heightMap.lineAt(s,Z.ByPos,this.heightOracle,0,0).to)}viewportIsAppropriate({from:e,to:t},i=0){if(!this.inView)return!0;let{top:s}=this.heightMap.lineAt(e,Z.ByPos,this.heightOracle,0,0),{bottom:r}=this.heightMap.lineAt(t,Z.ByPos,this.heightOracle,0,0),{visibleTop:o,visibleBottom:l}=this;return(e==0||s<=o-Math.max(10,Math.min(-i,250)))&&(t==this.state.doc.length||r>=l+Math.max(10,Math.min(i,250)))&&s>o-2*1e3&&r<l+2*1e3}mapLineGaps(e,t){if(!e.length||t.empty)return e;let i=[];for(let s of e)t.touchesRange(s.from,s.to)||i.push(new or(t.mapPos(s.from),t.mapPos(s.to),s.size,s.displaySize));return i}ensureLineGaps(e,t){let i=this.heightOracle.lineWrapping,s=i?1e4:2e3,r=s>>1,o=s<<1;if(this.defaultTextDirection!=ee.LTR&&!i)return[];let l=[],a=(c,f,u,d)=>{if(f-c<r)return;let m=this.state.selection.main,x=[m.from];m.empty||x.push(m.to);for(let y of x)if(y>c&&y<f){a(c,y-10,u,d),a(y+10,f,u,d);return}let S=um(e,y=>y.from>=u.from&&y.to<=u.to&&Math.abs(y.from-c)<r&&Math.abs(y.to-f)<r&&!x.some(O=>y.from<O&&y.to>O));if(!S){if(f<u.to&&t&&i&&t.visibleRanges.some(v=>v.from<=f&&v.to>=f)){let v=t.moveToLineBoundary(E.cursor(f),!1,!0).head;v>c&&(f=v)}let y=this.gapSize(u,c,f,d),O=i||y<2e6?y:2e6;S=new or(c,f,y,O)}l.push(S)},h=c=>{if(c.length<o||c.type!=Pe.Text)return;let f=fm(c.from,c.to,this.stateDeco);if(f.total<o)return;let u=this.scrollTarget?this.scrollTarget.range.head:null,d,m;if(i){let x=s/this.heightOracle.lineLength*this.heightOracle.lineHeight,S,y;if(u!=null){let O=Bn(f,u),v=((this.visibleBottom-this.visibleTop)/2+x)/c.height;S=O-v,y=O+v}else S=(this.visibleTop-c.top-x)/c.height,y=(this.visibleBottom-c.top+x)/c.height;d=Rn(f,S),m=Rn(f,y)}else{let x=f.total*this.heightOracle.charWidth,S=s*this.heightOracle.charWidth,y=0;if(x>2e6)for(let T of e)T.from>=c.from&&T.from<c.to&&T.size!=T.displaySize&&T.from*this.heightOracle.charWidth+y<this.pixelViewport.left&&(y=T.size-T.displaySize);let O=this.pixelViewport.left+y,v=this.pixelViewport.right+y,g,k;if(u!=null){let T=Bn(f,u),M=((v-O)/2+S)/x;g=T-M,k=T+M}else g=(O-S)/x,k=(v+S)/x;d=Rn(f,g),m=Rn(f,k)}d>c.from&&a(c.from,d,c,f),m<c.to&&a(m,c.to,c,f)};for(let c of this.viewportLines)Array.isArray(c.type)?c.type.forEach(h):h(c);return l}gapSize(e,t,i,s){let r=Bn(s,i)-Bn(s,t);return this.heightOracle.lineWrapping?e.height*r:s.total*this.heightOracle.charWidth*r}updateLineGaps(e){or.same(e,this.lineGaps)||(this.lineGaps=e,this.lineGapDeco=W.set(e.map(t=>t.draw(this,this.heightOracle.lineWrapping))))}computeVisibleRanges(e){let t=this.stateDeco;this.lineGaps.length&&(t=t.concat(this.lineGapDeco));let i=[];U.spans(t,this.viewport.from,this.viewport.to,{span(r,o){i.push({from:r,to:o})},point(){}},20);let s=0;if(i.length!=this.visibleRanges.length)s=12;else for(let r=0;r<i.length&&!(s&8);r++){let o=this.visibleRanges[r],l=i[r];(o.from!=l.from||o.to!=l.to)&&(s|=4,e&&e.mapPos(o.from,-1)==l.from&&e.mapPos(o.to,1)==l.to||(s|=8))}return this.visibleRanges=i,s}lineBlockAt(e){return e>=this.viewport.from&&e<=this.viewport.to&&this.viewportLines.find(t=>t.from<=e&&t.to>=e)||ji(this.heightMap.lineAt(e,Z.ByPos,this.heightOracle,0,0),this.scaler)}lineBlockAtHeight(e){return e>=this.viewportLines[0].top&&e<=this.viewportLines[this.viewportLines.length-1].bottom&&this.viewportLines.find(t=>t.top<=e&&t.bottom>=e)||ji(this.heightMap.lineAt(this.scaler.fromDOM(e),Z.ByHeight,this.heightOracle,0,0),this.scaler)}scrollAnchorAt(e){let t=this.lineBlockAtHeight(e+8);return t.from>=this.viewport.from||this.viewportLines[0].top-e>200?t:this.viewportLines[0]}elementAtHeight(e){return ji(this.heightMap.blockAt(this.scaler.fromDOM(e),this.heightOracle,0,0),this.scaler)}get docHeight(){return this.scaler.toDOM(this.heightMap.height)}get contentHeight(){return this.docHeight+this.paddingTop+this.paddingBottom}}class Nn{constructor(e,t){this.from=e,this.to=t}}function fm(n,e,t){let i=[],s=n,r=0;return U.spans(t,n,e,{span(){},point(o,l){o>s&&(i.push({from:s,to:o}),r+=o-s),s=l}},20),s<e&&(i.push({from:s,to:e}),r+=e-s),{total:r,ranges:i}}function Rn({total:n,ranges:e},t){if(t<=0)return e[0].from;if(t>=1)return e[e.length-1].to;let i=Math.floor(n*t);for(let s=0;;s++){let{from:r,to:o}=e[s],l=o-r;if(i<=l)return r+i;i-=l}}function Bn(n,e){let t=0;for(let{from:i,to:s}of n.ranges){if(e<=s){t+=e-i;break}t+=s-i}return t/n.total}function um(n,e){for(let t of n)if(e(t))return t}const ga={toDOM(n){return n},fromDOM(n){return n},scale:1,eq(n){return n==this}};class tl{constructor(e,t,i){let s=0,r=0,o=0;this.viewports=i.map(({from:l,to:a})=>{let h=t.lineAt(l,Z.ByPos,e,0,0).top,c=t.lineAt(a,Z.ByPos,e,0,0).bottom;return s+=c-h,{from:l,to:a,top:h,bottom:c,domTop:0,domBottom:0}}),this.scale=(7e6-s)/(t.height-s);for(let l of this.viewports)l.domTop=o+(l.top-r)*this.scale,o=l.domBottom=l.domTop+(l.bottom-l.top),r=l.bottom}toDOM(e){for(let t=0,i=0,s=0;;t++){let r=t<this.viewports.length?this.viewports[t]:null;if(!r||e<r.top)return s+(e-i)*this.scale;if(e<=r.bottom)return r.domTop+(e-r.top);i=r.bottom,s=r.domBottom}}fromDOM(e){for(let t=0,i=0,s=0;;t++){let r=t<this.viewports.length?this.viewports[t]:null;if(!r||e<r.domTop)return i+(e-s)/this.scale;if(e<=r.domBottom)return r.top+(e-r.domTop);i=r.bottom,s=r.domBottom}}eq(e){return e instanceof tl?this.scale==e.scale&&this.viewports.length==e.viewports.length&&this.viewports.every((t,i)=>t.from==e.viewports[i].from&&t.to==e.viewports[i].to):!1}}function ji(n,e){if(e.scale==1)return n;let t=e.toDOM(n.top),i=e.toDOM(n.bottom);return new ut(n.from,n.length,t,i-t,Array.isArray(n._content)?n._content.map(s=>ji(s,e)):n._content)}const In=F.define({combine:n=>n.join(" ")}),mo=F.define({combine:n=>n.indexOf(!0)>-1}),go=$t.newName(),lf=$t.newName(),af=$t.newName(),hf={"&light":"."+lf,"&dark":"."+af};function yo(n,e,t){return new $t(e,{finish(i){return/&/.test(i)?i.replace(/&\w*/,s=>{if(s=="&")return n;if(!t||!t[s])throw new RangeError(`Unsupported selector: ${s}`);return t[s]}):n+" "+i}})}const dm=yo("."+go,{"&":{position:"relative !important",boxSizing:"border-box","&.cm-focused":{outline:"1px dotted #212121"},display:"flex !important",flexDirection:"column"},".cm-scroller":{display:"flex !important",alignItems:"flex-start !important",fontFamily:"monospace",lineHeight:1.4,height:"100%",overflowX:"auto",position:"relative",zIndex:0,overflowAnchor:"none"},".cm-content":{margin:0,flexGrow:2,flexShrink:0,display:"block",whiteSpace:"pre",wordWrap:"normal",boxSizing:"border-box",minHeight:"100%",padding:"4px 0",outline:"none","&[contenteditable=true]":{WebkitUserModify:"read-write-plaintext-only"}},".cm-lineWrapping":{whiteSpace_fallback:"pre-wrap",whiteSpace:"break-spaces",wordBreak:"break-word",overflowWrap:"anywhere",flexShrink:1},"&light .cm-content":{caretColor:"black"},"&dark .cm-content":{caretColor:"white"},".cm-line":{display:"block",padding:"0 2px 0 6px"},".cm-layer":{position:"absolute",left:0,top:0,contain:"size style","& > *":{position:"absolute"}},"&light .cm-selectionBackground":{background:"#d9d9d9"},"&dark .cm-selectionBackground":{background:"#222"},"&light.cm-focused > .cm-scroller > .cm-selectionLayer .cm-selectionBackground":{background:"#d7d4f0"},"&dark.cm-focused > .cm-scroller > .cm-selectionLayer .cm-selectionBackground":{background:"#233"},".cm-cursorLayer":{pointerEvents:"none"},"&.cm-focused > .cm-scroller > .cm-cursorLayer":{animation:"steps(1) cm-blink 1.2s infinite"},"@keyframes cm-blink":{"0%":{},"50%":{opacity:0},"100%":{}},"@keyframes cm-blink2":{"0%":{},"50%":{opacity:0},"100%":{}},".cm-cursor, .cm-dropCursor":{borderLeft:"1.2px solid black",marginLeft:"-0.6px",pointerEvents:"none"},".cm-cursor":{display:"none"},"&dark .cm-cursor":{borderLeftColor:"#ddd"},".cm-dropCursor":{position:"absolute"},"&.cm-focused > .cm-scroller > .cm-cursorLayer .cm-cursor":{display:"block"},".cm-iso":{unicodeBidi:"isolate"},".cm-announced":{position:"fixed",top:"-10000px"},"@media print":{".cm-announced":{display:"none"}},"&light .cm-activeLine":{backgroundColor:"#cceeff44"},"&dark .cm-activeLine":{backgroundColor:"#99eeff33"},"&light .cm-specialChar":{color:"red"},"&dark .cm-specialChar":{color:"#f78"},".cm-gutters":{flexShrink:0,display:"flex",height:"100%",boxSizing:"border-box",zIndex:200},".cm-gutters-before":{insetInlineStart:0},".cm-gutters-after":{insetInlineEnd:0},"&light .cm-gutters":{backgroundColor:"#f5f5f5",color:"#6c6c6c",border:"0px solid #ddd","&.cm-gutters-before":{borderRightWidth:"1px"},"&.cm-gutters-after":{borderLeftWidth:"1px"}},"&dark .cm-gutters":{backgroundColor:"#333338",color:"#ccc"},".cm-gutter":{display:"flex !important",flexDirection:"column",flexShrink:0,boxSizing:"border-box",minHeight:"100%",overflow:"hidden"},".cm-gutterElement":{boxSizing:"border-box"},".cm-lineNumbers .cm-gutterElement":{padding:"0 3px 0 5px",minWidth:"20px",textAlign:"right",whiteSpace:"nowrap"},"&light .cm-activeLineGutter":{backgroundColor:"#e2f2ff"},"&dark .cm-activeLineGutter":{backgroundColor:"#222227"},".cm-panels":{boxSizing:"border-box",position:"sticky",left:0,right:0,zIndex:300},"&light .cm-panels":{backgroundColor:"#f5f5f5",color:"black"},"&light .cm-panels-top":{borderBottom:"1px solid #ddd"},"&light .cm-panels-bottom":{borderTop:"1px solid #ddd"},"&dark .cm-panels":{backgroundColor:"#333338",color:"white"},".cm-dialog":{padding:"2px 19px 4px 6px",position:"relative","& label":{fontSize:"80%"}},".cm-dialog-close":{position:"absolute",top:"3px",right:"4px",backgroundColor:"inherit",border:"none",font:"inherit",fontSize:"14px",padding:"0"},".cm-tab":{display:"inline-block",overflow:"hidden",verticalAlign:"bottom"},".cm-widgetBuffer":{verticalAlign:"text-top",height:"1em",width:0,display:"inline"},".cm-placeholder":{color:"#888",display:"inline-block",verticalAlign:"top",userSelect:"none"},".cm-highlightSpace":{backgroundImage:"radial-gradient(circle at 50% 55%, #aaa 20%, transparent 5%)",backgroundPosition:"center"},".cm-highlightTab":{backgroundImage:`url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="200" height="20"><path stroke="%23888" stroke-width="1" fill="none" d="M1 10H196L190 5M190 15L196 10M197 4L197 16"/></svg>')`,backgroundSize:"auto 100%",backgroundPosition:"right 90%",backgroundRepeat:"no-repeat"},".cm-trailingSpace":{backgroundColor:"#ff332255"},".cm-button":{verticalAlign:"middle",color:"inherit",fontSize:"70%",padding:".2em 1em",borderRadius:"1px"},"&light .cm-button":{backgroundImage:"linear-gradient(#eff1f5, #d9d9df)",border:"1px solid #888","&:active":{backgroundImage:"linear-gradient(#b4b4b4, #d0d3d6)"}},"&dark .cm-button":{backgroundImage:"linear-gradient(#393939, #111)",border:"1px solid #888","&:active":{backgroundImage:"linear-gradient(#111, #333)"}},".cm-textfield":{verticalAlign:"middle",color:"inherit",fontSize:"70%",border:"1px solid silver",padding:".2em .5em"},"&light .cm-textfield":{backgroundColor:"white"},"&dark .cm-textfield":{border:"1px solid #555",backgroundColor:"inherit"}},hf),pm={childList:!0,characterData:!0,subtree:!0,attributes:!0,characterDataOldValue:!0},lr=L.ie&&L.ie_version<=11;class mm{constructor(e){this.view=e,this.active=!1,this.editContext=null,this.selectionRange=new Qd,this.selectionChanged=!1,this.delayedFlush=-1,this.resizeTimeout=-1,this.queue=[],this.delayedAndroidKey=null,this.flushingAndroidKey=-1,this.lastChange=0,this.scrollTargets=[],this.intersection=null,this.resizeScroll=null,this.intersecting=!1,this.gapIntersection=null,this.gaps=[],this.printQuery=null,this.parentCheck=-1,this.dom=e.contentDOM,this.observer=new MutationObserver(t=>{for(let i of t)this.queue.push(i);(L.ie&&L.ie_version<=11||L.ios&&e.composing)&&t.some(i=>i.type=="childList"&&i.removedNodes.length||i.type=="characterData"&&i.oldValue.length>i.target.nodeValue.length)?this.flushSoon():this.flush()}),window.EditContext&&L.android&&e.constructor.EDIT_CONTEXT!==!1&&!(L.chrome&&L.chrome_version<126)&&(this.editContext=new ym(e),e.state.facet(kt)&&(e.contentDOM.editContext=this.editContext.editContext)),lr&&(this.onCharData=t=>{this.queue.push({target:t.target,type:"characterData",oldValue:t.prevValue}),this.flushSoon()}),this.onSelectionChange=this.onSelectionChange.bind(this),this.onResize=this.onResize.bind(this),this.onPrint=this.onPrint.bind(this),this.onScroll=this.onScroll.bind(this),window.matchMedia&&(this.printQuery=window.matchMedia("print")),typeof ResizeObserver=="function"&&(this.resizeScroll=new ResizeObserver(()=>{var t;((t=this.view.docView)===null||t===void 0?void 0:t.lastUpdate)<Date.now()-75&&this.onResize()}),this.resizeScroll.observe(e.scrollDOM)),this.addWindowListeners(this.win=e.win),this.start(),typeof IntersectionObserver=="function"&&(this.intersection=new IntersectionObserver(t=>{this.parentCheck<0&&(this.parentCheck=setTimeout(this.listenForScroll.bind(this),1e3)),t.length>0&&t[t.length-1].intersectionRatio>0!=this.intersecting&&(this.intersecting=!this.intersecting,this.intersecting!=this.view.inView&&this.onScrollChanged(document.createEvent("Event")))},{threshold:[0,.001]}),this.intersection.observe(this.dom),this.gapIntersection=new IntersectionObserver(t=>{t.length>0&&t[t.length-1].intersectionRatio>0&&this.onScrollChanged(document.createEvent("Event"))},{})),this.listenForScroll(),this.readSelectionRange()}onScrollChanged(e){this.view.inputState.runHandlers("scroll",e),this.intersecting&&this.view.measure()}onScroll(e){this.intersecting&&this.flush(!1),this.editContext&&this.view.requestMeasure(this.editContext.measureReq),this.onScrollChanged(e)}onResize(){this.resizeTimeout<0&&(this.resizeTimeout=setTimeout(()=>{this.resizeTimeout=-1,this.view.requestMeasure()},50))}onPrint(e){(e.type=="change"||!e.type)&&!e.matches||(this.view.viewState.printing=!0,this.view.measure(),setTimeout(()=>{this.view.viewState.printing=!1,this.view.requestMeasure()},500))}updateGaps(e){if(this.gapIntersection&&(e.length!=this.gaps.length||this.gaps.some((t,i)=>t!=e[i]))){this.gapIntersection.disconnect();for(let t of e)this.gapIntersection.observe(t);this.gaps=e}}onSelectionChange(e){let t=this.selectionChanged;if(!this.readSelectionRange()||this.delayedAndroidKey)return;let{view:i}=this,s=this.selectionRange;if(i.state.facet(kt)?i.root.activeElement!=this.dom:!Jn(this.dom,s))return;let r=s.anchorNode&&i.docView.nearest(s.anchorNode);if(r&&r.ignoreEvent(e)){t||(this.selectionChanged=!1);return}(L.ie&&L.ie_version<=11||L.android&&L.chrome)&&!i.state.selection.main.empty&&s.focusNode&&Xi(s.focusNode,s.focusOffset,s.anchorNode,s.anchorOffset)?this.flushSoon():this.flush(!1)}readSelectionRange(){let{view:e}=this,t=nn(e.root);if(!t)return!1;let i=L.safari&&e.root.nodeType==11&&e.root.activeElement==this.dom&&gm(this.view,t)||t;if(!i||this.selectionRange.eq(i))return!1;let s=Jn(this.dom,i);return s&&!this.selectionChanged&&e.inputState.lastFocusTime>Date.now()-200&&e.inputState.lastTouchTime<Date.now()-300&&Jd(this.dom,i)?(this.view.inputState.lastFocusTime=0,e.docView.updateSelection(),!1):(this.selectionRange.setRange(i),s&&(this.selectionChanged=!0),!0)}setSelectionRange(e,t){this.selectionRange.set(e.node,e.offset,t.node,t.offset),this.selectionChanged=!1}clearSelectionRange(){this.selectionRange.set(null,0,null,0)}listenForScroll(){this.parentCheck=-1;let e=0,t=null;for(let i=this.dom;i;)if(i.nodeType==1)!t&&e<this.scrollTargets.length&&this.scrollTargets[e]==i?e++:t||(t=this.scrollTargets.slice(0,e)),t&&t.push(i),i=i.assignedSlot||i.parentNode;else if(i.nodeType==11)i=i.host;else break;if(e<this.scrollTargets.length&&!t&&(t=this.scrollTargets.slice(0,e)),t){for(let i of this.scrollTargets)i.removeEventListener("scroll",this.onScroll);for(let i of this.scrollTargets=t)i.addEventListener("scroll",this.onScroll)}}ignore(e){if(!this.active)return e();try{return this.stop(),e()}finally{this.start(),this.clear()}}start(){this.active||(this.observer.observe(this.dom,pm),lr&&this.dom.addEventListener("DOMCharacterDataModified",this.onCharData),this.active=!0)}stop(){this.active&&(this.active=!1,this.observer.disconnect(),lr&&this.dom.removeEventListener("DOMCharacterDataModified",this.onCharData))}clear(){this.processRecords(),this.queue.length=0,this.selectionChanged=!1}delayAndroidKey(e,t){var i;if(!this.delayedAndroidKey){let s=()=>{let r=this.delayedAndroidKey;r&&(this.clearDelayedAndroidKey(),this.view.inputState.lastKeyCode=r.keyCode,this.view.inputState.lastKeyTime=Date.now(),!this.flush()&&r.force&&xi(this.dom,r.key,r.keyCode))};this.flushingAndroidKey=this.view.win.requestAnimationFrame(s)}(!this.delayedAndroidKey||e=="Enter")&&(this.delayedAndroidKey={key:e,keyCode:t,force:this.lastChange<Date.now()-50||!!(!((i=this.delayedAndroidKey)===null||i===void 0)&&i.force)})}clearDelayedAndroidKey(){this.win.cancelAnimationFrame(this.flushingAndroidKey),this.delayedAndroidKey=null,this.flushingAndroidKey=-1}flushSoon(){this.delayedFlush<0&&(this.delayedFlush=this.view.win.requestAnimationFrame(()=>{this.delayedFlush=-1,this.flush()}))}forceFlush(){this.delayedFlush>=0&&(this.view.win.cancelAnimationFrame(this.delayedFlush),this.delayedFlush=-1),this.flush()}pendingRecords(){for(let e of this.observer.takeRecords())this.queue.push(e);return this.queue}processRecords(){let e=this.pendingRecords();e.length&&(this.queue=[]);let t=-1,i=-1,s=!1;for(let r of e){let o=this.readMutation(r);o&&(o.typeOver&&(s=!0),t==-1?{from:t,to:i}=o:(t=Math.min(o.from,t),i=Math.max(o.to,i)))}return{from:t,to:i,typeOver:s}}readChange(){let{from:e,to:t,typeOver:i}=this.processRecords(),s=this.selectionChanged&&Jn(this.dom,this.selectionRange);if(e<0&&!s)return null;e>-1&&(this.lastChange=Date.now()),this.view.inputState.lastFocusTime=0,this.selectionChanged=!1;let r=new Rp(this.view,e,t,i);return this.view.docView.domChanged={newSel:r.newSel?r.newSel.main:null},r}flush(e=!0){if(this.delayedFlush>=0||this.delayedAndroidKey)return!1;e&&this.readSelectionRange();let t=this.readChange();if(!t)return this.view.requestMeasure(),!1;let i=this.view.state,s=Qc(this.view,t);return this.view.state==i&&(t.domChanged||t.newSel&&!t.newSel.main.eq(this.view.state.selection.main))&&this.view.update([]),s}readMutation(e){let t=this.view.docView.nearest(e.target);if(!t||t.ignoreMutation(e))return null;if(t.markDirty(e.type=="attributes"),e.type=="attributes"&&(t.flags|=4),e.type=="childList"){let i=ya(t,e.previousSibling||e.target.previousSibling,-1),s=ya(t,e.nextSibling||e.target.nextSibling,1);return{from:i?t.posAfter(i):t.posAtStart,to:s?t.posBefore(s):t.posAtEnd,typeOver:!1}}else return e.type=="characterData"?{from:t.posAtStart,to:t.posAtEnd,typeOver:e.target.nodeValue==e.oldValue}:null}setWindow(e){e!=this.win&&(this.removeWindowListeners(this.win),this.win=e,this.addWindowListeners(this.win))}addWindowListeners(e){e.addEventListener("resize",this.onResize),this.printQuery?this.printQuery.addEventListener?this.printQuery.addEventListener("change",this.onPrint):this.printQuery.addListener(this.onPrint):e.addEventListener("beforeprint",this.onPrint),e.addEventListener("scroll",this.onScroll),e.document.addEventListener("selectionchange",this.onSelectionChange)}removeWindowListeners(e){e.removeEventListener("scroll",this.onScroll),e.removeEventListener("resize",this.onResize),this.printQuery?this.printQuery.removeEventListener?this.printQuery.removeEventListener("change",this.onPrint):this.printQuery.removeListener(this.onPrint):e.removeEventListener("beforeprint",this.onPrint),e.document.removeEventListener("selectionchange",this.onSelectionChange)}update(e){this.editContext&&(this.editContext.update(e),e.startState.facet(kt)!=e.state.facet(kt)&&(e.view.contentDOM.editContext=e.state.facet(kt)?this.editContext.editContext:null))}destroy(){var e,t,i;this.stop(),(e=this.intersection)===null||e===void 0||e.disconnect(),(t=this.gapIntersection)===null||t===void 0||t.disconnect(),(i=this.resizeScroll)===null||i===void 0||i.disconnect();for(let s of this.scrollTargets)s.removeEventListener("scroll",this.onScroll);this.removeWindowListeners(this.win),clearTimeout(this.parentCheck),clearTimeout(this.resizeTimeout),this.win.cancelAnimationFrame(this.delayedFlush),this.win.cancelAnimationFrame(this.flushingAndroidKey),this.editContext&&(this.view.contentDOM.editContext=null,this.editContext.destroy())}}function ya(n,e,t){for(;e;){let i=J.get(e);if(i&&i.parent==n)return i;let s=e.parentNode;e=s!=n.dom?s:t>0?e.nextSibling:e.previousSibling}return null}function ba(n,e){let t=e.startContainer,i=e.startOffset,s=e.endContainer,r=e.endOffset,o=n.docView.domAtPos(n.state.selection.main.anchor);return Xi(o.node,o.offset,s,r)&&([t,i,s,r]=[s,r,t,i]),{anchorNode:t,anchorOffset:i,focusNode:s,focusOffset:r}}function gm(n,e){if(e.getComposedRanges){let s=e.getComposedRanges(n.root)[0];if(s)return ba(n,s)}let t=null;function i(s){s.preventDefault(),s.stopImmediatePropagation(),t=s.getTargetRanges()[0]}return n.contentDOM.addEventListener("beforeinput",i,!0),n.dom.ownerDocument.execCommand("indent"),n.contentDOM.removeEventListener("beforeinput",i,!0),t?ba(n,t):null}class ym{constructor(e){this.from=0,this.to=0,this.pendingContextChange=null,this.handlers=Object.create(null),this.composing=null,this.resetRange(e.state);let t=this.editContext=new window.EditContext({text:e.state.doc.sliceString(this.from,this.to),selectionStart:this.toContextPos(Math.max(this.from,Math.min(this.to,e.state.selection.main.anchor))),selectionEnd:this.toContextPos(e.state.selection.main.head)});this.handlers.textupdate=i=>{let s=e.state.selection.main,{anchor:r,head:o}=s,l=this.toEditorPos(i.updateRangeStart),a=this.toEditorPos(i.updateRangeEnd);e.inputState.composing>=0&&!this.composing&&(this.composing={contextBase:i.updateRangeStart,editorBase:l,drifted:!1});let h={from:l,to:a,insert:G.of(i.text.split(`
`))};if(h.from==this.from&&r<this.from?h.from=r:h.to==this.to&&r>this.to&&(h.to=r),h.from==h.to&&!h.insert.length){let c=E.single(this.toEditorPos(i.selectionStart),this.toEditorPos(i.selectionEnd));c.main.eq(s)||e.dispatch({selection:c,userEvent:"select"});return}if((L.mac||L.android)&&h.from==o-1&&/^\. ?$/.test(i.text)&&e.contentDOM.getAttribute("autocorrect")=="off"&&(h={from:l,to:a,insert:G.of([i.text.replace("."," ")])}),this.pendingContextChange=h,!e.state.readOnly){let c=this.to-this.from+(h.to-h.from+h.insert.length);Zo(e,h,E.single(this.toEditorPos(i.selectionStart,c),this.toEditorPos(i.selectionEnd,c)))}this.pendingContextChange&&(this.revertPending(e.state),this.setSelection(e.state))},this.handlers.characterboundsupdate=i=>{let s=[],r=null;for(let o=this.toEditorPos(i.rangeStart),l=this.toEditorPos(i.rangeEnd);o<l;o++){let a=e.coordsForChar(o);r=a&&new DOMRect(a.left,a.top,a.right-a.left,a.bottom-a.top)||r||new DOMRect,s.push(r)}t.updateCharacterBounds(i.rangeStart,s)},this.handlers.textformatupdate=i=>{let s=[];for(let r of i.getTextFormats()){let o=r.underlineStyle,l=r.underlineThickness;if(o!="None"&&l!="None"){let a=this.toEditorPos(r.rangeStart),h=this.toEditorPos(r.rangeEnd);if(a<h){let c=`text-decoration: underline ${o=="Dashed"?"dashed ":o=="Squiggle"?"wavy ":""}${l=="Thin"?1:2}px`;s.push(W.mark({attributes:{style:c}}).range(a,h))}}}e.dispatch({effects:Hc.of(W.set(s))})},this.handlers.compositionstart=()=>{e.inputState.composing<0&&(e.inputState.composing=0,e.inputState.compositionFirstChange=!0)},this.handlers.compositionend=()=>{if(e.inputState.composing=-1,e.inputState.compositionFirstChange=null,this.composing){let{drifted:i}=this.composing;this.composing=null,i&&this.reset(e.state)}};for(let i in this.handlers)t.addEventListener(i,this.handlers[i]);this.measureReq={read:i=>{this.editContext.updateControlBounds(i.contentDOM.getBoundingClientRect());let s=nn(i.root);s&&s.rangeCount&&this.editContext.updateSelectionBounds(s.getRangeAt(0).getBoundingClientRect())}}}applyEdits(e){let t=0,i=!1,s=this.pendingContextChange;return e.changes.iterChanges((r,o,l,a,h)=>{if(i)return;let c=h.length-(o-r);if(s&&o>=s.to)if(s.from==r&&s.to==o&&s.insert.eq(h)){s=this.pendingContextChange=null,t+=c,this.to+=c;return}else s=null,this.revertPending(e.state);if(r+=t,o+=t,o<=this.from)this.from+=c,this.to+=c;else if(r<this.to){if(r<this.from||o>this.to||this.to-this.from+h.length>3e4){i=!0;return}this.editContext.updateText(this.toContextPos(r),this.toContextPos(o),h.toString()),this.to+=c}t+=c}),s&&!i&&this.revertPending(e.state),!i}update(e){let t=this.pendingContextChange,i=e.startState.selection.main;this.composing&&(this.composing.drifted||!e.changes.touchesRange(i.from,i.to)&&e.transactions.some(s=>!s.isUserEvent("input.type")&&s.changes.touchesRange(this.from,this.to)))?(this.composing.drifted=!0,this.composing.editorBase=e.changes.mapPos(this.composing.editorBase)):!this.applyEdits(e)||!this.rangeIsValid(e.state)?(this.pendingContextChange=null,this.reset(e.state)):(e.docChanged||e.selectionSet||t)&&this.setSelection(e.state),(e.geometryChanged||e.docChanged||e.selectionSet)&&e.view.requestMeasure(this.measureReq)}resetRange(e){let{head:t}=e.selection.main;this.from=Math.max(0,t-1e4),this.to=Math.min(e.doc.length,t+1e4)}reset(e){this.resetRange(e),this.editContext.updateText(0,this.editContext.text.length,e.doc.sliceString(this.from,this.to)),this.setSelection(e)}revertPending(e){let t=this.pendingContextChange;this.pendingContextChange=null,this.editContext.updateText(this.toContextPos(t.from),this.toContextPos(t.from+t.insert.length),e.doc.sliceString(t.from,t.to))}setSelection(e){let{main:t}=e.selection,i=this.toContextPos(Math.max(this.from,Math.min(this.to,t.anchor))),s=this.toContextPos(t.head);(this.editContext.selectionStart!=i||this.editContext.selectionEnd!=s)&&this.editContext.updateSelection(i,s)}rangeIsValid(e){let{head:t}=e.selection.main;return!(this.from>0&&t-this.from<500||this.to<e.doc.length&&this.to-t<500||this.to-this.from>1e4*3)}toEditorPos(e,t=this.to-this.from){e=Math.min(e,t);let i=this.composing;return i&&i.drifted?i.editorBase+(e-i.contextBase):e+this.from}toContextPos(e){let t=this.composing;return t&&t.drifted?t.contextBase+(e-t.editorBase):e-this.from}destroy(){for(let e in this.handlers)this.editContext.removeEventListener(e,this.handlers[e])}}class R{get state(){return this.viewState.state}get viewport(){return this.viewState.viewport}get visibleRanges(){return this.viewState.visibleRanges}get inView(){return this.viewState.inView}get composing(){return!!this.inputState&&this.inputState.composing>0}get compositionStarted(){return!!this.inputState&&this.inputState.composing>=0}get root(){return this._root}get win(){return this.dom.ownerDocument.defaultView||window}constructor(e={}){var t;this.plugins=[],this.pluginMap=new Map,this.editorAttrs={},this.contentAttrs={},this.bidiCache=[],this.destroyed=!1,this.updateState=2,this.measureScheduled=-1,this.measureRequests=[],this.contentDOM=document.createElement("div"),this.scrollDOM=document.createElement("div"),this.scrollDOM.tabIndex=-1,this.scrollDOM.className="cm-scroller",this.scrollDOM.appendChild(this.contentDOM),this.announceDOM=document.createElement("div"),this.announceDOM.className="cm-announced",this.announceDOM.setAttribute("aria-live","polite"),this.dom=document.createElement("div"),this.dom.appendChild(this.announceDOM),this.dom.appendChild(this.scrollDOM),e.parent&&e.parent.appendChild(this.dom);let{dispatch:i}=e;this.dispatchTransactions=e.dispatchTransactions||i&&(s=>s.forEach(r=>i(r,this)))||(s=>this.update(s)),this.dispatch=this.dispatch.bind(this),this._root=e.root||Yd(e.parent)||document,this.viewState=new ma(e.state||j.create(e)),e.scrollTo&&e.scrollTo.is(En)&&(this.viewState.scrollTarget=e.scrollTo.value.clip(this.viewState.state)),this.plugins=this.state.facet(pi).map(s=>new nr(s));for(let s of this.plugins)s.update(this);this.observer=new mm(this),this.inputState=new Vp(this),this.inputState.ensureHandlers(this.plugins),this.docView=new Ql(this),this.mountStyles(),this.updateAttrs(),this.updateState=0,this.requestMeasure(),!((t=document.fonts)===null||t===void 0)&&t.ready&&document.fonts.ready.then(()=>this.requestMeasure())}dispatch(...e){let t=e.length==1&&e[0]instanceof fe?e:e.length==1&&Array.isArray(e[0])?e[0]:[this.state.update(...e)];this.dispatchTransactions(t,this)}update(e){if(this.updateState!=0)throw new Error("Calls to EditorView.update are not allowed while an update is in progress");let t=!1,i=!1,s,r=this.state;for(let u of e){if(u.startState!=r)throw new RangeError("Trying to update state with a transaction that doesn't start from the previous state.");r=u.state}if(this.destroyed){this.viewState.state=r;return}let o=this.hasFocus,l=0,a=null;e.some(u=>u.annotation(nf))?(this.inputState.notifiedFocused=o,l=1):o!=this.inputState.notifiedFocused&&(this.inputState.notifiedFocused=o,a=sf(r,o),a||(l=1));let h=this.observer.delayedAndroidKey,c=null;if(h?(this.observer.clearDelayedAndroidKey(),c=this.observer.readChange(),(c&&!this.state.doc.eq(r.doc)||!this.state.selection.eq(r.selection))&&(c=null)):this.observer.clear(),r.facet(j.phrases)!=this.state.facet(j.phrases))return this.setState(r);s=ms.create(this,r,e),s.flags|=l;let f=this.viewState.scrollTarget;try{this.updateState=2;for(let u of e){if(f&&(f=f.map(u.changes)),u.scrollIntoView){let{main:d}=u.state.selection;f=new vi(d.empty?d:E.cursor(d.head,d.head>d.anchor?-1:1))}for(let d of u.effects)d.is(En)&&(f=d.value.clip(this.state))}this.viewState.update(s,f),this.bidiCache=ys.update(this.bidiCache,s.changes),s.empty||(this.updatePlugins(s),this.inputState.update(s)),t=this.docView.update(s),this.state.facet(Hi)!=this.styleModules&&this.mountStyles(),i=this.updateAttrs(),this.showAnnouncements(e),this.docView.updateSelection(t,e.some(u=>u.isUserEvent("select.pointer")))}finally{this.updateState=0}if(s.startState.facet(In)!=s.state.facet(In)&&(this.viewState.mustMeasureContent=!0),(t||i||f||this.viewState.mustEnforceCursorAssoc||this.viewState.mustMeasureContent)&&this.requestMeasure(),t&&this.docViewUpdate(),!s.empty)for(let u of this.state.facet(co))try{u(s)}catch(d){De(this.state,d,"update listener")}(a||c)&&Promise.resolve().then(()=>{a&&this.state==a.startState&&this.dispatch(a),c&&!Qc(this,c)&&h.force&&xi(this.contentDOM,h.key,h.keyCode)})}setState(e){if(this.updateState!=0)throw new Error("Calls to EditorView.setState are not allowed while an update is in progress");if(this.destroyed){this.viewState.state=e;return}this.updateState=2;let t=this.hasFocus;try{for(let i of this.plugins)i.destroy(this);this.viewState=new ma(e),this.plugins=e.facet(pi).map(i=>new nr(i)),this.pluginMap.clear();for(let i of this.plugins)i.update(this);this.docView.destroy(),this.docView=new Ql(this),this.inputState.ensureHandlers(this.plugins),this.mountStyles(),this.updateAttrs(),this.bidiCache=[]}finally{this.updateState=0}t&&this.focus(),this.requestMeasure()}updatePlugins(e){let t=e.startState.facet(pi),i=e.state.facet(pi);if(t!=i){let s=[];for(let r of i){let o=t.indexOf(r);if(o<0)s.push(new nr(r));else{let l=this.plugins[o];l.mustUpdate=e,s.push(l)}}for(let r of this.plugins)r.mustUpdate!=e&&r.destroy(this);this.plugins=s,this.pluginMap.clear()}else for(let s of this.plugins)s.mustUpdate=e;for(let s=0;s<this.plugins.length;s++)this.plugins[s].update(this);t!=i&&this.inputState.ensureHandlers(this.plugins)}docViewUpdate(){for(let e of this.plugins){let t=e.value;if(t&&t.docViewUpdate)try{t.docViewUpdate(this)}catch(i){De(this.state,i,"doc view update listener")}}}measure(e=!0){if(this.destroyed)return;if(this.measureScheduled>-1&&this.win.cancelAnimationFrame(this.measureScheduled),this.observer.delayedAndroidKey){this.measureScheduled=-1,this.requestMeasure();return}this.measureScheduled=0,e&&this.observer.forceFlush();let t=null,i=this.scrollDOM,s=i.scrollTop*this.scaleY,{scrollAnchorPos:r,scrollAnchorHeight:o}=this.viewState;Math.abs(s-this.viewState.scrollTop)>1&&(o=-1),this.viewState.scrollAnchorHeight=-1;try{for(let l=0;;l++){if(o<0)if(yc(i))r=-1,o=this.viewState.heightMap.height;else{let d=this.viewState.scrollAnchorAt(s);r=d.from,o=d.top}this.updateState=1;let a=this.viewState.measure(this);if(!a&&!this.measureRequests.length&&this.viewState.scrollTarget==null)break;if(l>5){console.warn(this.measureRequests.length?"Measure loop restarted more than 5 times":"Viewport failed to stabilize");break}let h=[];a&4||([this.measureRequests,h]=[h,this.measureRequests]);let c=h.map(d=>{try{return d.read(this)}catch(m){return De(this.state,m),xa}}),f=ms.create(this,this.state,[]),u=!1;f.flags|=a,t?t.flags|=a:t=f,this.updateState=2,f.empty||(this.updatePlugins(f),this.inputState.update(f),this.updateAttrs(),u=this.docView.update(f),u&&this.docViewUpdate());for(let d=0;d<h.length;d++)if(c[d]!=xa)try{let m=h[d];m.write&&m.write(c[d],this)}catch(m){De(this.state,m)}if(u&&this.docView.updateSelection(!0),!f.viewportChanged&&this.measureRequests.length==0){if(this.viewState.editorHeight)if(this.viewState.scrollTarget){this.docView.scrollIntoView(this.viewState.scrollTarget),this.viewState.scrollTarget=null,o=-1;continue}else{let m=(r<0?this.viewState.heightMap.height:this.viewState.lineBlockAt(r).top)-o;if(m>1||m<-1){s=s+m,i.scrollTop=s/this.scaleY,o=-1;continue}}break}}}finally{this.updateState=0,this.measureScheduled=-1}if(t&&!t.empty)for(let l of this.state.facet(co))l(t)}get themeClasses(){return go+" "+(this.state.facet(mo)?af:lf)+" "+this.state.facet(In)}updateAttrs(){let e=va(this,zc,{class:"cm-editor"+(this.hasFocus?" cm-focused ":" ")+this.themeClasses}),t={spellcheck:"false",autocorrect:"off",autocapitalize:"off",writingsuggestions:"false",translate:"no",contenteditable:this.state.facet(kt)?"true":"false",class:"cm-content",style:`${L.tabSize}: ${this.state.tabSize}`,role:"textbox","aria-multiline":"true"};this.state.readOnly&&(t["aria-readonly"]="true"),va(this,Qo,t);let i=this.observer.ignore(()=>{let s=ro(this.contentDOM,this.contentAttrs,t),r=ro(this.dom,this.editorAttrs,e);return s||r});return this.editorAttrs=e,this.contentAttrs=t,i}showAnnouncements(e){let t=!0;for(let i of e)for(let s of i.effects)if(s.is(R.announce)){t&&(this.announceDOM.textContent=""),t=!1;let r=this.announceDOM.appendChild(document.createElement("div"));r.textContent=s.value}}mountStyles(){this.styleModules=this.state.facet(Hi);let e=this.state.facet(R.cspNonce);$t.mount(this.root,this.styleModules.concat(dm).reverse(),e?{nonce:e}:void 0)}readMeasured(){if(this.updateState==2)throw new Error("Reading the editor layout isn't allowed during an update");this.updateState==0&&this.measureScheduled>-1&&this.measure(!1)}requestMeasure(e){if(this.measureScheduled<0&&(this.measureScheduled=this.win.requestAnimationFrame(()=>this.measure())),e){if(this.measureRequests.indexOf(e)>-1)return;if(e.key!=null){for(let t=0;t<this.measureRequests.length;t++)if(this.measureRequests[t].key===e.key){this.measureRequests[t]=e;return}}this.measureRequests.push(e)}}plugin(e){let t=this.pluginMap.get(e);return(t===void 0||t&&t.plugin!=e)&&this.pluginMap.set(e,t=this.plugins.find(i=>i.plugin==e)||null),t&&t.update(this).value}get documentTop(){return this.contentDOM.getBoundingClientRect().top+this.viewState.paddingTop}get documentPadding(){return{top:this.viewState.paddingTop,bottom:this.viewState.paddingBottom}}get scaleX(){return this.viewState.scaleX}get scaleY(){return this.viewState.scaleY}elementAtHeight(e){return this.readMeasured(),this.viewState.elementAtHeight(e)}lineBlockAtHeight(e){return this.readMeasured(),this.viewState.lineBlockAtHeight(e)}get viewportLineBlocks(){return this.viewState.viewportLines}lineBlockAt(e){return this.viewState.lineBlockAt(e)}get contentHeight(){return this.viewState.contentHeight}moveByChar(e,t,i){return rr(this,e,ta(this,e,t,i))}moveByGroup(e,t){return rr(this,e,ta(this,e,t,i=>Ep(this,e.head,i)))}visualLineSide(e,t){let i=this.bidiSpans(e),s=this.textDirectionAt(e.from),r=i[t?i.length-1:0];return E.cursor(r.side(t,s)+e.from,r.forward(!t,s)?1:-1)}moveToLineBoundary(e,t,i=!0){return Mp(this,e,t,i)}moveVertically(e,t,i){return rr(this,e,Dp(this,e,t,i))}domAtPos(e){return this.docView.domAtPos(e)}posAtDOM(e,t=0){return this.docView.posFromDOM(e,t)}posAtCoords(e,t=!0){return this.readMeasured(),Xc(this,e,t)}coordsAtPos(e,t=1){this.readMeasured();let i=this.docView.coordsAt(e,t);if(!i||i.left==i.right)return i;let s=this.state.doc.lineAt(e),r=this.bidiSpans(s),o=r[Vt.find(r,e-s.from,-1,t)];return yn(i,o.dir==ee.LTR==t>0)}coordsForChar(e){return this.readMeasured(),this.docView.coordsForChar(e)}get defaultCharacterWidth(){return this.viewState.heightOracle.charWidth}get defaultLineHeight(){return this.viewState.heightOracle.lineHeight}get textDirection(){return this.viewState.defaultTextDirection}textDirectionAt(e){return!this.state.facet(_c)||e<this.viewport.from||e>this.viewport.to?this.textDirection:(this.readMeasured(),this.docView.textDirectionAt(e))}get lineWrapping(){return this.viewState.heightOracle.lineWrapping}bidiSpans(e){if(e.length>bm)return Nc(e.length);let t=this.textDirectionAt(e.from),i;for(let r of this.bidiCache)if(r.from==e.from&&r.dir==t&&(r.fresh||Pc(r.isolates,i=Xl(this,e))))return r.order;i||(i=Xl(this,e));let s=up(e.text,t,i);return this.bidiCache.push(new ys(e.from,e.to,t,i,!0,s)),s}get hasFocus(){var e;return(this.dom.ownerDocument.hasFocus()||L.safari&&((e=this.inputState)===null||e===void 0?void 0:e.lastContextMenu)>Date.now()-3e4)&&this.root.activeElement==this.contentDOM}focus(){this.observer.ignore(()=>{mc(this.contentDOM),this.docView.updateSelection()})}setRoot(e){this._root!=e&&(this._root=e,this.observer.setWindow((e.nodeType==9?e:e.ownerDocument).defaultView||window),this.mountStyles())}destroy(){this.root.activeElement==this.contentDOM&&this.contentDOM.blur();for(let e of this.plugins)e.destroy(this);this.plugins=[],this.inputState.destroy(),this.docView.destroy(),this.dom.remove(),this.observer.destroy(),this.measureScheduled>-1&&this.win.cancelAnimationFrame(this.measureScheduled),this.destroyed=!0}static scrollIntoView(e,t={}){return En.of(new vi(typeof e=="number"?E.cursor(e):e,t.y,t.x,t.yMargin,t.xMargin))}scrollSnapshot(){let{scrollTop:e,scrollLeft:t}=this.scrollDOM,i=this.viewState.scrollAnchorAt(e);return En.of(new vi(E.cursor(i.from),"start","start",i.top-e,t,!0))}setTabFocusMode(e){e==null?this.inputState.tabFocusMode=this.inputState.tabFocusMode<0?0:-1:typeof e=="boolean"?this.inputState.tabFocusMode=e?0:-1:this.inputState.tabFocusMode!=0&&(this.inputState.tabFocusMode=Date.now()+e)}static domEventHandlers(e){return ne.define(()=>({}),{eventHandlers:e})}static domEventObservers(e){return ne.define(()=>({}),{eventObservers:e})}static theme(e,t){let i=$t.newName(),s=[In.of(i),Hi.of(yo(`.${i}`,e))];return t&&t.dark&&s.push(mo.of(!0)),s}static baseTheme(e){return li.lowest(Hi.of(yo("."+go,e,hf)))}static findFromDOM(e){var t;let i=e.querySelector(".cm-content"),s=i&&J.get(i)||J.get(e);return((t=s==null?void 0:s.rootView)===null||t===void 0?void 0:t.view)||null}}R.styleModule=Hi;R.inputHandler=Vc;R.clipboardInputFilter=Go;R.clipboardOutputFilter=Xo;R.scrollHandler=qc;R.focusChangeEffect=Wc;R.perLineTextDirection=_c;R.exceptionSink=Fc;R.updateListener=co;R.editable=kt;R.mouseSelectionStyle=Lc;R.dragMovesSelection=Ic;R.clickAddsSelectionRange=Bc;R.decorations=sn;R.outerDecorations=jc;R.atomicRanges=Yo;R.bidiIsolatedRanges=Kc;R.scrollMargins=Uc;R.darkTheme=mo;R.cspNonce=F.define({combine:n=>n.length?n[0]:""});R.contentAttributes=Qo;R.editorAttributes=zc;R.lineWrapping=R.contentAttributes.of({class:"cm-lineWrapping"});R.announce=_.define();const bm=4096,xa={};class ys{constructor(e,t,i,s,r,o){this.from=e,this.to=t,this.dir=i,this.isolates=s,this.fresh=r,this.order=o}static update(e,t){if(t.empty&&!e.some(r=>r.fresh))return e;let i=[],s=e.length?e[e.length-1].dir:ee.LTR;for(let r=Math.max(0,e.length-10);r<e.length;r++){let o=e[r];o.dir==s&&!t.touchesRange(o.from,o.to)&&i.push(new ys(t.mapPos(o.from,1),t.mapPos(o.to,-1),o.dir,o.isolates,!1,o.order))}return i}}function va(n,e,t){for(let i=n.state.facet(e),s=i.length-1;s>=0;s--){let r=i[s],o=typeof r=="function"?r(n):r;o&&so(o,t)}return t}const xm=L.mac?"mac":L.windows?"win":L.linux?"linux":"key";function vm(n,e){const t=n.split(/-(?!$)/);let i=t[t.length-1];i=="Space"&&(i=" ");let s,r,o,l;for(let a=0;a<t.length-1;++a){const h=t[a];if(/^(cmd|meta|m)$/i.test(h))l=!0;else if(/^a(lt)?$/i.test(h))s=!0;else if(/^(c|ctrl|control)$/i.test(h))r=!0;else if(/^s(hift)?$/i.test(h))o=!0;else if(/^mod$/i.test(h))e=="mac"?l=!0:r=!0;else throw new Error("Unrecognized modifier name: "+h)}return s&&(i="Alt-"+i),r&&(i="Ctrl-"+i),l&&(i="Meta-"+i),o&&(i="Shift-"+i),i}function Ln(n,e,t){return e.altKey&&(n="Alt-"+n),e.ctrlKey&&(n="Ctrl-"+n),e.metaKey&&(n="Meta-"+n),t!==!1&&e.shiftKey&&(n="Shift-"+n),n}const wm=li.default(R.domEventHandlers({keydown(n,e){return ff(cf(e.state),n,e,"editor")}})),vn=F.define({enables:wm}),wa=new WeakMap;function cf(n){let e=n.facet(vn),t=wa.get(e);return t||wa.set(e,t=km(e.reduce((i,s)=>i.concat(s),[]))),t}function Sm(n,e,t){return ff(cf(n.state),e,n,t)}let It=null;const Om=4e3;function km(n,e=xm){let t=Object.create(null),i=Object.create(null),s=(o,l)=>{let a=i[o];if(a==null)i[o]=l;else if(a!=l)throw new Error("Key binding "+o+" is used both as a regular binding and as a multi-stroke prefix")},r=(o,l,a,h,c)=>{var f,u;let d=t[o]||(t[o]=Object.create(null)),m=l.split(/ (?!$)/).map(y=>vm(y,e));for(let y=1;y<m.length;y++){let O=m.slice(0,y).join(" ");s(O,!0),d[O]||(d[O]={preventDefault:!0,stopPropagation:!1,run:[v=>{let g=It={view:v,prefix:O,scope:o};return setTimeout(()=>{It==g&&(It=null)},Om),!0}]})}let x=m.join(" ");s(x,!1);let S=d[x]||(d[x]={preventDefault:!1,stopPropagation:!1,run:((u=(f=d._any)===null||f===void 0?void 0:f.run)===null||u===void 0?void 0:u.slice())||[]});a&&S.run.push(a),h&&(S.preventDefault=!0),c&&(S.stopPropagation=!0)};for(let o of n){let l=o.scope?o.scope.split(" "):["editor"];if(o.any)for(let h of l){let c=t[h]||(t[h]=Object.create(null));c._any||(c._any={preventDefault:!1,stopPropagation:!1,run:[]});let{any:f}=o;for(let u in c)c[u].run.push(d=>f(d,bo))}let a=o[e]||o.key;if(a)for(let h of l)r(h,a,o.run,o.preventDefault,o.stopPropagation),o.shift&&r(h,"Shift-"+a,o.shift,o.preventDefault,o.stopPropagation)}return t}let bo=null;function ff(n,e,t,i){bo=e;let s=Kd(e),r=Ie(s,0),o=ft(r)==s.length&&s!=" ",l="",a=!1,h=!1,c=!1;It&&It.view==t&&It.scope==i&&(l=It.prefix+" ",Jc.indexOf(e.keyCode)<0&&(h=!0,It=null));let f=new Set,u=S=>{if(S){for(let y of S.run)if(!f.has(y)&&(f.add(y),y(t)))return S.stopPropagation&&(c=!0),!0;S.preventDefault&&(S.stopPropagation&&(c=!0),h=!0)}return!1},d=n[i],m,x;return d&&(u(d[l+Ln(s,e,!o)])?a=!0:o&&(e.altKey||e.metaKey||e.ctrlKey)&&!(L.windows&&e.ctrlKey&&e.altKey)&&!(L.mac&&e.altKey&&!e.ctrlKey)&&(m=qt[e.keyCode])&&m!=s?(u(d[l+Ln(m,e,!0)])||e.shiftKey&&(x=tn[e.keyCode])!=s&&x!=m&&u(d[l+Ln(x,e,!1)]))&&(a=!0):o&&e.shiftKey&&u(d[l+Ln(s,e,!0)])&&(a=!0),!a&&u(d._any)&&(a=!0)),h&&(a=!0),a&&c&&e.stopPropagation(),bo=null,a}class wn{constructor(e,t,i,s,r){this.className=e,this.left=t,this.top=i,this.width=s,this.height=r}draw(){let e=document.createElement("div");return e.className=this.className,this.adjust(e),e}update(e,t){return t.className!=this.className?!1:(this.adjust(e),!0)}adjust(e){e.style.left=this.left+"px",e.style.top=this.top+"px",this.width!=null&&(e.style.width=this.width+"px"),e.style.height=this.height+"px"}eq(e){return this.left==e.left&&this.top==e.top&&this.width==e.width&&this.height==e.height&&this.className==e.className}static forRange(e,t,i){if(i.empty){let s=e.coordsAtPos(i.head,i.assoc||1);if(!s)return[];let r=uf(e);return[new wn(t,s.left-r.left,s.top-r.top,null,s.bottom-s.top)]}else return Cm(e,t,i)}}function uf(n){let e=n.scrollDOM.getBoundingClientRect();return{left:(n.textDirection==ee.LTR?e.left:e.right-n.scrollDOM.clientWidth*n.scaleX)-n.scrollDOM.scrollLeft*n.scaleX,top:e.top-n.scrollDOM.scrollTop*n.scaleY}}function Sa(n,e,t,i){let s=n.coordsAtPos(e,t*2);if(!s)return i;let r=n.dom.getBoundingClientRect(),o=(s.top+s.bottom)/2,l=n.posAtCoords({x:r.left+1,y:o}),a=n.posAtCoords({x:r.right-1,y:o});return l==null||a==null?i:{from:Math.max(i.from,Math.min(l,a)),to:Math.min(i.to,Math.max(l,a))}}function Cm(n,e,t){if(t.to<=n.viewport.from||t.from>=n.viewport.to)return[];let i=Math.max(t.from,n.viewport.from),s=Math.min(t.to,n.viewport.to),r=n.textDirection==ee.LTR,o=n.contentDOM,l=o.getBoundingClientRect(),a=uf(n),h=o.querySelector(".cm-line"),c=h&&window.getComputedStyle(h),f=l.left+(c?parseInt(c.paddingLeft)+Math.min(0,parseInt(c.textIndent)):0),u=l.right-(c?parseInt(c.paddingRight):0),d=uo(n,i,1),m=uo(n,s,-1),x=d.type==Pe.Text?d:null,S=m.type==Pe.Text?m:null;if(x&&(n.lineWrapping||d.widgetLineBreaks)&&(x=Sa(n,i,1,x)),S&&(n.lineWrapping||m.widgetLineBreaks)&&(S=Sa(n,s,-1,S)),x&&S&&x.from==S.from&&x.to==S.to)return O(v(t.from,t.to,x));{let k=x?v(t.from,null,x):g(d,!1),T=S?v(null,t.to,S):g(m,!0),M=[];return(x||d).to<(S||m).from-(x&&S?1:0)||d.widgetLineBreaks>1&&k.bottom+n.defaultLineHeight/2<T.top?M.push(y(f,k.bottom,u,T.top)):k.bottom<T.top&&n.elementAtHeight((k.bottom+T.top)/2).type==Pe.Text&&(k.bottom=T.top=(k.bottom+T.top)/2),O(k).concat(M).concat(O(T))}function y(k,T,M,N){return new wn(e,k-a.left,T-a.top,M-k,N-T)}function O({top:k,bottom:T,horizontal:M}){let N=[];for(let w=0;w<M.length;w+=2)N.push(y(M[w],k,M[w+1],T));return N}function v(k,T,M){let N=1e9,w=-1e9,P=[];function V($,q,de,H,ze){let se=n.coordsAtPos($,$==M.to?-2:2),Oe=n.coordsAtPos(de,de==M.from?2:-2);!se||!Oe||(N=Math.min(se.top,Oe.top,N),w=Math.max(se.bottom,Oe.bottom,w),ze==ee.LTR?P.push(r&&q?f:se.left,r&&H?u:Oe.right):P.push(!r&&H?f:Oe.left,!r&&q?u:se.right))}let I=k??M.from,z=T??M.to;for(let $ of n.visibleRanges)if($.to>I&&$.from<z)for(let q=Math.max($.from,I),de=Math.min($.to,z);;){let H=n.state.doc.lineAt(q);for(let ze of n.bidiSpans(H)){let se=ze.from+H.from,Oe=ze.to+H.from;if(se>=de)break;Oe>q&&V(Math.max(se,q),k==null&&se<=I,Math.min(Oe,de),T==null&&Oe>=z,ze.dir)}if(q=H.to+1,q>=de)break}return P.length==0&&V(I,k==null,z,T==null,n.textDirection),{top:N,bottom:w,horizontal:P}}function g(k,T){let M=l.top+(T?k.top:k.bottom);return{top:M,bottom:M,horizontal:[]}}}function Am(n,e){return n.constructor==e.constructor&&n.eq(e)}class Tm{constructor(e,t){this.view=e,this.layer=t,this.drawn=[],this.scaleX=1,this.scaleY=1,this.measureReq={read:this.measure.bind(this),write:this.draw.bind(this)},this.dom=e.scrollDOM.appendChild(document.createElement("div")),this.dom.classList.add("cm-layer"),t.above&&this.dom.classList.add("cm-layer-above"),t.class&&this.dom.classList.add(t.class),this.scale(),this.dom.setAttribute("aria-hidden","true"),this.setOrder(e.state),e.requestMeasure(this.measureReq),t.mount&&t.mount(this.dom,e)}update(e){e.startState.facet(is)!=e.state.facet(is)&&this.setOrder(e.state),(this.layer.update(e,this.dom)||e.geometryChanged)&&(this.scale(),e.view.requestMeasure(this.measureReq))}docViewUpdate(e){this.layer.updateOnDocViewUpdate!==!1&&e.requestMeasure(this.measureReq)}setOrder(e){let t=0,i=e.facet(is);for(;t<i.length&&i[t]!=this.layer;)t++;this.dom.style.zIndex=String((this.layer.above?150:-1)-t)}measure(){return this.layer.markers(this.view)}scale(){let{scaleX:e,scaleY:t}=this.view;(e!=this.scaleX||t!=this.scaleY)&&(this.scaleX=e,this.scaleY=t,this.dom.style.transform=`scale(${1/e}, ${1/t})`)}draw(e){if(e.length!=this.drawn.length||e.some((t,i)=>!Am(t,this.drawn[i]))){let t=this.dom.firstChild,i=0;for(let s of e)s.update&&t&&s.constructor&&this.drawn[i].constructor&&s.update(t,this.drawn[i])?(t=t.nextSibling,i++):this.dom.insertBefore(s.draw(),t);for(;t;){let s=t.nextSibling;t.remove(),t=s}this.drawn=e}}destroy(){this.layer.destroy&&this.layer.destroy(this.dom,this.view),this.dom.remove()}}const is=F.define();function df(n){return[ne.define(e=>new Tm(e,n)),is.of(n)]}const rn=F.define({combine(n){return nt(n,{cursorBlinkRate:1200,drawRangeCursor:!0},{cursorBlinkRate:(e,t)=>Math.min(e,t),drawRangeCursor:(e,t)=>e||t})}});function Mm(n={}){return[rn.of(n),Em,Dm,Pm,$c.of(!0)]}function pf(n){return n.startState.facet(rn)!=n.state.facet(rn)}const Em=df({above:!0,markers(n){let{state:e}=n,t=e.facet(rn),i=[];for(let s of e.selection.ranges){let r=s==e.selection.main;if(s.empty||t.drawRangeCursor){let o=r?"cm-cursor cm-cursor-primary":"cm-cursor cm-cursor-secondary",l=s.empty?s:E.cursor(s.head,s.head>s.anchor?-1:1);for(let a of wn.forRange(n,o,l))i.push(a)}}return i},update(n,e){n.transactions.some(i=>i.selection)&&(e.style.animationName=e.style.animationName=="cm-blink"?"cm-blink2":"cm-blink");let t=pf(n);return t&&Oa(n.state,e),n.docChanged||n.selectionSet||t},mount(n,e){Oa(e.state,n)},class:"cm-cursorLayer"});function Oa(n,e){e.style.animationDuration=n.facet(rn).cursorBlinkRate+"ms"}const Dm=df({above:!1,markers(n){return n.state.selection.ranges.map(e=>e.empty?[]:wn.forRange(n,"cm-selectionBackground",e)).reduce((e,t)=>e.concat(t))},update(n,e){return n.docChanged||n.selectionSet||n.viewportChanged||pf(n)},class:"cm-selectionLayer"}),Pm=li.highest(R.theme({".cm-line":{"& ::selection, &::selection":{backgroundColor:"transparent !important"},caretColor:"transparent !important"},".cm-content":{caretColor:"transparent !important","& :focus":{caretColor:"initial !important","&::selection, & ::selection":{backgroundColor:"Highlight !important"}}}})),mf=_.define({map(n,e){return n==null?null:e.mapPos(n)}}),Ki=ue.define({create(){return null},update(n,e){return n!=null&&(n=e.changes.mapPos(n)),e.effects.reduce((t,i)=>i.is(mf)?i.value:t,n)}}),Nm=ne.fromClass(class{constructor(n){this.view=n,this.cursor=null,this.measureReq={read:this.readPos.bind(this),write:this.drawCursor.bind(this)}}update(n){var e;let t=n.state.field(Ki);t==null?this.cursor!=null&&((e=this.cursor)===null||e===void 0||e.remove(),this.cursor=null):(this.cursor||(this.cursor=this.view.scrollDOM.appendChild(document.createElement("div")),this.cursor.className="cm-dropCursor"),(n.startState.field(Ki)!=t||n.docChanged||n.geometryChanged)&&this.view.requestMeasure(this.measureReq))}readPos(){let{view:n}=this,e=n.state.field(Ki),t=e!=null&&n.coordsAtPos(e);if(!t)return null;let i=n.scrollDOM.getBoundingClientRect();return{left:t.left-i.left+n.scrollDOM.scrollLeft*n.scaleX,top:t.top-i.top+n.scrollDOM.scrollTop*n.scaleY,height:t.bottom-t.top}}drawCursor(n){if(this.cursor){let{scaleX:e,scaleY:t}=this.view;n?(this.cursor.style.left=n.left/e+"px",this.cursor.style.top=n.top/t+"px",this.cursor.style.height=n.height/t+"px"):this.cursor.style.left="-100000px"}}destroy(){this.cursor&&this.cursor.remove()}setDropPos(n){this.view.state.field(Ki)!=n&&this.view.dispatch({effects:mf.of(n)})}},{eventObservers:{dragover(n){this.setDropPos(this.view.posAtCoords({x:n.clientX,y:n.clientY}))},dragleave(n){(n.target==this.view.contentDOM||!this.view.contentDOM.contains(n.relatedTarget))&&this.setDropPos(null)},dragend(){this.setDropPos(null)},drop(){this.setDropPos(null)}}});function Rm(){return[Ki,Nm]}function ka(n,e,t,i,s){e.lastIndex=0;for(let r=n.iterRange(t,i),o=t,l;!r.next().done;o+=r.value.length)if(!r.lineBreak)for(;l=e.exec(r.value);)s(o+l.index,l)}function Bm(n,e){let t=n.visibleRanges;if(t.length==1&&t[0].from==n.viewport.from&&t[0].to==n.viewport.to)return t;let i=[];for(let{from:s,to:r}of t)s=Math.max(n.state.doc.lineAt(s).from,s-e),r=Math.min(n.state.doc.lineAt(r).to,r+e),i.length&&i[i.length-1].to>=s?i[i.length-1].to=r:i.push({from:s,to:r});return i}class Im{constructor(e){const{regexp:t,decoration:i,decorate:s,boundary:r,maxLength:o=1e3}=e;if(!t.global)throw new RangeError("The regular expression given to MatchDecorator should have its 'g' flag set");if(this.regexp=t,s)this.addMatch=(l,a,h,c)=>s(c,h,h+l[0].length,l,a);else if(typeof i=="function")this.addMatch=(l,a,h,c)=>{let f=i(l,a,h);f&&c(h,h+l[0].length,f)};else if(i)this.addMatch=(l,a,h,c)=>c(h,h+l[0].length,i);else throw new RangeError("Either 'decorate' or 'decoration' should be provided to MatchDecorator");this.boundary=r,this.maxLength=o}createDeco(e){let t=new At,i=t.add.bind(t);for(let{from:s,to:r}of Bm(e,this.maxLength))ka(e.state.doc,this.regexp,s,r,(o,l)=>this.addMatch(l,e,o,i));return t.finish()}updateDeco(e,t){let i=1e9,s=-1;return e.docChanged&&e.changes.iterChanges((r,o,l,a)=>{a>=e.view.viewport.from&&l<=e.view.viewport.to&&(i=Math.min(l,i),s=Math.max(a,s))}),e.viewportMoved||s-i>1e3?this.createDeco(e.view):s>-1?this.updateRange(e.view,t.map(e.changes),i,s):t}updateRange(e,t,i,s){for(let r of e.visibleRanges){let o=Math.max(r.from,i),l=Math.min(r.to,s);if(l>=o){let a=e.state.doc.lineAt(o),h=a.to<l?e.state.doc.lineAt(l):a,c=Math.max(r.from,a.from),f=Math.min(r.to,h.to);if(this.boundary){for(;o>a.from;o--)if(this.boundary.test(a.text[o-1-a.from])){c=o;break}for(;l<h.to;l++)if(this.boundary.test(h.text[l-h.from])){f=l;break}}let u=[],d,m=(x,S,y)=>u.push(y.range(x,S));if(a==h)for(this.regexp.lastIndex=c-a.from;(d=this.regexp.exec(a.text))&&d.index<f-a.from;)this.addMatch(d,e,d.index+a.from,m);else ka(e.state.doc,this.regexp,c,f,(x,S)=>this.addMatch(S,e,x,m));t=t.update({filterFrom:c,filterTo:f,filter:(x,S)=>x<c||S>f,add:u})}}return t}}const xo=/x/.unicode!=null?"gu":"g",Lm=new RegExp(`[\0-\b
--­؜​‎‏\u2028\u2029‭‮⁦⁧⁩\uFEFF￹-￼]`,xo),Fm={0:"null",7:"bell",8:"backspace",10:"newline",11:"vertical tab",13:"carriage return",27:"escape",8203:"zero width space",8204:"zero width non-joiner",8205:"zero width joiner",8206:"left-to-right mark",8207:"right-to-left mark",8232:"line separator",8237:"left-to-right override",8238:"right-to-left override",8294:"left-to-right isolate",8295:"right-to-left isolate",8297:"pop directional isolate",8233:"paragraph separator",65279:"zero width no-break space",65532:"object replacement"};let ar=null;function Vm(){var n;if(ar==null&&typeof document<"u"&&document.body){let e=document.body.style;ar=((n=e.tabSize)!==null&&n!==void 0?n:e.MozTabSize)!=null}return ar||!1}const ns=F.define({combine(n){let e=nt(n,{render:null,specialChars:Lm,addSpecialChars:null});return(e.replaceTabs=!Vm())&&(e.specialChars=new RegExp("	|"+e.specialChars.source,xo)),e.addSpecialChars&&(e.specialChars=new RegExp(e.specialChars.source+"|"+e.addSpecialChars.source,xo)),e}});function Wm(n={}){return[ns.of(n),_m()]}let Ca=null;function _m(){return Ca||(Ca=ne.fromClass(class{constructor(n){this.view=n,this.decorations=W.none,this.decorationCache=Object.create(null),this.decorator=this.makeDecorator(n.state.facet(ns)),this.decorations=this.decorator.createDeco(n)}makeDecorator(n){return new Im({regexp:n.specialChars,decoration:(e,t,i)=>{let{doc:s}=t.state,r=Ie(e[0],0);if(r==9){let o=s.lineAt(i),l=t.state.tabSize,a=Ni(o.text,l,i-o.from);return W.replace({widget:new zm((l-a%l)*this.view.defaultCharacterWidth/this.view.scaleX)})}return this.decorationCache[r]||(this.decorationCache[r]=W.replace({widget:new Hm(n,r)}))},boundary:n.replaceTabs?void 0:/[^]/})}update(n){let e=n.state.facet(ns);n.startState.facet(ns)!=e?(this.decorator=this.makeDecorator(e),this.decorations=this.decorator.createDeco(n.view)):this.decorations=this.decorator.updateDeco(n,this.decorations)}},{decorations:n=>n.decorations}))}const $m="•";function qm(n){return n>=32?$m:n==10?"␤":String.fromCharCode(9216+n)}class Hm extends Et{constructor(e,t){super(),this.options=e,this.code=t}eq(e){return e.code==this.code}toDOM(e){let t=qm(this.code),i=e.state.phrase("Control character")+" "+(Fm[this.code]||"0x"+this.code.toString(16)),s=this.options.render&&this.options.render(this.code,i,t);if(s)return s;let r=document.createElement("span");return r.textContent=t,r.title=i,r.setAttribute("aria-label",i),r.className="cm-specialChar",r}ignoreEvent(){return!1}}class zm extends Et{constructor(e){super(),this.width=e}eq(e){return e.width==this.width}toDOM(){let e=document.createElement("span");return e.textContent="	",e.className="cm-tab",e.style.width=this.width+"px",e}ignoreEvent(){return!1}}function jm(){return Um}const Km=W.line({class:"cm-activeLine"}),Um=ne.fromClass(class{constructor(n){this.decorations=this.getDeco(n)}update(n){(n.docChanged||n.selectionSet)&&(this.decorations=this.getDeco(n.view))}getDeco(n){let e=-1,t=[];for(let i of n.state.selection.ranges){let s=n.lineBlockAt(i.head);s.from>e&&(t.push(Km.range(s.from)),e=s.from)}return W.set(t)}},{decorations:n=>n.decorations});class Gm extends Et{constructor(e){super(),this.content=e}toDOM(e){let t=document.createElement("span");return t.className="cm-placeholder",t.style.pointerEvents="none",t.appendChild(typeof this.content=="string"?document.createTextNode(this.content):typeof this.content=="function"?this.content(e):this.content.cloneNode(!0)),t.setAttribute("aria-hidden","true"),t}coordsAt(e){let t=e.firstChild?Ci(e.firstChild):[];if(!t.length)return null;let i=window.getComputedStyle(e.parentNode),s=yn(t[0],i.direction!="rtl"),r=parseInt(i.lineHeight);return s.bottom-s.top>r*1.5?{left:s.left,right:s.right,top:s.top,bottom:s.top+r}:s}ignoreEvent(){return!1}}function Xm(n){let e=ne.fromClass(class{constructor(t){this.view=t,this.placeholder=n?W.set([W.widget({widget:new Gm(n),side:1}).range(0)]):W.none}get decorations(){return this.view.state.doc.length?W.none:this.placeholder}},{decorations:t=>t.decorations});return typeof n=="string"?[e,R.contentAttributes.of({"aria-placeholder":n})]:e}const vo=2e3;function Qm(n,e,t){let i=Math.min(e.line,t.line),s=Math.max(e.line,t.line),r=[];if(e.off>vo||t.off>vo||e.col<0||t.col<0){let o=Math.min(e.off,t.off),l=Math.max(e.off,t.off);for(let a=i;a<=s;a++){let h=n.doc.line(a);h.length<=l&&r.push(E.range(h.from+o,h.to+l))}}else{let o=Math.min(e.col,t.col),l=Math.max(e.col,t.col);for(let a=i;a<=s;a++){let h=n.doc.line(a),c=Yr(h.text,o,n.tabSize,!0);if(c<0)r.push(E.cursor(h.to));else{let f=Yr(h.text,l,n.tabSize);r.push(E.range(h.from+c,h.from+f))}}}return r}function Ym(n,e){let t=n.coordsAtPos(n.viewport.from);return t?Math.round(Math.abs((t.left-e)/n.defaultCharacterWidth)):-1}function Aa(n,e){let t=n.posAtCoords({x:e.clientX,y:e.clientY},!1),i=n.state.doc.lineAt(t),s=t-i.from,r=s>vo?-1:s==i.length?Ym(n,e.clientX):Ni(i.text,n.state.tabSize,t-i.from);return{line:i.number,col:r,off:s}}function Jm(n,e){let t=Aa(n,e),i=n.state.selection;return t?{update(s){if(s.docChanged){let r=s.changes.mapPos(s.startState.doc.line(t.line).from),o=s.state.doc.lineAt(r);t={line:o.number,col:t.col,off:Math.min(t.off,o.length)},i=i.map(s.changes)}},get(s,r,o){let l=Aa(n,s);if(!l)return i;let a=Qm(n.state,t,l);return a.length?o?E.create(a.concat(i.ranges)):E.create(a):i}}:null}function Zm(n){let e=t=>t.altKey&&t.button==0;return R.mouseSelectionStyle.of((t,i)=>e(i)?Jm(t,i):null)}const eg={Alt:[18,n=>!!n.altKey],Control:[17,n=>!!n.ctrlKey],Shift:[16,n=>!!n.shiftKey],Meta:[91,n=>!!n.metaKey]},tg={style:"cursor: crosshair"};function ig(n={}){let[e,t]=eg[n.key||"Alt"],i=ne.fromClass(class{constructor(s){this.view=s,this.isDown=!1}set(s){this.isDown!=s&&(this.isDown=s,this.view.update([]))}},{eventObservers:{keydown(s){this.set(s.keyCode==e||t(s))},keyup(s){(s.keyCode==e||!t(s))&&this.set(!1)},mousemove(s){this.set(t(s))}}});return[i,R.contentAttributes.of(s=>{var r;return!((r=s.plugin(i))===null||r===void 0)&&r.isDown?tg:null})]}const Wi="-10000px";class gf{constructor(e,t,i,s){this.facet=t,this.createTooltipView=i,this.removeTooltipView=s,this.input=e.state.facet(t),this.tooltips=this.input.filter(o=>o);let r=null;this.tooltipViews=this.tooltips.map(o=>r=i(o,r))}update(e,t){var i;let s=e.state.facet(this.facet),r=s.filter(a=>a);if(s===this.input){for(let a of this.tooltipViews)a.update&&a.update(e);return!1}let o=[],l=t?[]:null;for(let a=0;a<r.length;a++){let h=r[a],c=-1;if(h){for(let f=0;f<this.tooltips.length;f++){let u=this.tooltips[f];u&&u.create==h.create&&(c=f)}if(c<0)o[a]=this.createTooltipView(h,a?o[a-1]:null),l&&(l[a]=!!h.above);else{let f=o[a]=this.tooltipViews[c];l&&(l[a]=t[c]),f.update&&f.update(e)}}}for(let a of this.tooltipViews)o.indexOf(a)<0&&(this.removeTooltipView(a),(i=a.destroy)===null||i===void 0||i.call(a));return t&&(l.forEach((a,h)=>t[h]=a),t.length=l.length),this.input=s,this.tooltips=r,this.tooltipViews=o,!0}}function ng(n){let e=n.dom.ownerDocument.documentElement;return{top:0,left:0,bottom:e.clientHeight,right:e.clientWidth}}const hr=F.define({combine:n=>{var e,t,i;return{position:L.ios?"absolute":((e=n.find(s=>s.position))===null||e===void 0?void 0:e.position)||"fixed",parent:((t=n.find(s=>s.parent))===null||t===void 0?void 0:t.parent)||null,tooltipSpace:((i=n.find(s=>s.tooltipSpace))===null||i===void 0?void 0:i.tooltipSpace)||ng}}}),Ta=new WeakMap,il=ne.fromClass(class{constructor(n){this.view=n,this.above=[],this.inView=!0,this.madeAbsolute=!1,this.lastTransaction=0,this.measureTimeout=-1;let e=n.state.facet(hr);this.position=e.position,this.parent=e.parent,this.classes=n.themeClasses,this.createContainer(),this.measureReq={read:this.readMeasure.bind(this),write:this.writeMeasure.bind(this),key:this},this.resizeObserver=typeof ResizeObserver=="function"?new ResizeObserver(()=>this.measureSoon()):null,this.manager=new gf(n,Vs,(t,i)=>this.createTooltip(t,i),t=>{this.resizeObserver&&this.resizeObserver.unobserve(t.dom),t.dom.remove()}),this.above=this.manager.tooltips.map(t=>!!t.above),this.intersectionObserver=typeof IntersectionObserver=="function"?new IntersectionObserver(t=>{Date.now()>this.lastTransaction-50&&t.length>0&&t[t.length-1].intersectionRatio<1&&this.measureSoon()},{threshold:[1]}):null,this.observeIntersection(),n.win.addEventListener("resize",this.measureSoon=this.measureSoon.bind(this)),this.maybeMeasure()}createContainer(){this.parent?(this.container=document.createElement("div"),this.container.style.position="relative",this.container.className=this.view.themeClasses,this.parent.appendChild(this.container)):this.container=this.view.dom}observeIntersection(){if(this.intersectionObserver){this.intersectionObserver.disconnect();for(let n of this.manager.tooltipViews)this.intersectionObserver.observe(n.dom)}}measureSoon(){this.measureTimeout<0&&(this.measureTimeout=setTimeout(()=>{this.measureTimeout=-1,this.maybeMeasure()},50))}update(n){n.transactions.length&&(this.lastTransaction=Date.now());let e=this.manager.update(n,this.above);e&&this.observeIntersection();let t=e||n.geometryChanged,i=n.state.facet(hr);if(i.position!=this.position&&!this.madeAbsolute){this.position=i.position;for(let s of this.manager.tooltipViews)s.dom.style.position=this.position;t=!0}if(i.parent!=this.parent){this.parent&&this.container.remove(),this.parent=i.parent,this.createContainer();for(let s of this.manager.tooltipViews)this.container.appendChild(s.dom);t=!0}else this.parent&&this.view.themeClasses!=this.classes&&(this.classes=this.container.className=this.view.themeClasses);t&&this.maybeMeasure()}createTooltip(n,e){let t=n.create(this.view),i=e?e.dom:null;if(t.dom.classList.add("cm-tooltip"),n.arrow&&!t.dom.querySelector(".cm-tooltip > .cm-tooltip-arrow")){let s=document.createElement("div");s.className="cm-tooltip-arrow",t.dom.appendChild(s)}return t.dom.style.position=this.position,t.dom.style.top=Wi,t.dom.style.left="0px",this.container.insertBefore(t.dom,i),t.mount&&t.mount(this.view),this.resizeObserver&&this.resizeObserver.observe(t.dom),t}destroy(){var n,e,t;this.view.win.removeEventListener("resize",this.measureSoon);for(let i of this.manager.tooltipViews)i.dom.remove(),(n=i.destroy)===null||n===void 0||n.call(i);this.parent&&this.container.remove(),(e=this.resizeObserver)===null||e===void 0||e.disconnect(),(t=this.intersectionObserver)===null||t===void 0||t.disconnect(),clearTimeout(this.measureTimeout)}readMeasure(){let n=1,e=1,t=!1;if(this.position=="fixed"&&this.manager.tooltipViews.length){let{dom:r}=this.manager.tooltipViews[0];if(L.gecko)t=r.offsetParent!=this.container.ownerDocument.body;else if(r.style.top==Wi&&r.style.left=="0px"){let o=r.getBoundingClientRect();t=Math.abs(o.top+1e4)>1||Math.abs(o.left)>1}}if(t||this.position=="absolute")if(this.parent){let r=this.parent.getBoundingClientRect();r.width&&r.height&&(n=r.width/this.parent.offsetWidth,e=r.height/this.parent.offsetHeight)}else({scaleX:n,scaleY:e}=this.view.viewState);let i=this.view.scrollDOM.getBoundingClientRect(),s=Jo(this.view);return{visible:{left:i.left+s.left,top:i.top+s.top,right:i.right-s.right,bottom:i.bottom-s.bottom},parent:this.parent?this.container.getBoundingClientRect():this.view.dom.getBoundingClientRect(),pos:this.manager.tooltips.map((r,o)=>{let l=this.manager.tooltipViews[o];return l.getCoords?l.getCoords(r.pos):this.view.coordsAtPos(r.pos)}),size:this.manager.tooltipViews.map(({dom:r})=>r.getBoundingClientRect()),space:this.view.state.facet(hr).tooltipSpace(this.view),scaleX:n,scaleY:e,makeAbsolute:t}}writeMeasure(n){var e;if(n.makeAbsolute){this.madeAbsolute=!0,this.position="absolute";for(let l of this.manager.tooltipViews)l.dom.style.position="absolute"}let{visible:t,space:i,scaleX:s,scaleY:r}=n,o=[];for(let l=0;l<this.manager.tooltips.length;l++){let a=this.manager.tooltips[l],h=this.manager.tooltipViews[l],{dom:c}=h,f=n.pos[l],u=n.size[l];if(!f||a.clip!==!1&&(f.bottom<=Math.max(t.top,i.top)||f.top>=Math.min(t.bottom,i.bottom)||f.right<Math.max(t.left,i.left)-.1||f.left>Math.min(t.right,i.right)+.1)){c.style.top=Wi;continue}let d=a.arrow?h.dom.querySelector(".cm-tooltip-arrow"):null,m=d?7:0,x=u.right-u.left,S=(e=Ta.get(h))!==null&&e!==void 0?e:u.bottom-u.top,y=h.offset||rg,O=this.view.textDirection==ee.LTR,v=u.width>i.right-i.left?O?i.left:i.right-u.width:O?Math.max(i.left,Math.min(f.left-(d?14:0)+y.x,i.right-x)):Math.min(Math.max(i.left,f.left-x+(d?14:0)-y.x),i.right-x),g=this.above[l];!a.strictSide&&(g?f.top-S-m-y.y<i.top:f.bottom+S+m+y.y>i.bottom)&&g==i.bottom-f.bottom>f.top-i.top&&(g=this.above[l]=!g);let k=(g?f.top-i.top:i.bottom-f.bottom)-m;if(k<S&&h.resize!==!1){if(k<this.view.defaultLineHeight){c.style.top=Wi;continue}Ta.set(h,S),c.style.height=(S=k)/r+"px"}else c.style.height&&(c.style.height="");let T=g?f.top-S-m-y.y:f.bottom+m+y.y,M=v+x;if(h.overlap!==!0)for(let N of o)N.left<M&&N.right>v&&N.top<T+S&&N.bottom>T&&(T=g?N.top-S-2-m:N.bottom+m+2);if(this.position=="absolute"?(c.style.top=(T-n.parent.top)/r+"px",Ma(c,(v-n.parent.left)/s)):(c.style.top=T/r+"px",Ma(c,v/s)),d){let N=f.left+(O?y.x:-y.x)-(v+14-7);d.style.left=N/s+"px"}h.overlap!==!0&&o.push({left:v,top:T,right:M,bottom:T+S}),c.classList.toggle("cm-tooltip-above",g),c.classList.toggle("cm-tooltip-below",!g),h.positioned&&h.positioned(n.space)}}maybeMeasure(){if(this.manager.tooltips.length&&(this.view.inView&&this.view.requestMeasure(this.measureReq),this.inView!=this.view.inView&&(this.inView=this.view.inView,!this.inView)))for(let n of this.manager.tooltipViews)n.dom.style.top=Wi}},{eventObservers:{scroll(){this.maybeMeasure()}}});function Ma(n,e){let t=parseInt(n.style.left,10);(isNaN(t)||Math.abs(e-t)>1)&&(n.style.left=e+"px")}const sg=R.baseTheme({".cm-tooltip":{zIndex:500,boxSizing:"border-box"},"&light .cm-tooltip":{border:"1px solid #bbb",backgroundColor:"#f5f5f5"},"&light .cm-tooltip-section:not(:first-child)":{borderTop:"1px solid #bbb"},"&dark .cm-tooltip":{backgroundColor:"#333338",color:"white"},".cm-tooltip-arrow":{height:"7px",width:`${7*2}px`,position:"absolute",zIndex:-1,overflow:"hidden","&:before, &:after":{content:"''",position:"absolute",width:0,height:0,borderLeft:"7px solid transparent",borderRight:"7px solid transparent"},".cm-tooltip-above &":{bottom:"-7px","&:before":{borderTop:"7px solid #bbb"},"&:after":{borderTop:"7px solid #f5f5f5",bottom:"1px"}},".cm-tooltip-below &":{top:"-7px","&:before":{borderBottom:"7px solid #bbb"},"&:after":{borderBottom:"7px solid #f5f5f5",top:"1px"}}},"&dark .cm-tooltip .cm-tooltip-arrow":{"&:before":{borderTopColor:"#333338",borderBottomColor:"#333338"},"&:after":{borderTopColor:"transparent",borderBottomColor:"transparent"}}}),rg={x:0,y:0},Vs=F.define({enables:[il,sg]}),bs=F.define({combine:n=>n.reduce((e,t)=>e.concat(t),[])});class Ws{static create(e){return new Ws(e)}constructor(e){this.view=e,this.mounted=!1,this.dom=document.createElement("div"),this.dom.classList.add("cm-tooltip-hover"),this.manager=new gf(e,bs,(t,i)=>this.createHostedView(t,i),t=>t.dom.remove())}createHostedView(e,t){let i=e.create(this.view);return i.dom.classList.add("cm-tooltip-section"),this.dom.insertBefore(i.dom,t?t.dom.nextSibling:this.dom.firstChild),this.mounted&&i.mount&&i.mount(this.view),i}mount(e){for(let t of this.manager.tooltipViews)t.mount&&t.mount(e);this.mounted=!0}positioned(e){for(let t of this.manager.tooltipViews)t.positioned&&t.positioned(e)}update(e){this.manager.update(e)}destroy(){var e;for(let t of this.manager.tooltipViews)(e=t.destroy)===null||e===void 0||e.call(t)}passProp(e){let t;for(let i of this.manager.tooltipViews){let s=i[e];if(s!==void 0){if(t===void 0)t=s;else if(t!==s)return}}return t}get offset(){return this.passProp("offset")}get getCoords(){return this.passProp("getCoords")}get overlap(){return this.passProp("overlap")}get resize(){return this.passProp("resize")}}const og=Vs.compute([bs],n=>{let e=n.facet(bs);return e.length===0?null:{pos:Math.min(...e.map(t=>t.pos)),end:Math.max(...e.map(t=>{var i;return(i=t.end)!==null&&i!==void 0?i:t.pos})),create:Ws.create,above:e[0].above,arrow:e.some(t=>t.arrow)}});class lg{constructor(e,t,i,s,r){this.view=e,this.source=t,this.field=i,this.setHover=s,this.hoverTime=r,this.hoverTimeout=-1,this.restartTimeout=-1,this.pending=null,this.lastMove={x:0,y:0,target:e.dom,time:0},this.checkHover=this.checkHover.bind(this),e.dom.addEventListener("mouseleave",this.mouseleave=this.mouseleave.bind(this)),e.dom.addEventListener("mousemove",this.mousemove=this.mousemove.bind(this))}update(){this.pending&&(this.pending=null,clearTimeout(this.restartTimeout),this.restartTimeout=setTimeout(()=>this.startHover(),20))}get active(){return this.view.state.field(this.field)}checkHover(){if(this.hoverTimeout=-1,this.active.length)return;let e=Date.now()-this.lastMove.time;e<this.hoverTime?this.hoverTimeout=setTimeout(this.checkHover,this.hoverTime-e):this.startHover()}startHover(){clearTimeout(this.restartTimeout);let{view:e,lastMove:t}=this,i=e.docView.nearest(t.target);if(!i)return;let s,r=1;if(i instanceof Ft)s=i.posAtStart;else{if(s=e.posAtCoords(t),s==null)return;let l=e.coordsAtPos(s);if(!l||t.y<l.top||t.y>l.bottom||t.x<l.left-e.defaultCharacterWidth||t.x>l.right+e.defaultCharacterWidth)return;let a=e.bidiSpans(e.state.doc.lineAt(s)).find(c=>c.from<=s&&c.to>=s),h=a&&a.dir==ee.RTL?-1:1;r=t.x<l.left?-h:h}let o=this.source(e,s,r);if(o!=null&&o.then){let l=this.pending={pos:s};o.then(a=>{this.pending==l&&(this.pending=null,a&&!(Array.isArray(a)&&!a.length)&&e.dispatch({effects:this.setHover.of(Array.isArray(a)?a:[a])}))},a=>De(e.state,a,"hover tooltip"))}else o&&!(Array.isArray(o)&&!o.length)&&e.dispatch({effects:this.setHover.of(Array.isArray(o)?o:[o])})}get tooltip(){let e=this.view.plugin(il),t=e?e.manager.tooltips.findIndex(i=>i.create==Ws.create):-1;return t>-1?e.manager.tooltipViews[t]:null}mousemove(e){var t,i;this.lastMove={x:e.clientX,y:e.clientY,target:e.target,time:Date.now()},this.hoverTimeout<0&&(this.hoverTimeout=setTimeout(this.checkHover,this.hoverTime));let{active:s,tooltip:r}=this;if(s.length&&r&&!ag(r.dom,e)||this.pending){let{pos:o}=s[0]||this.pending,l=(i=(t=s[0])===null||t===void 0?void 0:t.end)!==null&&i!==void 0?i:o;(o==l?this.view.posAtCoords(this.lastMove)!=o:!hg(this.view,o,l,e.clientX,e.clientY))&&(this.view.dispatch({effects:this.setHover.of([])}),this.pending=null)}}mouseleave(e){clearTimeout(this.hoverTimeout),this.hoverTimeout=-1;let{active:t}=this;if(t.length){let{tooltip:i}=this;i&&i.dom.contains(e.relatedTarget)?this.watchTooltipLeave(i.dom):this.view.dispatch({effects:this.setHover.of([])})}}watchTooltipLeave(e){let t=i=>{e.removeEventListener("mouseleave",t),this.active.length&&!this.view.dom.contains(i.relatedTarget)&&this.view.dispatch({effects:this.setHover.of([])})};e.addEventListener("mouseleave",t)}destroy(){clearTimeout(this.hoverTimeout),this.view.dom.removeEventListener("mouseleave",this.mouseleave),this.view.dom.removeEventListener("mousemove",this.mousemove)}}const Fn=4;function ag(n,e){let{left:t,right:i,top:s,bottom:r}=n.getBoundingClientRect(),o;if(o=n.querySelector(".cm-tooltip-arrow")){let l=o.getBoundingClientRect();s=Math.min(l.top,s),r=Math.max(l.bottom,r)}return e.clientX>=t-Fn&&e.clientX<=i+Fn&&e.clientY>=s-Fn&&e.clientY<=r+Fn}function hg(n,e,t,i,s,r){let o=n.scrollDOM.getBoundingClientRect(),l=n.documentTop+n.documentPadding.top+n.contentHeight;if(o.left>i||o.right<i||o.top>s||Math.min(o.bottom,l)<s)return!1;let a=n.posAtCoords({x:i,y:s},!1);return a>=e&&a<=t}function cg(n,e={}){let t=_.define(),i=ue.define({create(){return[]},update(s,r){if(s.length&&(e.hideOnChange&&(r.docChanged||r.selection)?s=[]:e.hideOn&&(s=s.filter(o=>!e.hideOn(r,o))),r.docChanged)){let o=[];for(let l of s){let a=r.changes.mapPos(l.pos,-1,Ee.TrackDel);if(a!=null){let h=Object.assign(Object.create(null),l);h.pos=a,h.end!=null&&(h.end=r.changes.mapPos(h.end)),o.push(h)}}s=o}for(let o of r.effects)o.is(t)&&(s=o.value),o.is(fg)&&(s=[]);return s},provide:s=>bs.from(s)});return{active:i,extension:[i,ne.define(s=>new lg(s,n,i,t,e.hoverTime||300)),og]}}function yf(n,e){let t=n.plugin(il);if(!t)return null;let i=t.manager.tooltips.indexOf(e);return i<0?null:t.manager.tooltipViews[i]}const fg=_.define(),Ea=F.define({combine(n){let e,t;for(let i of n)e=e||i.topContainer,t=t||i.bottomContainer;return{topContainer:e,bottomContainer:t}}});function on(n,e){let t=n.plugin(bf),i=t?t.specs.indexOf(e):-1;return i>-1?t.panels[i]:null}const bf=ne.fromClass(class{constructor(n){this.input=n.state.facet(ln),this.specs=this.input.filter(t=>t),this.panels=this.specs.map(t=>t(n));let e=n.state.facet(Ea);this.top=new Vn(n,!0,e.topContainer),this.bottom=new Vn(n,!1,e.bottomContainer),this.top.sync(this.panels.filter(t=>t.top)),this.bottom.sync(this.panels.filter(t=>!t.top));for(let t of this.panels)t.dom.classList.add("cm-panel"),t.mount&&t.mount()}update(n){let e=n.state.facet(Ea);this.top.container!=e.topContainer&&(this.top.sync([]),this.top=new Vn(n.view,!0,e.topContainer)),this.bottom.container!=e.bottomContainer&&(this.bottom.sync([]),this.bottom=new Vn(n.view,!1,e.bottomContainer)),this.top.syncClasses(),this.bottom.syncClasses();let t=n.state.facet(ln);if(t!=this.input){let i=t.filter(a=>a),s=[],r=[],o=[],l=[];for(let a of i){let h=this.specs.indexOf(a),c;h<0?(c=a(n.view),l.push(c)):(c=this.panels[h],c.update&&c.update(n)),s.push(c),(c.top?r:o).push(c)}this.specs=i,this.panels=s,this.top.sync(r),this.bottom.sync(o);for(let a of l)a.dom.classList.add("cm-panel"),a.mount&&a.mount()}else for(let i of this.panels)i.update&&i.update(n)}destroy(){this.top.sync([]),this.bottom.sync([])}},{provide:n=>R.scrollMargins.of(e=>{let t=e.plugin(n);return t&&{top:t.top.scrollMargin(),bottom:t.bottom.scrollMargin()}})});class Vn{constructor(e,t,i){this.view=e,this.top=t,this.container=i,this.dom=void 0,this.classes="",this.panels=[],this.syncClasses()}sync(e){for(let t of this.panels)t.destroy&&e.indexOf(t)<0&&t.destroy();this.panels=e,this.syncDOM()}syncDOM(){if(this.panels.length==0){this.dom&&(this.dom.remove(),this.dom=void 0);return}if(!this.dom){this.dom=document.createElement("div"),this.dom.className=this.top?"cm-panels cm-panels-top":"cm-panels cm-panels-bottom",this.dom.style[this.top?"top":"bottom"]="0";let t=this.container||this.view.dom;t.insertBefore(this.dom,this.top?t.firstChild:null)}let e=this.dom.firstChild;for(let t of this.panels)if(t.dom.parentNode==this.dom){for(;e!=t.dom;)e=Da(e);e=e.nextSibling}else this.dom.insertBefore(t.dom,e);for(;e;)e=Da(e)}scrollMargin(){return!this.dom||this.container?0:Math.max(0,this.top?this.dom.getBoundingClientRect().bottom-Math.max(0,this.view.scrollDOM.getBoundingClientRect().top):Math.min(innerHeight,this.view.scrollDOM.getBoundingClientRect().bottom)-this.dom.getBoundingClientRect().top)}syncClasses(){if(!(!this.container||this.classes==this.view.themeClasses)){for(let e of this.classes.split(" "))e&&this.container.classList.remove(e);for(let e of(this.classes=this.view.themeClasses).split(" "))e&&this.container.classList.add(e)}}}function Da(n){let e=n.nextSibling;return n.remove(),e}const ln=F.define({enables:bf});class bt extends ii{compare(e){return this==e||this.constructor==e.constructor&&this.eq(e)}eq(e){return!1}destroy(e){}}bt.prototype.elementClass="";bt.prototype.toDOM=void 0;bt.prototype.mapMode=Ee.TrackBefore;bt.prototype.startSide=bt.prototype.endSide=-1;bt.prototype.point=!0;const ss=F.define(),ug=F.define(),dg={class:"",renderEmptyElements:!1,elementStyle:"",markers:()=>U.empty,lineMarker:()=>null,widgetMarker:()=>null,lineMarkerChange:null,initialSpacer:null,updateSpacer:null,domEventHandlers:{},side:"before"},Yi=F.define();function xf(n){return[vf(),Yi.of({...dg,...n})]}const Pa=F.define({combine:n=>n.some(e=>e)});function vf(n){return[pg]}const pg=ne.fromClass(class{constructor(n){this.view=n,this.domAfter=null,this.prevViewport=n.viewport,this.dom=document.createElement("div"),this.dom.className="cm-gutters cm-gutters-before",this.dom.setAttribute("aria-hidden","true"),this.dom.style.minHeight=this.view.contentHeight/this.view.scaleY+"px",this.gutters=n.state.facet(Yi).map(e=>new Ra(n,e)),this.fixed=!n.state.facet(Pa);for(let e of this.gutters)e.config.side=="after"?this.getDOMAfter().appendChild(e.dom):this.dom.appendChild(e.dom);this.fixed&&(this.dom.style.position="sticky"),this.syncGutters(!1),n.scrollDOM.insertBefore(this.dom,n.contentDOM)}getDOMAfter(){return this.domAfter||(this.domAfter=document.createElement("div"),this.domAfter.className="cm-gutters cm-gutters-after",this.domAfter.setAttribute("aria-hidden","true"),this.domAfter.style.minHeight=this.view.contentHeight/this.view.scaleY+"px",this.domAfter.style.position=this.fixed?"sticky":"",this.view.scrollDOM.appendChild(this.domAfter)),this.domAfter}update(n){if(this.updateGutters(n)){let e=this.prevViewport,t=n.view.viewport,i=Math.min(e.to,t.to)-Math.max(e.from,t.from);this.syncGutters(i<(t.to-t.from)*.8)}if(n.geometryChanged){let e=this.view.contentHeight/this.view.scaleY+"px";this.dom.style.minHeight=e,this.domAfter&&(this.domAfter.style.minHeight=e)}this.view.state.facet(Pa)!=!this.fixed&&(this.fixed=!this.fixed,this.dom.style.position=this.fixed?"sticky":"",this.domAfter&&(this.domAfter.style.position=this.fixed?"sticky":"")),this.prevViewport=n.view.viewport}syncGutters(n){let e=this.dom.nextSibling;n&&(this.dom.remove(),this.domAfter&&this.domAfter.remove());let t=U.iter(this.view.state.facet(ss),this.view.viewport.from),i=[],s=this.gutters.map(r=>new mg(r,this.view.viewport,-this.view.documentPadding.top));for(let r of this.view.viewportLineBlocks)if(i.length&&(i=[]),Array.isArray(r.type)){let o=!0;for(let l of r.type)if(l.type==Pe.Text&&o){wo(t,i,l.from);for(let a of s)a.line(this.view,l,i);o=!1}else if(l.widget)for(let a of s)a.widget(this.view,l)}else if(r.type==Pe.Text){wo(t,i,r.from);for(let o of s)o.line(this.view,r,i)}else if(r.widget)for(let o of s)o.widget(this.view,r);for(let r of s)r.finish();n&&(this.view.scrollDOM.insertBefore(this.dom,e),this.domAfter&&this.view.scrollDOM.appendChild(this.domAfter))}updateGutters(n){let e=n.startState.facet(Yi),t=n.state.facet(Yi),i=n.docChanged||n.heightChanged||n.viewportChanged||!U.eq(n.startState.facet(ss),n.state.facet(ss),n.view.viewport.from,n.view.viewport.to);if(e==t)for(let s of this.gutters)s.update(n)&&(i=!0);else{i=!0;let s=[];for(let r of t){let o=e.indexOf(r);o<0?s.push(new Ra(this.view,r)):(this.gutters[o].update(n),s.push(this.gutters[o]))}for(let r of this.gutters)r.dom.remove(),s.indexOf(r)<0&&r.destroy();for(let r of s)r.config.side=="after"?this.getDOMAfter().appendChild(r.dom):this.dom.appendChild(r.dom);this.gutters=s}return i}destroy(){for(let n of this.gutters)n.destroy();this.dom.remove(),this.domAfter&&this.domAfter.remove()}},{provide:n=>R.scrollMargins.of(e=>{let t=e.plugin(n);if(!t||t.gutters.length==0||!t.fixed)return null;let i=t.dom.offsetWidth*e.scaleX,s=t.domAfter?t.domAfter.offsetWidth*e.scaleX:0;return e.textDirection==ee.LTR?{left:i,right:s}:{right:i,left:s}})});function Na(n){return Array.isArray(n)?n:[n]}function wo(n,e,t){for(;n.value&&n.from<=t;)n.from==t&&e.push(n.value),n.next()}class mg{constructor(e,t,i){this.gutter=e,this.height=i,this.i=0,this.cursor=U.iter(e.markers,t.from)}addElement(e,t,i){let{gutter:s}=this,r=(t.top-this.height)/e.scaleY,o=t.height/e.scaleY;if(this.i==s.elements.length){let l=new wf(e,o,r,i);s.elements.push(l),s.dom.appendChild(l.dom)}else s.elements[this.i].update(e,o,r,i);this.height=t.bottom,this.i++}line(e,t,i){let s=[];wo(this.cursor,s,t.from),i.length&&(s=s.concat(i));let r=this.gutter.config.lineMarker(e,t,s);r&&s.unshift(r);let o=this.gutter;s.length==0&&!o.config.renderEmptyElements||this.addElement(e,t,s)}widget(e,t){let i=this.gutter.config.widgetMarker(e,t.widget,t),s=i?[i]:null;for(let r of e.state.facet(ug)){let o=r(e,t.widget,t);o&&(s||(s=[])).push(o)}s&&this.addElement(e,t,s)}finish(){let e=this.gutter;for(;e.elements.length>this.i;){let t=e.elements.pop();e.dom.removeChild(t.dom),t.destroy()}}}class Ra{constructor(e,t){this.view=e,this.config=t,this.elements=[],this.spacer=null,this.dom=document.createElement("div"),this.dom.className="cm-gutter"+(this.config.class?" "+this.config.class:"");for(let i in t.domEventHandlers)this.dom.addEventListener(i,s=>{let r=s.target,o;if(r!=this.dom&&this.dom.contains(r)){for(;r.parentNode!=this.dom;)r=r.parentNode;let a=r.getBoundingClientRect();o=(a.top+a.bottom)/2}else o=s.clientY;let l=e.lineBlockAtHeight(o-e.documentTop);t.domEventHandlers[i](e,l,s)&&s.preventDefault()});this.markers=Na(t.markers(e)),t.initialSpacer&&(this.spacer=new wf(e,0,0,[t.initialSpacer(e)]),this.dom.appendChild(this.spacer.dom),this.spacer.dom.style.cssText+="visibility: hidden; pointer-events: none")}update(e){let t=this.markers;if(this.markers=Na(this.config.markers(e.view)),this.spacer&&this.config.updateSpacer){let s=this.config.updateSpacer(this.spacer.markers[0],e);s!=this.spacer.markers[0]&&this.spacer.update(e.view,0,0,[s])}let i=e.view.viewport;return!U.eq(this.markers,t,i.from,i.to)||(this.config.lineMarkerChange?this.config.lineMarkerChange(e):!1)}destroy(){for(let e of this.elements)e.destroy()}}class wf{constructor(e,t,i,s){this.height=-1,this.above=0,this.markers=[],this.dom=document.createElement("div"),this.dom.className="cm-gutterElement",this.update(e,t,i,s)}update(e,t,i,s){this.height!=t&&(this.height=t,this.dom.style.height=t+"px"),this.above!=i&&(this.dom.style.marginTop=(this.above=i)?i+"px":""),gg(this.markers,s)||this.setMarkers(e,s)}setMarkers(e,t){let i="cm-gutterElement",s=this.dom.firstChild;for(let r=0,o=0;;){let l=o,a=r<t.length?t[r++]:null,h=!1;if(a){let c=a.elementClass;c&&(i+=" "+c);for(let f=o;f<this.markers.length;f++)if(this.markers[f].compare(a)){l=f,h=!0;break}}else l=this.markers.length;for(;o<l;){let c=this.markers[o++];if(c.toDOM){c.destroy(s);let f=s.nextSibling;s.remove(),s=f}}if(!a)break;a.toDOM&&(h?s=s.nextSibling:this.dom.insertBefore(a.toDOM(e),s)),h&&o++}this.dom.className=i,this.markers=t}destroy(){this.setMarkers(null,[])}}function gg(n,e){if(n.length!=e.length)return!1;for(let t=0;t<n.length;t++)if(!n[t].compare(e[t]))return!1;return!0}const yg=F.define(),bg=F.define(),mi=F.define({combine(n){return nt(n,{formatNumber:String,domEventHandlers:{}},{domEventHandlers(e,t){let i=Object.assign({},e);for(let s in t){let r=i[s],o=t[s];i[s]=r?(l,a,h)=>r(l,a,h)||o(l,a,h):o}return i}})}});class cr extends bt{constructor(e){super(),this.number=e}eq(e){return this.number==e.number}toDOM(){return document.createTextNode(this.number)}}function fr(n,e){return n.state.facet(mi).formatNumber(e,n.state)}const xg=Yi.compute([mi],n=>({class:"cm-lineNumbers",renderEmptyElements:!1,markers(e){return e.state.facet(yg)},lineMarker(e,t,i){return i.some(s=>s.toDOM)?null:new cr(fr(e,e.state.doc.lineAt(t.from).number))},widgetMarker:(e,t,i)=>{for(let s of e.state.facet(bg)){let r=s(e,t,i);if(r)return r}return null},lineMarkerChange:e=>e.startState.facet(mi)!=e.state.facet(mi),initialSpacer(e){return new cr(fr(e,Ba(e.state.doc.lines)))},updateSpacer(e,t){let i=fr(t.view,Ba(t.view.state.doc.lines));return i==e.number?e:new cr(i)},domEventHandlers:n.facet(mi).domEventHandlers,side:"before"}));function nl(n={}){return[mi.of(n),vf(),xg]}function Ba(n){let e=9;for(;e<n;)e=e*10+9;return e}const vg=new class extends bt{constructor(){super(...arguments),this.elementClass="cm-activeLineGutter"}},wg=ss.compute(["selection"],n=>{let e=[],t=-1;for(let i of n.selection.ranges){let s=n.doc.lineAt(i.head).from;s>t&&(t=s,e.push(vg.range(s)))}return U.of(e)});function Sg(){return wg}const Sf=1024;let Og=0;class ur{constructor(e,t){this.from=e,this.to=t}}class K{constructor(e={}){this.id=Og++,this.perNode=!!e.perNode,this.deserialize=e.deserialize||(()=>{throw new Error("This node type doesn't define a deserialize function")})}add(e){if(this.perNode)throw new RangeError("Can't add per-node props to node types");return typeof e!="function"&&(e=We.match(e)),t=>{let i=e(t);return i===void 0?null:[this,i]}}}K.closedBy=new K({deserialize:n=>n.split(" ")});K.openedBy=new K({deserialize:n=>n.split(" ")});K.group=new K({deserialize:n=>n.split(" ")});K.isolate=new K({deserialize:n=>{if(n&&n!="rtl"&&n!="ltr"&&n!="auto")throw new RangeError("Invalid value for isolate: "+n);return n||"auto"}});K.contextHash=new K({perNode:!0});K.lookAhead=new K({perNode:!0});K.mounted=new K({perNode:!0});class xs{constructor(e,t,i){this.tree=e,this.overlay=t,this.parser=i}static get(e){return e&&e.props&&e.props[K.mounted.id]}}const kg=Object.create(null);class We{constructor(e,t,i,s=0){this.name=e,this.props=t,this.id=i,this.flags=s}static define(e){let t=e.props&&e.props.length?Object.create(null):kg,i=(e.top?1:0)|(e.skipped?2:0)|(e.error?4:0)|(e.name==null?8:0),s=new We(e.name||"",t,e.id,i);if(e.props){for(let r of e.props)if(Array.isArray(r)||(r=r(s)),r){if(r[0].perNode)throw new RangeError("Can't store a per-node prop on a node type");t[r[0].id]=r[1]}}return s}prop(e){return this.props[e.id]}get isTop(){return(this.flags&1)>0}get isSkipped(){return(this.flags&2)>0}get isError(){return(this.flags&4)>0}get isAnonymous(){return(this.flags&8)>0}is(e){if(typeof e=="string"){if(this.name==e)return!0;let t=this.prop(K.group);return t?t.indexOf(e)>-1:!1}return this.id==e}static match(e){let t=Object.create(null);for(let i in e)for(let s of i.split(" "))t[s]=e[i];return i=>{for(let s=i.prop(K.group),r=-1;r<(s?s.length:0);r++){let o=t[r<0?i.name:s[r]];if(o)return o}}}}We.none=new We("",Object.create(null),0,8);class sl{constructor(e){this.types=e;for(let t=0;t<e.length;t++)if(e[t].id!=t)throw new RangeError("Node type ids should correspond to array positions when creating a node set")}extend(...e){let t=[];for(let i of this.types){let s=null;for(let r of e){let o=r(i);o&&(s||(s=Object.assign({},i.props)),s[o[0].id]=o[1])}t.push(s?new We(i.name,s,i.id,i.flags):i)}return new sl(t)}}const Wn=new WeakMap,Ia=new WeakMap;var pe;(function(n){n[n.ExcludeBuffers=1]="ExcludeBuffers",n[n.IncludeAnonymous=2]="IncludeAnonymous",n[n.IgnoreMounts=4]="IgnoreMounts",n[n.IgnoreOverlays=8]="IgnoreOverlays"})(pe||(pe={}));class le{constructor(e,t,i,s,r){if(this.type=e,this.children=t,this.positions=i,this.length=s,this.props=null,r&&r.length){this.props=Object.create(null);for(let[o,l]of r)this.props[typeof o=="number"?o:o.id]=l}}toString(){let e=xs.get(this);if(e&&!e.overlay)return e.tree.toString();let t="";for(let i of this.children){let s=i.toString();s&&(t&&(t+=","),t+=s)}return this.type.name?(/\W/.test(this.type.name)&&!this.type.isError?JSON.stringify(this.type.name):this.type.name)+(t.length?"("+t+")":""):t}cursor(e=0){return new Oo(this.topNode,e)}cursorAt(e,t=0,i=0){let s=Wn.get(this)||this.topNode,r=new Oo(s);return r.moveTo(e,t),Wn.set(this,r._tree),r}get topNode(){return new Ye(this,0,0,null)}resolve(e,t=0){let i=an(Wn.get(this)||this.topNode,e,t,!1);return Wn.set(this,i),i}resolveInner(e,t=0){let i=an(Ia.get(this)||this.topNode,e,t,!0);return Ia.set(this,i),i}resolveStack(e,t=0){return Tg(this,e,t)}iterate(e){let{enter:t,leave:i,from:s=0,to:r=this.length}=e,o=e.mode||0,l=(o&pe.IncludeAnonymous)>0;for(let a=this.cursor(o|pe.IncludeAnonymous);;){let h=!1;if(a.from<=r&&a.to>=s&&(!l&&a.type.isAnonymous||t(a)!==!1)){if(a.firstChild())continue;h=!0}for(;h&&i&&(l||!a.type.isAnonymous)&&i(a),!a.nextSibling();){if(!a.parent())return;h=!0}}}prop(e){return e.perNode?this.props?this.props[e.id]:void 0:this.type.prop(e)}get propValues(){let e=[];if(this.props)for(let t in this.props)e.push([+t,this.props[t]]);return e}balance(e={}){return this.children.length<=8?this:ll(We.none,this.children,this.positions,0,this.children.length,0,this.length,(t,i,s)=>new le(this.type,t,i,s,this.propValues),e.makeTree||((t,i,s)=>new le(We.none,t,i,s)))}static build(e){return Mg(e)}}le.empty=new le(We.none,[],[],0);class rl{constructor(e,t){this.buffer=e,this.index=t}get id(){return this.buffer[this.index-4]}get start(){return this.buffer[this.index-3]}get end(){return this.buffer[this.index-2]}get size(){return this.buffer[this.index-1]}get pos(){return this.index}next(){this.index-=4}fork(){return new rl(this.buffer,this.index)}}class zt{constructor(e,t,i){this.buffer=e,this.length=t,this.set=i}get type(){return We.none}toString(){let e=[];for(let t=0;t<this.buffer.length;)e.push(this.childString(t)),t=this.buffer[t+3];return e.join(",")}childString(e){let t=this.buffer[e],i=this.buffer[e+3],s=this.set.types[t],r=s.name;if(/\W/.test(r)&&!s.isError&&(r=JSON.stringify(r)),e+=4,i==e)return r;let o=[];for(;e<i;)o.push(this.childString(e)),e=this.buffer[e+3];return r+"("+o.join(",")+")"}findChild(e,t,i,s,r){let{buffer:o}=this,l=-1;for(let a=e;a!=t&&!(Of(r,s,o[a+1],o[a+2])&&(l=a,i>0));a=o[a+3]);return l}slice(e,t,i){let s=this.buffer,r=new Uint16Array(t-e),o=0;for(let l=e,a=0;l<t;){r[a++]=s[l++],r[a++]=s[l++]-i;let h=r[a++]=s[l++]-i;r[a++]=s[l++]-e,o=Math.max(o,h)}return new zt(r,o,this.set)}}function Of(n,e,t,i){switch(n){case-2:return t<e;case-1:return i>=e&&t<e;case 0:return t<e&&i>e;case 1:return t<=e&&i>e;case 2:return i>e;case 4:return!0}}function an(n,e,t,i){for(var s;n.from==n.to||(t<1?n.from>=e:n.from>e)||(t>-1?n.to<=e:n.to<e);){let o=!i&&n instanceof Ye&&n.index<0?null:n.parent;if(!o)return n;n=o}let r=i?0:pe.IgnoreOverlays;if(i)for(let o=n,l=o.parent;l;o=l,l=o.parent)o instanceof Ye&&o.index<0&&((s=l.enter(e,t,r))===null||s===void 0?void 0:s.from)!=o.from&&(n=l);for(;;){let o=n.enter(e,t,r);if(!o)return n;n=o}}class kf{cursor(e=0){return new Oo(this,e)}getChild(e,t=null,i=null){let s=La(this,e,t,i);return s.length?s[0]:null}getChildren(e,t=null,i=null){return La(this,e,t,i)}resolve(e,t=0){return an(this,e,t,!1)}resolveInner(e,t=0){return an(this,e,t,!0)}matchContext(e){return So(this.parent,e)}enterUnfinishedNodesBefore(e){let t=this.childBefore(e),i=this;for(;t;){let s=t.lastChild;if(!s||s.to!=t.to)break;s.type.isError&&s.from==s.to?(i=t,t=s.prevSibling):t=s}return i}get node(){return this}get next(){return this.parent}}class Ye extends kf{constructor(e,t,i,s){super(),this._tree=e,this.from=t,this.index=i,this._parent=s}get type(){return this._tree.type}get name(){return this._tree.type.name}get to(){return this.from+this._tree.length}nextChild(e,t,i,s,r=0){for(let o=this;;){for(let{children:l,positions:a}=o._tree,h=t>0?l.length:-1;e!=h;e+=t){let c=l[e],f=a[e]+o.from;if(Of(s,i,f,f+c.length)){if(c instanceof zt){if(r&pe.ExcludeBuffers)continue;let u=c.findChild(0,c.buffer.length,t,i-f,s);if(u>-1)return new Wt(new Cg(o,c,e,f),null,u)}else if(r&pe.IncludeAnonymous||!c.type.isAnonymous||ol(c)){let u;if(!(r&pe.IgnoreMounts)&&(u=xs.get(c))&&!u.overlay)return new Ye(u.tree,f,e,o);let d=new Ye(c,f,e,o);return r&pe.IncludeAnonymous||!d.type.isAnonymous?d:d.nextChild(t<0?c.children.length-1:0,t,i,s)}}}if(r&pe.IncludeAnonymous||!o.type.isAnonymous||(o.index>=0?e=o.index+t:e=t<0?-1:o._parent._tree.children.length,o=o._parent,!o))return null}}get firstChild(){return this.nextChild(0,1,0,4)}get lastChild(){return this.nextChild(this._tree.children.length-1,-1,0,4)}childAfter(e){return this.nextChild(0,1,e,2)}childBefore(e){return this.nextChild(this._tree.children.length-1,-1,e,-2)}enter(e,t,i=0){let s;if(!(i&pe.IgnoreOverlays)&&(s=xs.get(this._tree))&&s.overlay){let r=e-this.from;for(let{from:o,to:l}of s.overlay)if((t>0?o<=r:o<r)&&(t<0?l>=r:l>r))return new Ye(s.tree,s.overlay[0].from+this.from,-1,this)}return this.nextChild(0,1,e,t,i)}nextSignificantParent(){let e=this;for(;e.type.isAnonymous&&e._parent;)e=e._parent;return e}get parent(){return this._parent?this._parent.nextSignificantParent():null}get nextSibling(){return this._parent&&this.index>=0?this._parent.nextChild(this.index+1,1,0,4):null}get prevSibling(){return this._parent&&this.index>=0?this._parent.nextChild(this.index-1,-1,0,4):null}get tree(){return this._tree}toTree(){return this._tree}toString(){return this._tree.toString()}}function La(n,e,t,i){let s=n.cursor(),r=[];if(!s.firstChild())return r;if(t!=null){for(let o=!1;!o;)if(o=s.type.is(t),!s.nextSibling())return r}for(;;){if(i!=null&&s.type.is(i))return r;if(s.type.is(e)&&r.push(s.node),!s.nextSibling())return i==null?r:[]}}function So(n,e,t=e.length-1){for(let i=n;t>=0;i=i.parent){if(!i)return!1;if(!i.type.isAnonymous){if(e[t]&&e[t]!=i.name)return!1;t--}}return!0}class Cg{constructor(e,t,i,s){this.parent=e,this.buffer=t,this.index=i,this.start=s}}class Wt extends kf{get name(){return this.type.name}get from(){return this.context.start+this.context.buffer.buffer[this.index+1]}get to(){return this.context.start+this.context.buffer.buffer[this.index+2]}constructor(e,t,i){super(),this.context=e,this._parent=t,this.index=i,this.type=e.buffer.set.types[e.buffer.buffer[i]]}child(e,t,i){let{buffer:s}=this.context,r=s.findChild(this.index+4,s.buffer[this.index+3],e,t-this.context.start,i);return r<0?null:new Wt(this.context,this,r)}get firstChild(){return this.child(1,0,4)}get lastChild(){return this.child(-1,0,4)}childAfter(e){return this.child(1,e,2)}childBefore(e){return this.child(-1,e,-2)}enter(e,t,i=0){if(i&pe.ExcludeBuffers)return null;let{buffer:s}=this.context,r=s.findChild(this.index+4,s.buffer[this.index+3],t>0?1:-1,e-this.context.start,t);return r<0?null:new Wt(this.context,this,r)}get parent(){return this._parent||this.context.parent.nextSignificantParent()}externalSibling(e){return this._parent?null:this.context.parent.nextChild(this.context.index+e,e,0,4)}get nextSibling(){let{buffer:e}=this.context,t=e.buffer[this.index+3];return t<(this._parent?e.buffer[this._parent.index+3]:e.buffer.length)?new Wt(this.context,this._parent,t):this.externalSibling(1)}get prevSibling(){let{buffer:e}=this.context,t=this._parent?this._parent.index+4:0;return this.index==t?this.externalSibling(-1):new Wt(this.context,this._parent,e.findChild(t,this.index,-1,0,4))}get tree(){return null}toTree(){let e=[],t=[],{buffer:i}=this.context,s=this.index+4,r=i.buffer[this.index+3];if(r>s){let o=i.buffer[this.index+1];e.push(i.slice(s,r,o)),t.push(0)}return new le(this.type,e,t,this.to-this.from)}toString(){return this.context.buffer.childString(this.index)}}function Cf(n){if(!n.length)return null;let e=0,t=n[0];for(let r=1;r<n.length;r++){let o=n[r];(o.from>t.from||o.to<t.to)&&(t=o,e=r)}let i=t instanceof Ye&&t.index<0?null:t.parent,s=n.slice();return i?s[e]=i:s.splice(e,1),new Ag(s,t)}class Ag{constructor(e,t){this.heads=e,this.node=t}get next(){return Cf(this.heads)}}function Tg(n,e,t){let i=n.resolveInner(e,t),s=null;for(let r=i instanceof Ye?i:i.context.parent;r;r=r.parent)if(r.index<0){let o=r.parent;(s||(s=[i])).push(o.resolve(e,t)),r=o}else{let o=xs.get(r.tree);if(o&&o.overlay&&o.overlay[0].from<=e&&o.overlay[o.overlay.length-1].to>=e){let l=new Ye(o.tree,o.overlay[0].from+r.from,-1,r);(s||(s=[i])).push(an(l,e,t,!1))}}return s?Cf(s):i}class Oo{get name(){return this.type.name}constructor(e,t=0){if(this.mode=t,this.buffer=null,this.stack=[],this.index=0,this.bufferNode=null,e instanceof Ye)this.yieldNode(e);else{this._tree=e.context.parent,this.buffer=e.context;for(let i=e._parent;i;i=i._parent)this.stack.unshift(i.index);this.bufferNode=e,this.yieldBuf(e.index)}}yieldNode(e){return e?(this._tree=e,this.type=e.type,this.from=e.from,this.to=e.to,!0):!1}yieldBuf(e,t){this.index=e;let{start:i,buffer:s}=this.buffer;return this.type=t||s.set.types[s.buffer[e]],this.from=i+s.buffer[e+1],this.to=i+s.buffer[e+2],!0}yield(e){return e?e instanceof Ye?(this.buffer=null,this.yieldNode(e)):(this.buffer=e.context,this.yieldBuf(e.index,e.type)):!1}toString(){return this.buffer?this.buffer.buffer.childString(this.index):this._tree.toString()}enterChild(e,t,i){if(!this.buffer)return this.yield(this._tree.nextChild(e<0?this._tree._tree.children.length-1:0,e,t,i,this.mode));let{buffer:s}=this.buffer,r=s.findChild(this.index+4,s.buffer[this.index+3],e,t-this.buffer.start,i);return r<0?!1:(this.stack.push(this.index),this.yieldBuf(r))}firstChild(){return this.enterChild(1,0,4)}lastChild(){return this.enterChild(-1,0,4)}childAfter(e){return this.enterChild(1,e,2)}childBefore(e){return this.enterChild(-1,e,-2)}enter(e,t,i=this.mode){return this.buffer?i&pe.ExcludeBuffers?!1:this.enterChild(1,e,t):this.yield(this._tree.enter(e,t,i))}parent(){if(!this.buffer)return this.yieldNode(this.mode&pe.IncludeAnonymous?this._tree._parent:this._tree.parent);if(this.stack.length)return this.yieldBuf(this.stack.pop());let e=this.mode&pe.IncludeAnonymous?this.buffer.parent:this.buffer.parent.nextSignificantParent();return this.buffer=null,this.yieldNode(e)}sibling(e){if(!this.buffer)return this._tree._parent?this.yield(this._tree.index<0?null:this._tree._parent.nextChild(this._tree.index+e,e,0,4,this.mode)):!1;let{buffer:t}=this.buffer,i=this.stack.length-1;if(e<0){let s=i<0?0:this.stack[i]+4;if(this.index!=s)return this.yieldBuf(t.findChild(s,this.index,-1,0,4))}else{let s=t.buffer[this.index+3];if(s<(i<0?t.buffer.length:t.buffer[this.stack[i]+3]))return this.yieldBuf(s)}return i<0?this.yield(this.buffer.parent.nextChild(this.buffer.index+e,e,0,4,this.mode)):!1}nextSibling(){return this.sibling(1)}prevSibling(){return this.sibling(-1)}atLastNode(e){let t,i,{buffer:s}=this;if(s){if(e>0){if(this.index<s.buffer.buffer.length)return!1}else for(let r=0;r<this.index;r++)if(s.buffer.buffer[r+3]<this.index)return!1;({index:t,parent:i}=s)}else({index:t,_parent:i}=this._tree);for(;i;{index:t,_parent:i}=i)if(t>-1)for(let r=t+e,o=e<0?-1:i._tree.children.length;r!=o;r+=e){let l=i._tree.children[r];if(this.mode&pe.IncludeAnonymous||l instanceof zt||!l.type.isAnonymous||ol(l))return!1}return!0}move(e,t){if(t&&this.enterChild(e,0,4))return!0;for(;;){if(this.sibling(e))return!0;if(this.atLastNode(e)||!this.parent())return!1}}next(e=!0){return this.move(1,e)}prev(e=!0){return this.move(-1,e)}moveTo(e,t=0){for(;(this.from==this.to||(t<1?this.from>=e:this.from>e)||(t>-1?this.to<=e:this.to<e))&&this.parent(););for(;this.enterChild(1,e,t););return this}get node(){if(!this.buffer)return this._tree;let e=this.bufferNode,t=null,i=0;if(e&&e.context==this.buffer)e:for(let s=this.index,r=this.stack.length;r>=0;){for(let o=e;o;o=o._parent)if(o.index==s){if(s==this.index)return o;t=o,i=r+1;break e}s=this.stack[--r]}for(let s=i;s<this.stack.length;s++)t=new Wt(this.buffer,t,this.stack[s]);return this.bufferNode=new Wt(this.buffer,t,this.index)}get tree(){return this.buffer?null:this._tree._tree}iterate(e,t){for(let i=0;;){let s=!1;if(this.type.isAnonymous||e(this)!==!1){if(this.firstChild()){i++;continue}this.type.isAnonymous||(s=!0)}for(;;){if(s&&t&&t(this),s=this.type.isAnonymous,!i)return;if(this.nextSibling())break;this.parent(),i--,s=!0}}}matchContext(e){if(!this.buffer)return So(this.node.parent,e);let{buffer:t}=this.buffer,{types:i}=t.set;for(let s=e.length-1,r=this.stack.length-1;s>=0;r--){if(r<0)return So(this._tree,e,s);let o=i[t.buffer[this.stack[r]]];if(!o.isAnonymous){if(e[s]&&e[s]!=o.name)return!1;s--}}return!0}}function ol(n){return n.children.some(e=>e instanceof zt||!e.type.isAnonymous||ol(e))}function Mg(n){var e;let{buffer:t,nodeSet:i,maxBufferLength:s=Sf,reused:r=[],minRepeatType:o=i.types.length}=n,l=Array.isArray(t)?new rl(t,t.length):t,a=i.types,h=0,c=0;function f(k,T,M,N,w,P){let{id:V,start:I,end:z,size:$}=l,q=c,de=h;for(;$<0;)if(l.next(),$==-1){let ye=r[V];M.push(ye),N.push(I-k);return}else if($==-3){h=V;return}else if($==-4){c=V;return}else throw new RangeError(`Unrecognized record size: ${$}`);let H=a[V],ze,se,Oe=I-k;if(z-I<=s&&(se=S(l.pos-T,w))){let ye=new Uint16Array(se.size-se.skip),ke=l.pos-se.size,je=ye.length;for(;l.pos>ke;)je=y(se.start,ye,je);ze=new zt(ye,z-se.start,i),Oe=se.start-k}else{let ye=l.pos-$;l.next();let ke=[],je=[],rt=V>=o?V:-1,vt=0,ai=z;for(;l.pos>ye;)rt>=0&&l.id==rt&&l.size>=0?(l.end<=ai-s&&(m(ke,je,I,vt,l.end,ai,rt,q,de),vt=ke.length,ai=l.end),l.next()):P>2500?u(I,ye,ke,je):f(I,ye,ke,je,rt,P+1);if(rt>=0&&vt>0&&vt<ke.length&&m(ke,je,I,vt,I,ai,rt,q,de),ke.reverse(),je.reverse(),rt>-1&&vt>0){let b=d(H,de);ze=ll(H,ke,je,0,ke.length,0,z-I,b,b)}else ze=x(H,ke,je,z-I,q-z,de)}M.push(ze),N.push(Oe)}function u(k,T,M,N){let w=[],P=0,V=-1;for(;l.pos>T;){let{id:I,start:z,end:$,size:q}=l;if(q>4)l.next();else{if(V>-1&&z<V)break;V<0&&(V=$-s),w.push(I,z,$),P++,l.next()}}if(P){let I=new Uint16Array(P*4),z=w[w.length-2];for(let $=w.length-3,q=0;$>=0;$-=3)I[q++]=w[$],I[q++]=w[$+1]-z,I[q++]=w[$+2]-z,I[q++]=q;M.push(new zt(I,w[2]-z,i)),N.push(z-k)}}function d(k,T){return(M,N,w)=>{let P=0,V=M.length-1,I,z;if(V>=0&&(I=M[V])instanceof le){if(!V&&I.type==k&&I.length==w)return I;(z=I.prop(K.lookAhead))&&(P=N[V]+I.length+z)}return x(k,M,N,w,P,T)}}function m(k,T,M,N,w,P,V,I,z){let $=[],q=[];for(;k.length>N;)$.push(k.pop()),q.push(T.pop()+M-w);k.push(x(i.types[V],$,q,P-w,I-P,z)),T.push(w-M)}function x(k,T,M,N,w,P,V){if(P){let I=[K.contextHash,P];V=V?[I].concat(V):[I]}if(w>25){let I=[K.lookAhead,w];V=V?[I].concat(V):[I]}return new le(k,T,M,N,V)}function S(k,T){let M=l.fork(),N=0,w=0,P=0,V=M.end-s,I={size:0,start:0,skip:0};e:for(let z=M.pos-k;M.pos>z;){let $=M.size;if(M.id==T&&$>=0){I.size=N,I.start=w,I.skip=P,P+=4,N+=4,M.next();continue}let q=M.pos-$;if($<0||q<z||M.start<V)break;let de=M.id>=o?4:0,H=M.start;for(M.next();M.pos>q;){if(M.size<0)if(M.size==-3)de+=4;else break e;else M.id>=o&&(de+=4);M.next()}w=H,N+=$,P+=de}return(T<0||N==k)&&(I.size=N,I.start=w,I.skip=P),I.size>4?I:void 0}function y(k,T,M){let{id:N,start:w,end:P,size:V}=l;if(l.next(),V>=0&&N<o){let I=M;if(V>4){let z=l.pos-(V-4);for(;l.pos>z;)M=y(k,T,M)}T[--M]=I,T[--M]=P-k,T[--M]=w-k,T[--M]=N}else V==-3?h=N:V==-4&&(c=N);return M}let O=[],v=[];for(;l.pos>0;)f(n.start||0,n.bufferStart||0,O,v,-1,0);let g=(e=n.length)!==null&&e!==void 0?e:O.length?v[0]+O[0].length:0;return new le(a[n.topID],O.reverse(),v.reverse(),g)}const Fa=new WeakMap;function rs(n,e){if(!n.isAnonymous||e instanceof zt||e.type!=n)return 1;let t=Fa.get(e);if(t==null){t=1;for(let i of e.children){if(i.type!=n||!(i instanceof le)){t=1;break}t+=rs(n,i)}Fa.set(e,t)}return t}function ll(n,e,t,i,s,r,o,l,a){let h=0;for(let m=i;m<s;m++)h+=rs(n,e[m]);let c=Math.ceil(h*1.5/8),f=[],u=[];function d(m,x,S,y,O){for(let v=S;v<y;){let g=v,k=x[v],T=rs(n,m[v]);for(v++;v<y;v++){let M=rs(n,m[v]);if(T+M>=c)break;T+=M}if(v==g+1){if(T>c){let M=m[g];d(M.children,M.positions,0,M.children.length,x[g]+O);continue}f.push(m[g])}else{let M=x[v-1]+m[v-1].length-k;f.push(ll(n,m,x,g,v,k,M,null,a))}u.push(k+O-r)}}return d(e,t,i,s,0),(l||a)(f,u,o)}class ei{constructor(e,t,i,s,r=!1,o=!1){this.from=e,this.to=t,this.tree=i,this.offset=s,this.open=(r?1:0)|(o?2:0)}get openStart(){return(this.open&1)>0}get openEnd(){return(this.open&2)>0}static addTree(e,t=[],i=!1){let s=[new ei(0,e.length,e,0,!1,i)];for(let r of t)r.to>e.length&&s.push(r);return s}static applyChanges(e,t,i=128){if(!t.length)return e;let s=[],r=1,o=e.length?e[0]:null;for(let l=0,a=0,h=0;;l++){let c=l<t.length?t[l]:null,f=c?c.fromA:1e9;if(f-a>=i)for(;o&&o.from<f;){let u=o;if(a>=u.from||f<=u.to||h){let d=Math.max(u.from,a)-h,m=Math.min(u.to,f)-h;u=d>=m?null:new ei(d,m,u.tree,u.offset+h,l>0,!!c)}if(u&&s.push(u),o.to>f)break;o=r<e.length?e[r++]:null}if(!c)break;a=c.toA,h=c.toA-c.toB}return s}}class Af{startParse(e,t,i){return typeof e=="string"&&(e=new Eg(e)),i=i?i.length?i.map(s=>new ur(s.from,s.to)):[new ur(0,0)]:[new ur(0,e.length)],this.createParse(e,t||[],i)}parse(e,t,i){let s=this.startParse(e,t,i);for(;;){let r=s.advance();if(r)return r}}}class Eg{constructor(e){this.string=e}get length(){return this.string.length}chunk(e){return this.string.slice(e)}get lineChunks(){return!1}read(e,t){return this.string.slice(e,t)}}new K({perNode:!0});var Va={};class vs{constructor(e,t,i,s,r,o,l,a,h,c=0,f){this.p=e,this.stack=t,this.state=i,this.reducePos=s,this.pos=r,this.score=o,this.buffer=l,this.bufferBase=a,this.curContext=h,this.lookAhead=c,this.parent=f}toString(){return`[${this.stack.filter((e,t)=>t%3==0).concat(this.state)}]@${this.pos}${this.score?"!"+this.score:""}`}static start(e,t,i=0){let s=e.parser.context;return new vs(e,[],t,i,i,0,[],0,s?new Wa(s,s.start):null,0,null)}get context(){return this.curContext?this.curContext.context:null}pushState(e,t){this.stack.push(this.state,t,this.bufferBase+this.buffer.length),this.state=e}reduce(e){var t;let i=e>>19,s=e&65535,{parser:r}=this.p,o=this.reducePos<this.pos-25;o&&this.setLookAhead(this.pos);let l=r.dynamicPrecedence(s);if(l&&(this.score+=l),i==0){this.pushState(r.getGoto(this.state,s,!0),this.reducePos),s<r.minRepeatTerm&&this.storeNode(s,this.reducePos,this.reducePos,o?8:4,!0),this.reduceContext(s,this.reducePos);return}let a=this.stack.length-(i-1)*3-(e&262144?6:0),h=a?this.stack[a-2]:this.p.ranges[0].from,c=this.reducePos-h;c>=2e3&&!(!((t=this.p.parser.nodeSet.types[s])===null||t===void 0)&&t.isAnonymous)&&(h==this.p.lastBigReductionStart?(this.p.bigReductionCount++,this.p.lastBigReductionSize=c):this.p.lastBigReductionSize<c&&(this.p.bigReductionCount=1,this.p.lastBigReductionStart=h,this.p.lastBigReductionSize=c));let f=a?this.stack[a-1]:0,u=this.bufferBase+this.buffer.length-f;if(s<r.minRepeatTerm||e&131072){let d=r.stateFlag(this.state,1)?this.pos:this.reducePos;this.storeNode(s,h,d,u+4,!0)}if(e&262144)this.state=this.stack[a];else{let d=this.stack[a-3];this.state=r.getGoto(d,s,!0)}for(;this.stack.length>a;)this.stack.pop();this.reduceContext(s,h)}storeNode(e,t,i,s=4,r=!1){if(e==0&&(!this.stack.length||this.stack[this.stack.length-1]<this.buffer.length+this.bufferBase)){let o=this,l=this.buffer.length;if(l==0&&o.parent&&(l=o.bufferBase-o.parent.bufferBase,o=o.parent),l>0&&o.buffer[l-4]==0&&o.buffer[l-1]>-1){if(t==i)return;if(o.buffer[l-2]>=t){o.buffer[l-2]=i;return}}}if(!r||this.pos==i)this.buffer.push(e,t,i,s);else{let o=this.buffer.length;if(o>0&&this.buffer[o-4]!=0){let l=!1;for(let a=o;a>0&&this.buffer[a-2]>i;a-=4)if(this.buffer[a-1]>=0){l=!0;break}if(l)for(;o>0&&this.buffer[o-2]>i;)this.buffer[o]=this.buffer[o-4],this.buffer[o+1]=this.buffer[o-3],this.buffer[o+2]=this.buffer[o-2],this.buffer[o+3]=this.buffer[o-1],o-=4,s>4&&(s-=4)}this.buffer[o]=e,this.buffer[o+1]=t,this.buffer[o+2]=i,this.buffer[o+3]=s}}shift(e,t,i,s){if(e&131072)this.pushState(e&65535,this.pos);else if((e&262144)==0){let r=e,{parser:o}=this.p;(s>this.pos||t<=o.maxNode)&&(this.pos=s,o.stateFlag(r,1)||(this.reducePos=s)),this.pushState(r,i),this.shiftContext(t,i),t<=o.maxNode&&this.buffer.push(t,i,s,4)}else this.pos=s,this.shiftContext(t,i),t<=this.p.parser.maxNode&&this.buffer.push(t,i,s,4)}apply(e,t,i,s){e&65536?this.reduce(e):this.shift(e,t,i,s)}useNode(e,t){let i=this.p.reused.length-1;(i<0||this.p.reused[i]!=e)&&(this.p.reused.push(e),i++);let s=this.pos;this.reducePos=this.pos=s+e.length,this.pushState(t,s),this.buffer.push(i,s,this.reducePos,-1),this.curContext&&this.updateContext(this.curContext.tracker.reuse(this.curContext.context,e,this,this.p.stream.reset(this.pos-e.length)))}split(){let e=this,t=e.buffer.length;for(;t>0&&e.buffer[t-2]>e.reducePos;)t-=4;let i=e.buffer.slice(t),s=e.bufferBase+t;for(;e&&s==e.bufferBase;)e=e.parent;return new vs(this.p,this.stack.slice(),this.state,this.reducePos,this.pos,this.score,i,s,this.curContext,this.lookAhead,e)}recoverByDelete(e,t){let i=e<=this.p.parser.maxNode;i&&this.storeNode(e,this.pos,t,4),this.storeNode(0,this.pos,t,i?8:4),this.pos=this.reducePos=t,this.score-=190}canShift(e){for(let t=new Dg(this);;){let i=this.p.parser.stateSlot(t.state,4)||this.p.parser.hasAction(t.state,e);if(i==0)return!1;if((i&65536)==0)return!0;t.reduce(i)}}recoverByInsert(e){if(this.stack.length>=300)return[];let t=this.p.parser.nextStates(this.state);if(t.length>8||this.stack.length>=120){let s=[];for(let r=0,o;r<t.length;r+=2)(o=t[r+1])!=this.state&&this.p.parser.hasAction(o,e)&&s.push(t[r],o);if(this.stack.length<120)for(let r=0;s.length<8&&r<t.length;r+=2){let o=t[r+1];s.some((l,a)=>a&1&&l==o)||s.push(t[r],o)}t=s}let i=[];for(let s=0;s<t.length&&i.length<4;s+=2){let r=t[s+1];if(r==this.state)continue;let o=this.split();o.pushState(r,this.pos),o.storeNode(0,o.pos,o.pos,4,!0),o.shiftContext(t[s],this.pos),o.reducePos=this.pos,o.score-=200,i.push(o)}return i}forceReduce(){let{parser:e}=this.p,t=e.stateSlot(this.state,5);if((t&65536)==0)return!1;if(!e.validAction(this.state,t)){let i=t>>19,s=t&65535,r=this.stack.length-i*3;if(r<0||e.getGoto(this.stack[r],s,!1)<0){let o=this.findForcedReduction();if(o==null)return!1;t=o}this.storeNode(0,this.pos,this.pos,4,!0),this.score-=100}return this.reducePos=this.pos,this.reduce(t),!0}findForcedReduction(){let{parser:e}=this.p,t=[],i=(s,r)=>{if(!t.includes(s))return t.push(s),e.allActions(s,o=>{if(!(o&393216))if(o&65536){let l=(o>>19)-r;if(l>1){let a=o&65535,h=this.stack.length-l*3;if(h>=0&&e.getGoto(this.stack[h],a,!1)>=0)return l<<19|65536|a}}else{let l=i(o,r+1);if(l!=null)return l}})};return i(this.state,0)}forceAll(){for(;!this.p.parser.stateFlag(this.state,2);)if(!this.forceReduce()){this.storeNode(0,this.pos,this.pos,4,!0);break}return this}get deadEnd(){if(this.stack.length!=3)return!1;let{parser:e}=this.p;return e.data[e.stateSlot(this.state,1)]==65535&&!e.stateSlot(this.state,4)}restart(){this.storeNode(0,this.pos,this.pos,4,!0),this.state=this.stack[0],this.stack.length=0}sameState(e){if(this.state!=e.state||this.stack.length!=e.stack.length)return!1;for(let t=0;t<this.stack.length;t+=3)if(this.stack[t]!=e.stack[t])return!1;return!0}get parser(){return this.p.parser}dialectEnabled(e){return this.p.parser.dialect.flags[e]}shiftContext(e,t){this.curContext&&this.updateContext(this.curContext.tracker.shift(this.curContext.context,e,this,this.p.stream.reset(t)))}reduceContext(e,t){this.curContext&&this.updateContext(this.curContext.tracker.reduce(this.curContext.context,e,this,this.p.stream.reset(t)))}emitContext(){let e=this.buffer.length-1;(e<0||this.buffer[e]!=-3)&&this.buffer.push(this.curContext.hash,this.pos,this.pos,-3)}emitLookAhead(){let e=this.buffer.length-1;(e<0||this.buffer[e]!=-4)&&this.buffer.push(this.lookAhead,this.pos,this.pos,-4)}updateContext(e){if(e!=this.curContext.context){let t=new Wa(this.curContext.tracker,e);t.hash!=this.curContext.hash&&this.emitContext(),this.curContext=t}}setLookAhead(e){e>this.lookAhead&&(this.emitLookAhead(),this.lookAhead=e)}close(){this.curContext&&this.curContext.tracker.strict&&this.emitContext(),this.lookAhead>0&&this.emitLookAhead()}}class Wa{constructor(e,t){this.tracker=e,this.context=t,this.hash=e.strict?e.hash(t):0}}class Dg{constructor(e){this.start=e,this.state=e.state,this.stack=e.stack,this.base=this.stack.length}reduce(e){let t=e&65535,i=e>>19;i==0?(this.stack==this.start.stack&&(this.stack=this.stack.slice()),this.stack.push(this.state,0,0),this.base+=3):this.base-=(i-1)*3;let s=this.start.p.parser.getGoto(this.stack[this.base-3],t,!0);this.state=s}}class ws{constructor(e,t,i){this.stack=e,this.pos=t,this.index=i,this.buffer=e.buffer,this.index==0&&this.maybeNext()}static create(e,t=e.bufferBase+e.buffer.length){return new ws(e,t,t-e.bufferBase)}maybeNext(){let e=this.stack.parent;e!=null&&(this.index=this.stack.bufferBase-e.bufferBase,this.stack=e,this.buffer=e.buffer)}get id(){return this.buffer[this.index-4]}get start(){return this.buffer[this.index-3]}get end(){return this.buffer[this.index-2]}get size(){return this.buffer[this.index-1]}next(){this.index-=4,this.pos-=4,this.index==0&&this.maybeNext()}fork(){return new ws(this.stack,this.pos,this.index)}}function _n(n,e=Uint16Array){if(typeof n!="string")return n;let t=null;for(let i=0,s=0;i<n.length;){let r=0;for(;;){let o=n.charCodeAt(i++),l=!1;if(o==126){r=65535;break}o>=92&&o--,o>=34&&o--;let a=o-32;if(a>=46&&(a-=46,l=!0),r+=a,l)break;r*=46}t?t[s++]=r:t=new e(r)}return t}class os{constructor(){this.start=-1,this.value=-1,this.end=-1,this.extended=-1,this.lookAhead=0,this.mask=0,this.context=0}}const _a=new os;class Pg{constructor(e,t){this.input=e,this.ranges=t,this.chunk="",this.chunkOff=0,this.chunk2="",this.chunk2Pos=0,this.next=-1,this.token=_a,this.rangeIndex=0,this.pos=this.chunkPos=t[0].from,this.range=t[0],this.end=t[t.length-1].to,this.readNext()}resolveOffset(e,t){let i=this.range,s=this.rangeIndex,r=this.pos+e;for(;r<i.from;){if(!s)return null;let o=this.ranges[--s];r-=i.from-o.to,i=o}for(;t<0?r>i.to:r>=i.to;){if(s==this.ranges.length-1)return null;let o=this.ranges[++s];r+=o.from-i.to,i=o}return r}clipPos(e){if(e>=this.range.from&&e<this.range.to)return e;for(let t of this.ranges)if(t.to>e)return Math.max(e,t.from);return this.end}peek(e){let t=this.chunkOff+e,i,s;if(t>=0&&t<this.chunk.length)i=this.pos+e,s=this.chunk.charCodeAt(t);else{let r=this.resolveOffset(e,1);if(r==null)return-1;if(i=r,i>=this.chunk2Pos&&i<this.chunk2Pos+this.chunk2.length)s=this.chunk2.charCodeAt(i-this.chunk2Pos);else{let o=this.rangeIndex,l=this.range;for(;l.to<=i;)l=this.ranges[++o];this.chunk2=this.input.chunk(this.chunk2Pos=i),i+this.chunk2.length>l.to&&(this.chunk2=this.chunk2.slice(0,l.to-i)),s=this.chunk2.charCodeAt(0)}}return i>=this.token.lookAhead&&(this.token.lookAhead=i+1),s}acceptToken(e,t=0){let i=t?this.resolveOffset(t,-1):this.pos;if(i==null||i<this.token.start)throw new RangeError("Token end out of bounds");this.token.value=e,this.token.end=i}acceptTokenTo(e,t){this.token.value=e,this.token.end=t}getChunk(){if(this.pos>=this.chunk2Pos&&this.pos<this.chunk2Pos+this.chunk2.length){let{chunk:e,chunkPos:t}=this;this.chunk=this.chunk2,this.chunkPos=this.chunk2Pos,this.chunk2=e,this.chunk2Pos=t,this.chunkOff=this.pos-this.chunkPos}else{this.chunk2=this.chunk,this.chunk2Pos=this.chunkPos;let e=this.input.chunk(this.pos),t=this.pos+e.length;this.chunk=t>this.range.to?e.slice(0,this.range.to-this.pos):e,this.chunkPos=this.pos,this.chunkOff=0}}readNext(){return this.chunkOff>=this.chunk.length&&(this.getChunk(),this.chunkOff==this.chunk.length)?this.next=-1:this.next=this.chunk.charCodeAt(this.chunkOff)}advance(e=1){for(this.chunkOff+=e;this.pos+e>=this.range.to;){if(this.rangeIndex==this.ranges.length-1)return this.setDone();e-=this.range.to-this.pos,this.range=this.ranges[++this.rangeIndex],this.pos=this.range.from}return this.pos+=e,this.pos>=this.token.lookAhead&&(this.token.lookAhead=this.pos+1),this.readNext()}setDone(){return this.pos=this.chunkPos=this.end,this.range=this.ranges[this.rangeIndex=this.ranges.length-1],this.chunk="",this.next=-1}reset(e,t){if(t?(this.token=t,t.start=e,t.lookAhead=e+1,t.value=t.extended=-1):this.token=_a,this.pos!=e){if(this.pos=e,e==this.end)return this.setDone(),this;for(;e<this.range.from;)this.range=this.ranges[--this.rangeIndex];for(;e>=this.range.to;)this.range=this.ranges[++this.rangeIndex];e>=this.chunkPos&&e<this.chunkPos+this.chunk.length?this.chunkOff=e-this.chunkPos:(this.chunk="",this.chunkOff=0),this.readNext()}return this}read(e,t){if(e>=this.chunkPos&&t<=this.chunkPos+this.chunk.length)return this.chunk.slice(e-this.chunkPos,t-this.chunkPos);if(e>=this.chunk2Pos&&t<=this.chunk2Pos+this.chunk2.length)return this.chunk2.slice(e-this.chunk2Pos,t-this.chunk2Pos);if(e>=this.range.from&&t<=this.range.to)return this.input.read(e,t);let i="";for(let s of this.ranges){if(s.from>=t)break;s.to>e&&(i+=this.input.read(Math.max(s.from,e),Math.min(s.to,t)))}return i}}class wi{constructor(e,t){this.data=e,this.id=t}token(e,t){let{parser:i}=t.p;Ng(this.data,e,t,this.id,i.data,i.tokenPrecTable)}}wi.prototype.contextual=wi.prototype.fallback=wi.prototype.extend=!1;wi.prototype.fallback=wi.prototype.extend=!1;class Tf{constructor(e,t={}){this.token=e,this.contextual=!!t.contextual,this.fallback=!!t.fallback,this.extend=!!t.extend}}function Ng(n,e,t,i,s,r){let o=0,l=1<<i,{dialect:a}=t.p.parser;e:for(;(l&n[o])!=0;){let h=n[o+1];for(let d=o+3;d<h;d+=2)if((n[d+1]&l)>0){let m=n[d];if(a.allows(m)&&(e.token.value==-1||e.token.value==m||Rg(m,e.token.value,s,r))){e.acceptToken(m);break}}let c=e.next,f=0,u=n[o+2];if(e.next<0&&u>f&&n[h+u*3-3]==65535){o=n[h+u*3-1];continue e}for(;f<u;){let d=f+u>>1,m=h+d+(d<<1),x=n[m],S=n[m+1]||65536;if(c<x)u=d;else if(c>=S)f=d+1;else{o=n[m+2],e.advance();continue e}}break}}function $a(n,e,t){for(let i=e,s;(s=n[i])!=65535;i++)if(s==t)return i-e;return-1}function Rg(n,e,t,i){let s=$a(t,i,e);return s<0||$a(t,i,n)<s}const _e=typeof process<"u"&&Va&&/\bparse\b/.test(Va.LOG);let dr=null;function qa(n,e,t){let i=n.cursor(pe.IncludeAnonymous);for(i.moveTo(e);;)if(!(t<0?i.childBefore(e):i.childAfter(e)))for(;;){if((t<0?i.to<e:i.from>e)&&!i.type.isError)return t<0?Math.max(0,Math.min(i.to-1,e-25)):Math.min(n.length,Math.max(i.from+1,e+25));if(t<0?i.prevSibling():i.nextSibling())break;if(!i.parent())return t<0?0:n.length}}class Bg{constructor(e,t){this.fragments=e,this.nodeSet=t,this.i=0,this.fragment=null,this.safeFrom=-1,this.safeTo=-1,this.trees=[],this.start=[],this.index=[],this.nextFragment()}nextFragment(){let e=this.fragment=this.i==this.fragments.length?null:this.fragments[this.i++];if(e){for(this.safeFrom=e.openStart?qa(e.tree,e.from+e.offset,1)-e.offset:e.from,this.safeTo=e.openEnd?qa(e.tree,e.to+e.offset,-1)-e.offset:e.to;this.trees.length;)this.trees.pop(),this.start.pop(),this.index.pop();this.trees.push(e.tree),this.start.push(-e.offset),this.index.push(0),this.nextStart=this.safeFrom}else this.nextStart=1e9}nodeAt(e){if(e<this.nextStart)return null;for(;this.fragment&&this.safeTo<=e;)this.nextFragment();if(!this.fragment)return null;for(;;){let t=this.trees.length-1;if(t<0)return this.nextFragment(),null;let i=this.trees[t],s=this.index[t];if(s==i.children.length){this.trees.pop(),this.start.pop(),this.index.pop();continue}let r=i.children[s],o=this.start[t]+i.positions[s];if(o>e)return this.nextStart=o,null;if(r instanceof le){if(o==e){if(o<this.safeFrom)return null;let l=o+r.length;if(l<=this.safeTo){let a=r.prop(K.lookAhead);if(!a||l+a<this.fragment.to)return r}}this.index[t]++,o+r.length>=Math.max(this.safeFrom,e)&&(this.trees.push(r),this.start.push(o),this.index.push(0))}else this.index[t]++,this.nextStart=o+r.length}}}class Ig{constructor(e,t){this.stream=t,this.tokens=[],this.mainToken=null,this.actions=[],this.tokens=e.tokenizers.map(i=>new os)}getActions(e){let t=0,i=null,{parser:s}=e.p,{tokenizers:r}=s,o=s.stateSlot(e.state,3),l=e.curContext?e.curContext.hash:0,a=0;for(let h=0;h<r.length;h++){if((1<<h&o)==0)continue;let c=r[h],f=this.tokens[h];if(!(i&&!c.fallback)&&((c.contextual||f.start!=e.pos||f.mask!=o||f.context!=l)&&(this.updateCachedToken(f,c,e),f.mask=o,f.context=l),f.lookAhead>f.end+25&&(a=Math.max(f.lookAhead,a)),f.value!=0)){let u=t;if(f.extended>-1&&(t=this.addActions(e,f.extended,f.end,t)),t=this.addActions(e,f.value,f.end,t),!c.extend&&(i=f,t>u))break}}for(;this.actions.length>t;)this.actions.pop();return a&&e.setLookAhead(a),!i&&e.pos==this.stream.end&&(i=new os,i.value=e.p.parser.eofTerm,i.start=i.end=e.pos,t=this.addActions(e,i.value,i.end,t)),this.mainToken=i,this.actions}getMainToken(e){if(this.mainToken)return this.mainToken;let t=new os,{pos:i,p:s}=e;return t.start=i,t.end=Math.min(i+1,s.stream.end),t.value=i==s.stream.end?s.parser.eofTerm:0,t}updateCachedToken(e,t,i){let s=this.stream.clipPos(i.pos);if(t.token(this.stream.reset(s,e),i),e.value>-1){let{parser:r}=i.p;for(let o=0;o<r.specialized.length;o++)if(r.specialized[o]==e.value){let l=r.specializers[o](this.stream.read(e.start,e.end),i);if(l>=0&&i.p.parser.dialect.allows(l>>1)){(l&1)==0?e.value=l>>1:e.extended=l>>1;break}}}else e.value=0,e.end=this.stream.clipPos(s+1)}putAction(e,t,i,s){for(let r=0;r<s;r+=3)if(this.actions[r]==e)return s;return this.actions[s++]=e,this.actions[s++]=t,this.actions[s++]=i,s}addActions(e,t,i,s){let{state:r}=e,{parser:o}=e.p,{data:l}=o;for(let a=0;a<2;a++)for(let h=o.stateSlot(r,a?2:1);;h+=3){if(l[h]==65535)if(l[h+1]==1)h=St(l,h+2);else{s==0&&l[h+1]==2&&(s=this.putAction(St(l,h+2),t,i,s));break}l[h]==t&&(s=this.putAction(St(l,h+1),t,i,s))}return s}}class Lg{constructor(e,t,i,s){this.parser=e,this.input=t,this.ranges=s,this.recovering=0,this.nextStackID=9812,this.minStackPos=0,this.reused=[],this.stoppedAt=null,this.lastBigReductionStart=-1,this.lastBigReductionSize=0,this.bigReductionCount=0,this.stream=new Pg(t,s),this.tokens=new Ig(e,this.stream),this.topTerm=e.top[1];let{from:r}=s[0];this.stacks=[vs.start(this,e.top[0],r)],this.fragments=i.length&&this.stream.end-r>e.bufferLength*4?new Bg(i,e.nodeSet):null}get parsedPos(){return this.minStackPos}advance(){let e=this.stacks,t=this.minStackPos,i=this.stacks=[],s,r;if(this.bigReductionCount>300&&e.length==1){let[o]=e;for(;o.forceReduce()&&o.stack.length&&o.stack[o.stack.length-2]>=this.lastBigReductionStart;);this.bigReductionCount=this.lastBigReductionSize=0}for(let o=0;o<e.length;o++){let l=e[o];for(;;){if(this.tokens.mainToken=null,l.pos>t)i.push(l);else{if(this.advanceStack(l,i,e))continue;{s||(s=[],r=[]),s.push(l);let a=this.tokens.getMainToken(l);r.push(a.value,a.end)}}break}}if(!i.length){let o=s&&Wg(s);if(o)return _e&&console.log("Finish with "+this.stackID(o)),this.stackToTree(o);if(this.parser.strict)throw _e&&s&&console.log("Stuck with token "+(this.tokens.mainToken?this.parser.getName(this.tokens.mainToken.value):"none")),new SyntaxError("No parse at "+t);this.recovering||(this.recovering=5)}if(this.recovering&&s){let o=this.stoppedAt!=null&&s[0].pos>this.stoppedAt?s[0]:this.runRecovery(s,r,i);if(o)return _e&&console.log("Force-finish "+this.stackID(o)),this.stackToTree(o.forceAll())}if(this.recovering){let o=this.recovering==1?1:this.recovering*3;if(i.length>o)for(i.sort((l,a)=>a.score-l.score);i.length>o;)i.pop();i.some(l=>l.reducePos>t)&&this.recovering--}else if(i.length>1){e:for(let o=0;o<i.length-1;o++){let l=i[o];for(let a=o+1;a<i.length;a++){let h=i[a];if(l.sameState(h)||l.buffer.length>500&&h.buffer.length>500)if((l.score-h.score||l.buffer.length-h.buffer.length)>0)i.splice(a--,1);else{i.splice(o--,1);continue e}}}i.length>12&&i.splice(12,i.length-12)}this.minStackPos=i[0].pos;for(let o=1;o<i.length;o++)i[o].pos<this.minStackPos&&(this.minStackPos=i[o].pos);return null}stopAt(e){if(this.stoppedAt!=null&&this.stoppedAt<e)throw new RangeError("Can't move stoppedAt forward");this.stoppedAt=e}advanceStack(e,t,i){let s=e.pos,{parser:r}=this,o=_e?this.stackID(e)+" -> ":"";if(this.stoppedAt!=null&&s>this.stoppedAt)return e.forceReduce()?e:null;if(this.fragments){let h=e.curContext&&e.curContext.tracker.strict,c=h?e.curContext.hash:0;for(let f=this.fragments.nodeAt(s);f;){let u=this.parser.nodeSet.types[f.type.id]==f.type?r.getGoto(e.state,f.type.id):-1;if(u>-1&&f.length&&(!h||(f.prop(K.contextHash)||0)==c))return e.useNode(f,u),_e&&console.log(o+this.stackID(e)+` (via reuse of ${r.getName(f.type.id)})`),!0;if(!(f instanceof le)||f.children.length==0||f.positions[0]>0)break;let d=f.children[0];if(d instanceof le&&f.positions[0]==0)f=d;else break}}let l=r.stateSlot(e.state,4);if(l>0)return e.reduce(l),_e&&console.log(o+this.stackID(e)+` (via always-reduce ${r.getName(l&65535)})`),!0;if(e.stack.length>=8400)for(;e.stack.length>6e3&&e.forceReduce(););let a=this.tokens.getActions(e);for(let h=0;h<a.length;){let c=a[h++],f=a[h++],u=a[h++],d=h==a.length||!i,m=d?e:e.split(),x=this.tokens.mainToken;if(m.apply(c,f,x?x.start:m.pos,u),_e&&console.log(o+this.stackID(m)+` (via ${(c&65536)==0?"shift":`reduce of ${r.getName(c&65535)}`} for ${r.getName(f)} @ ${s}${m==e?"":", split"})`),d)return!0;m.pos>s?t.push(m):i.push(m)}return!1}advanceFully(e,t){let i=e.pos;for(;;){if(!this.advanceStack(e,null,null))return!1;if(e.pos>i)return Ha(e,t),!0}}runRecovery(e,t,i){let s=null,r=!1;for(let o=0;o<e.length;o++){let l=e[o],a=t[o<<1],h=t[(o<<1)+1],c=_e?this.stackID(l)+" -> ":"";if(l.deadEnd&&(r||(r=!0,l.restart(),_e&&console.log(c+this.stackID(l)+" (restarted)"),this.advanceFully(l,i))))continue;let f=l.split(),u=c;for(let d=0;f.forceReduce()&&d<10&&(_e&&console.log(u+this.stackID(f)+" (via force-reduce)"),!this.advanceFully(f,i));d++)_e&&(u=this.stackID(f)+" -> ");for(let d of l.recoverByInsert(a))_e&&console.log(c+this.stackID(d)+" (via recover-insert)"),this.advanceFully(d,i);this.stream.end>l.pos?(h==l.pos&&(h++,a=0),l.recoverByDelete(a,h),_e&&console.log(c+this.stackID(l)+` (via recover-delete ${this.parser.getName(a)})`),Ha(l,i)):(!s||s.score<l.score)&&(s=l)}return s}stackToTree(e){return e.close(),le.build({buffer:ws.create(e),nodeSet:this.parser.nodeSet,topID:this.topTerm,maxBufferLength:this.parser.bufferLength,reused:this.reused,start:this.ranges[0].from,length:e.pos-this.ranges[0].from,minRepeatType:this.parser.minRepeatTerm})}stackID(e){let t=(dr||(dr=new WeakMap)).get(e);return t||dr.set(e,t=String.fromCodePoint(this.nextStackID++)),t+e}}function Ha(n,e){for(let t=0;t<e.length;t++){let i=e[t];if(i.pos==n.pos&&i.sameState(n)){e[t].score<n.score&&(e[t]=n);return}}e.push(n)}class Fg{constructor(e,t,i){this.source=e,this.flags=t,this.disabled=i}allows(e){return!this.disabled||this.disabled[e]==0}}const pr=n=>n;class Vg{constructor(e){this.start=e.start,this.shift=e.shift||pr,this.reduce=e.reduce||pr,this.reuse=e.reuse||pr,this.hash=e.hash||(()=>0),this.strict=e.strict!==!1}}class hn extends Af{constructor(e){if(super(),this.wrappers=[],e.version!=14)throw new RangeError(`Parser version (${e.version}) doesn't match runtime version (14)`);let t=e.nodeNames.split(" ");this.minRepeatTerm=t.length;for(let l=0;l<e.repeatNodeCount;l++)t.push("");let i=Object.keys(e.topRules).map(l=>e.topRules[l][1]),s=[];for(let l=0;l<t.length;l++)s.push([]);function r(l,a,h){s[l].push([a,a.deserialize(String(h))])}if(e.nodeProps)for(let l of e.nodeProps){let a=l[0];typeof a=="string"&&(a=K[a]);for(let h=1;h<l.length;){let c=l[h++];if(c>=0)r(c,a,l[h++]);else{let f=l[h+-c];for(let u=-c;u>0;u--)r(l[h++],a,f);h++}}}this.nodeSet=new sl(t.map((l,a)=>We.define({name:a>=this.minRepeatTerm?void 0:l,id:a,props:s[a],top:i.indexOf(a)>-1,error:a==0,skipped:e.skippedNodes&&e.skippedNodes.indexOf(a)>-1}))),e.propSources&&(this.nodeSet=this.nodeSet.extend(...e.propSources)),this.strict=!1,this.bufferLength=Sf;let o=_n(e.tokenData);this.context=e.context,this.specializerSpecs=e.specialized||[],this.specialized=new Uint16Array(this.specializerSpecs.length);for(let l=0;l<this.specializerSpecs.length;l++)this.specialized[l]=this.specializerSpecs[l].term;this.specializers=this.specializerSpecs.map(za),this.states=_n(e.states,Uint32Array),this.data=_n(e.stateData),this.goto=_n(e.goto),this.maxTerm=e.maxTerm,this.tokenizers=e.tokenizers.map(l=>typeof l=="number"?new wi(o,l):l),this.topRules=e.topRules,this.dialects=e.dialects||{},this.dynamicPrecedences=e.dynamicPrecedences||null,this.tokenPrecTable=e.tokenPrec,this.termNames=e.termNames||null,this.maxNode=this.nodeSet.types.length-1,this.dialect=this.parseDialect(),this.top=this.topRules[Object.keys(this.topRules)[0]]}createParse(e,t,i){let s=new Lg(this,e,t,i);for(let r of this.wrappers)s=r(s,e,t,i);return s}getGoto(e,t,i=!1){let s=this.goto;if(t>=s[0])return-1;for(let r=s[t+1];;){let o=s[r++],l=o&1,a=s[r++];if(l&&i)return a;for(let h=r+(o>>1);r<h;r++)if(s[r]==e)return a;if(l)return-1}}hasAction(e,t){let i=this.data;for(let s=0;s<2;s++)for(let r=this.stateSlot(e,s?2:1),o;;r+=3){if((o=i[r])==65535)if(i[r+1]==1)o=i[r=St(i,r+2)];else{if(i[r+1]==2)return St(i,r+2);break}if(o==t||o==0)return St(i,r+1)}return 0}stateSlot(e,t){return this.states[e*6+t]}stateFlag(e,t){return(this.stateSlot(e,0)&t)>0}validAction(e,t){return!!this.allActions(e,i=>i==t?!0:null)}allActions(e,t){let i=this.stateSlot(e,4),s=i?t(i):void 0;for(let r=this.stateSlot(e,1);s==null;r+=3){if(this.data[r]==65535)if(this.data[r+1]==1)r=St(this.data,r+2);else break;s=t(St(this.data,r+1))}return s}nextStates(e){let t=[];for(let i=this.stateSlot(e,1);;i+=3){if(this.data[i]==65535)if(this.data[i+1]==1)i=St(this.data,i+2);else break;if((this.data[i+2]&1)==0){let s=this.data[i+1];t.some((r,o)=>o&1&&r==s)||t.push(this.data[i],s)}}return t}configure(e){let t=Object.assign(Object.create(hn.prototype),this);if(e.props&&(t.nodeSet=this.nodeSet.extend(...e.props)),e.top){let i=this.topRules[e.top];if(!i)throw new RangeError(`Invalid top rule name ${e.top}`);t.top=i}return e.tokenizers&&(t.tokenizers=this.tokenizers.map(i=>{let s=e.tokenizers.find(r=>r.from==i);return s?s.to:i})),e.specializers&&(t.specializers=this.specializers.slice(),t.specializerSpecs=this.specializerSpecs.map((i,s)=>{let r=e.specializers.find(l=>l.from==i.external);if(!r)return i;let o=Object.assign(Object.assign({},i),{external:r.to});return t.specializers[s]=za(o),o})),e.contextTracker&&(t.context=e.contextTracker),e.dialect&&(t.dialect=this.parseDialect(e.dialect)),e.strict!=null&&(t.strict=e.strict),e.wrap&&(t.wrappers=t.wrappers.concat(e.wrap)),e.bufferLength!=null&&(t.bufferLength=e.bufferLength),t}hasWrappers(){return this.wrappers.length>0}getName(e){return this.termNames?this.termNames[e]:String(e<=this.maxNode&&this.nodeSet.types[e].name||e)}get eofTerm(){return this.maxNode+1}get topNode(){return this.nodeSet.types[this.top[1]]}dynamicPrecedence(e){let t=this.dynamicPrecedences;return t==null?0:t[e]||0}parseDialect(e){let t=Object.keys(this.dialects),i=t.map(()=>!1);if(e)for(let r of e.split(" ")){let o=t.indexOf(r);o>=0&&(i[o]=!0)}let s=null;for(let r=0;r<t.length;r++)if(!i[r])for(let o=this.dialects[t[r]],l;(l=this.data[o++])!=65535;)(s||(s=new Uint8Array(this.maxTerm+1)))[l]=1;return new Fg(e,i,s)}static deserialize(e){return new hn(e)}}function St(n,e){return n[e]|n[e+1]<<16}function Wg(n){let e=null;for(let t of n){let i=t.p.stoppedAt;(t.pos==t.p.stream.end||i!=null&&t.pos>i)&&t.p.parser.stateFlag(t.state,2)&&(!e||e.score<t.score)&&(e=t)}return e}function za(n){if(n.external){let e=n.extend?1:0;return(t,i)=>n.external(t,i)<<1|e}return n.get}let _g=0;class Ue{constructor(e,t,i,s){this.name=e,this.set=t,this.base=i,this.modified=s,this.id=_g++}toString(){let{name:e}=this;for(let t of this.modified)t.name&&(e=`${t.name}(${e})`);return e}static define(e,t){let i=typeof e=="string"?e:"?";if(e instanceof Ue&&(t=e),t!=null&&t.base)throw new Error("Can not derive from a modified tag");let s=new Ue(i,[],null,[]);if(s.set.push(s),t)for(let r of t.set)s.set.push(r);return s}static defineModifier(e){let t=new Ss(e);return i=>i.modified.indexOf(t)>-1?i:Ss.get(i.base||i,i.modified.concat(t).sort((s,r)=>s.id-r.id))}}let $g=0;class Ss{constructor(e){this.name=e,this.instances=[],this.id=$g++}static get(e,t){if(!t.length)return e;let i=t[0].instances.find(l=>l.base==e&&qg(t,l.modified));if(i)return i;let s=[],r=new Ue(e.name,s,e,t);for(let l of t)l.instances.push(r);let o=Hg(t);for(let l of e.set)if(!l.modified.length)for(let a of o)s.push(Ss.get(l,a));return r}}function qg(n,e){return n.length==e.length&&n.every((t,i)=>t==e[i])}function Hg(n){let e=[[]];for(let t=0;t<n.length;t++)for(let i=0,s=e.length;i<s;i++)e.push(e[i].concat(n[t]));return e.sort((t,i)=>i.length-t.length)}function al(n){let e=Object.create(null);for(let t in n){let i=n[t];Array.isArray(i)||(i=[i]);for(let s of t.split(" "))if(s){let r=[],o=2,l=s;for(let f=0;;){if(l=="..."&&f>0&&f+3==s.length){o=1;break}let u=/^"(?:[^"\\]|\\.)*?"|[^\/!]+/.exec(l);if(!u)throw new RangeError("Invalid path: "+s);if(r.push(u[0]=="*"?"":u[0][0]=='"'?JSON.parse(u[0]):u[0]),f+=u[0].length,f==s.length)break;let d=s[f++];if(f==s.length&&d=="!"){o=0;break}if(d!="/")throw new RangeError("Invalid path: "+s);l=s.slice(f)}let a=r.length-1,h=r[a];if(!h)throw new RangeError("Invalid path: "+s);let c=new Os(i,o,a>0?r.slice(0,a):null);e[h]=c.sort(e[h])}}return Mf.add(e)}const Mf=new K;class Os{constructor(e,t,i,s){this.tags=e,this.mode=t,this.context=i,this.next=s}get opaque(){return this.mode==0}get inherit(){return this.mode==1}sort(e){return!e||e.depth<this.depth?(this.next=e,this):(e.next=this.sort(e.next),e)}get depth(){return this.context?this.context.length:0}}Os.empty=new Os([],2,null);function Ef(n,e){let t=Object.create(null);for(let r of n)if(!Array.isArray(r.tag))t[r.tag.id]=r.class;else for(let o of r.tag)t[o.id]=r.class;let{scope:i,all:s=null}=e||{};return{style:r=>{let o=s;for(let l of r)for(let a of l.set){let h=t[a.id];if(h){o=o?o+" "+h:h;break}}return o},scope:i}}function zg(n,e){let t=null;for(let i of n){let s=i.style(e);s&&(t=t?t+" "+s:s)}return t}function jg(n,e,t,i=0,s=n.length){let r=new Kg(i,Array.isArray(e)?e:[e],t);r.highlightRange(n.cursor(),i,s,"",r.highlighters),r.flush(s)}class Kg{constructor(e,t,i){this.at=e,this.highlighters=t,this.span=i,this.class=""}startSpan(e,t){t!=this.class&&(this.flush(e),e>this.at&&(this.at=e),this.class=t)}flush(e){e>this.at&&this.class&&this.span(this.at,e,this.class)}highlightRange(e,t,i,s,r){let{type:o,from:l,to:a}=e;if(l>=i||a<=t)return;o.isTop&&(r=this.highlighters.filter(d=>!d.scope||d.scope(o)));let h=s,c=Ug(e)||Os.empty,f=zg(r,c.tags);if(f&&(h&&(h+=" "),h+=f,c.mode==1&&(s+=(s?" ":"")+f)),this.startSpan(Math.max(t,l),h),c.opaque)return;let u=e.tree&&e.tree.prop(K.mounted);if(u&&u.overlay){let d=e.node.enter(u.overlay[0].from+l,1),m=this.highlighters.filter(S=>!S.scope||S.scope(u.tree.type)),x=e.firstChild();for(let S=0,y=l;;S++){let O=S<u.overlay.length?u.overlay[S]:null,v=O?O.from+l:a,g=Math.max(t,y),k=Math.min(i,v);if(g<k&&x)for(;e.from<k&&(this.highlightRange(e,g,k,s,r),this.startSpan(Math.min(k,e.to),h),!(e.to>=v||!e.nextSibling())););if(!O||v>i)break;y=O.to+l,y>t&&(this.highlightRange(d.cursor(),Math.max(t,O.from+l),Math.min(i,y),"",m),this.startSpan(Math.min(i,y),h))}x&&e.parent()}else if(e.firstChild()){u&&(s="");do if(!(e.to<=t)){if(e.from>=i)break;this.highlightRange(e,t,i,s,r),this.startSpan(Math.min(i,e.to),h)}while(e.nextSibling());e.parent()}}}function Ug(n){let e=n.type.prop(Mf);for(;e&&e.context&&!n.matchContext(e.context);)e=e.next;return e||null}const B=Ue.define,$n=B(),Rt=B(),ja=B(Rt),Ka=B(Rt),Bt=B(),qn=B(Bt),mr=B(Bt),ht=B(),Gt=B(ht),lt=B(),at=B(),ko=B(),_i=B(ko),Hn=B(),A={comment:$n,lineComment:B($n),blockComment:B($n),docComment:B($n),name:Rt,variableName:B(Rt),typeName:ja,tagName:B(ja),propertyName:Ka,attributeName:B(Ka),className:B(Rt),labelName:B(Rt),namespace:B(Rt),macroName:B(Rt),literal:Bt,string:qn,docString:B(qn),character:B(qn),attributeValue:B(qn),number:mr,integer:B(mr),float:B(mr),bool:B(Bt),regexp:B(Bt),escape:B(Bt),color:B(Bt),url:B(Bt),keyword:lt,self:B(lt),null:B(lt),atom:B(lt),unit:B(lt),modifier:B(lt),operatorKeyword:B(lt),controlKeyword:B(lt),definitionKeyword:B(lt),moduleKeyword:B(lt),operator:at,derefOperator:B(at),arithmeticOperator:B(at),logicOperator:B(at),bitwiseOperator:B(at),compareOperator:B(at),updateOperator:B(at),definitionOperator:B(at),typeOperator:B(at),controlOperator:B(at),punctuation:ko,separator:B(ko),bracket:_i,angleBracket:B(_i),squareBracket:B(_i),paren:B(_i),brace:B(_i),content:ht,heading:Gt,heading1:B(Gt),heading2:B(Gt),heading3:B(Gt),heading4:B(Gt),heading5:B(Gt),heading6:B(Gt),contentSeparator:B(ht),list:B(ht),quote:B(ht),emphasis:B(ht),strong:B(ht),link:B(ht),monospace:B(ht),strikethrough:B(ht),inserted:B(),deleted:B(),changed:B(),invalid:B(),meta:Hn,documentMeta:B(Hn),annotation:B(Hn),processingInstruction:B(Hn),definition:Ue.defineModifier("definition"),constant:Ue.defineModifier("constant"),function:Ue.defineModifier("function"),standard:Ue.defineModifier("standard"),local:Ue.defineModifier("local"),special:Ue.defineModifier("special")};for(let n in A){let e=A[n];e instanceof Ue&&(e.name=n)}Ef([{tag:A.link,class:"tok-link"},{tag:A.heading,class:"tok-heading"},{tag:A.emphasis,class:"tok-emphasis"},{tag:A.strong,class:"tok-strong"},{tag:A.keyword,class:"tok-keyword"},{tag:A.atom,class:"tok-atom"},{tag:A.bool,class:"tok-bool"},{tag:A.url,class:"tok-url"},{tag:A.labelName,class:"tok-labelName"},{tag:A.inserted,class:"tok-inserted"},{tag:A.deleted,class:"tok-deleted"},{tag:A.literal,class:"tok-literal"},{tag:A.string,class:"tok-string"},{tag:A.number,class:"tok-number"},{tag:[A.regexp,A.escape,A.special(A.string)],class:"tok-string2"},{tag:A.variableName,class:"tok-variableName"},{tag:A.local(A.variableName),class:"tok-variableName tok-local"},{tag:A.definition(A.variableName),class:"tok-variableName tok-definition"},{tag:A.special(A.variableName),class:"tok-variableName2"},{tag:A.definition(A.propertyName),class:"tok-propertyName tok-definition"},{tag:A.typeName,class:"tok-typeName"},{tag:A.namespace,class:"tok-namespace"},{tag:A.className,class:"tok-className"},{tag:A.macroName,class:"tok-macroName"},{tag:A.propertyName,class:"tok-propertyName"},{tag:A.operator,class:"tok-operator"},{tag:A.comment,class:"tok-comment"},{tag:A.meta,class:"tok-meta"},{tag:A.invalid,class:"tok-invalid"},{tag:A.punctuation,class:"tok-punctuation"}]);const Co=1,Gg=2,Xg=3,Qg=4,Yg=5,Jg=36,Zg=37,e0=38,t0=11,i0=13;function n0(n){return n==45||n==46||n==58||n>=65&&n<=90||n==95||n>=97&&n<=122||n>=161}function s0(n){return n==9||n==10||n==13||n==32}let Ua=null,Ga=null,Xa=0;function Ao(n,e){let t=n.pos+e;if(Ga==n&&Xa==t)return Ua;for(;s0(n.peek(e));)e++;let i="";for(;;){let s=n.peek(e);if(!n0(s))break;i+=String.fromCharCode(s),e++}return Ga=n,Xa=t,Ua=i||null}function Qa(n,e){this.name=n,this.parent=e}const r0=new Vg({start:null,shift(n,e,t,i){return e==Co?new Qa(Ao(i,1)||"",n):n},reduce(n,e){return e==t0&&n?n.parent:n},reuse(n,e,t,i){let s=e.type.id;return s==Co||s==i0?new Qa(Ao(i,1)||"",n):n},strict:!1}),o0=new Tf((n,e)=>{if(n.next==60){if(n.advance(),n.next==47){n.advance();let t=Ao(n,0);if(!t)return n.acceptToken(Yg);if(e.context&&t==e.context.name)return n.acceptToken(Gg);for(let i=e.context;i;i=i.parent)if(i.name==t)return n.acceptToken(Xg,-2);n.acceptToken(Qg)}else if(n.next!=33&&n.next!=63)return n.acceptToken(Co)}},{contextual:!0});function hl(n,e){return new Tf(t=>{let i=0,s=e.charCodeAt(0);e:for(;!(t.next<0);t.advance(),i++)if(t.next==s){for(let r=1;r<e.length;r++)if(t.peek(r)!=e.charCodeAt(r))continue e;break}i&&t.acceptToken(n)})}const l0=hl(Jg,"-->"),a0=hl(Zg,"?>"),h0=hl(e0,"]]>"),c0=al({Text:A.content,"StartTag StartCloseTag EndTag SelfCloseEndTag":A.angleBracket,TagName:A.tagName,"MismatchedCloseTag/TagName":[A.tagName,A.invalid],AttributeName:A.attributeName,AttributeValue:A.attributeValue,Is:A.definitionOperator,"EntityReference CharacterReference":A.character,Comment:A.blockComment,ProcessingInst:A.processingInstruction,DoctypeDecl:A.documentMeta,Cdata:A.special(A.string)}),f0=hn.deserialize({version:14,states:",lOQOaOOOrOxO'#CfOzOpO'#CiO!tOaO'#CgOOOP'#Cg'#CgO!{OrO'#CrO#TOtO'#CsO#]OpO'#CtOOOP'#DT'#DTOOOP'#Cv'#CvQQOaOOOOOW'#Cw'#CwO#eOxO,59QOOOP,59Q,59QOOOO'#Cx'#CxO#mOpO,59TO#uO!bO,59TOOOP'#C|'#C|O$TOaO,59RO$[OpO'#CoOOOP,59R,59ROOOQ'#C}'#C}O$dOrO,59^OOOP,59^,59^OOOS'#DO'#DOO$lOtO,59_OOOP,59_,59_O$tOpO,59`O$|OpO,59`OOOP-E6t-E6tOOOW-E6u-E6uOOOP1G.l1G.lOOOO-E6v-E6vO%UO!bO1G.oO%UO!bO1G.oO%dOpO'#CkO%lO!bO'#CyO%zO!bO1G.oOOOP1G.o1G.oOOOP1G.w1G.wOOOP-E6z-E6zOOOP1G.m1G.mO&VOpO,59ZO&_OpO,59ZOOOQ-E6{-E6{OOOP1G.x1G.xOOOS-E6|-E6|OOOP1G.y1G.yO&gOpO1G.zO&gOpO1G.zOOOP1G.z1G.zO&oO!bO7+$ZO&}O!bO7+$ZOOOP7+$Z7+$ZOOOP7+$c7+$cO'YOpO,59VO'bOpO,59VO'mO!bO,59eOOOO-E6w-E6wO'{OpO1G.uO'{OpO1G.uOOOP1G.u1G.uO(TOpO7+$fOOOP7+$f7+$fO(]O!bO<<GuOOOP<<Gu<<GuOOOP<<G}<<G}O'bOpO1G.qO'bOpO1G.qO(hO#tO'#CnO(vO&jO'#CnOOOO1G.q1G.qO)UOpO7+$aOOOP7+$a7+$aOOOP<<HQ<<HQOOOPAN=aAN=aOOOPAN=iAN=iO'bOpO7+$]OOOO7+$]7+$]OOOO'#Cz'#CzO)^O#tO,59YOOOO,59Y,59YOOOO'#C{'#C{O)lO&jO,59YOOOP<<G{<<G{OOOO<<Gw<<GwOOOO-E6x-E6xOOOO1G.t1G.tOOOO-E6y-E6y",stateData:")z~OPQOSVOTWOVWOWWOXWOiXOyPO!QTO!SUO~OvZOx]O~O^`Oz^O~OPQOQcOSVOTWOVWOWWOXWOyPO!QTO!SUO~ORdO~P!SOteO!PgO~OuhO!RjO~O^lOz^O~OvZOxoO~O^qOz^O~O[vO`sOdwOz^O~ORyO~P!SO^{Oz^O~OteO!P}O~OuhO!R!PO~O^!QOz^O~O[!SOz^O~O[!VO`sOd!WOz^O~Oa!YOz^O~Oz^O[mX`mXdmX~O[!VO`sOd!WO~O^!]Oz^O~O[!_Oz^O~O[!aOz^O~O[!cO`sOd!dOz^O~O[!cO`sOd!dO~Oa!eOz^O~Oz^O{!gO}!hO~Oz^O[ma`madma~O[!kOz^O~O[!lOz^O~O[!mO`sOd!nO~OW!qOX!qO{!sO|!qO~OW!tOX!tO}!sO!O!tO~O[!vOz^O~OW!qOX!qO{!yO|!qO~OW!tOX!tO}!yO!O!tO~O",goto:"%cxPPPPPPPPPPyyP!PP!VPP!`!jP!pyyyP!v!|#S$[$k$q$w$}%TPPPP%ZXWORYbXRORYb_t`qru!T!U!bQ!i!YS!p!e!fR!w!oQdRRybXSORYbQYORmYQ[PRn[Q_QQkVjp_krz!R!T!X!Z!^!`!f!j!oQr`QzcQ!RlQ!TqQ!XsQ!ZtQ!^{Q!`!QQ!f!YQ!j!]R!o!eQu`S!UqrU![u!U!bR!b!TQ!r!gR!x!rQ!u!hR!z!uQbRRxbQfTR|fQiUR!OiSXOYTaRb",nodeNames:"⚠ StartTag StartCloseTag MissingCloseTag StartCloseTag StartCloseTag Document Text EntityReference CharacterReference Cdata Element EndTag OpenTag TagName Attribute AttributeName Is AttributeValue CloseTag SelfCloseEndTag SelfClosingTag Comment ProcessingInst MismatchedCloseTag DoctypeDecl",maxTerm:50,context:r0,nodeProps:[["closedBy",1,"SelfCloseEndTag EndTag",13,"CloseTag MissingCloseTag"],["openedBy",12,"StartTag StartCloseTag",19,"OpenTag",20,"StartTag"],["isolate",-6,13,18,19,21,22,24,""]],propSources:[c0],skippedNodes:[0],repeatNodeCount:9,tokenData:"!)v~R!YOX$qXY)iYZ)iZ]$q]^)i^p$qpq)iqr$qrs*vsv$qvw+fwx/ix}$q}!O0[!O!P$q!P!Q2z!Q![$q![!]4n!]!^$q!^!_8U!_!`!#t!`!a!$l!a!b!%d!b!c$q!c!}4n!}#P$q#P#Q!'W#Q#R$q#R#S4n#S#T$q#T#o4n#o%W$q%W%o4n%o%p$q%p&a4n&a&b$q&b1p4n1p4U$q4U4d4n4d4e$q4e$IS4n$IS$I`$q$I`$Ib4n$Ib$Kh$q$Kh%#t4n%#t&/x$q&/x&Et4n&Et&FV$q&FV;'S4n;'S;:j8O;:j;=`)c<%l?&r$q?&r?Ah4n?Ah?BY$q?BY?Mn4n?MnO$qi$zXVP|W!O`Or$qrs%gsv$qwx'^x!^$q!^!_(o!_;'S$q;'S;=`)c<%lO$qa%nVVP!O`Ov%gwx&Tx!^%g!^!_&o!_;'S%g;'S;=`'W<%lO%gP&YTVPOv&Tw!^&T!_;'S&T;'S;=`&i<%lO&TP&lP;=`<%l&T`&tS!O`Ov&ox;'S&o;'S;=`'Q<%lO&o`'TP;=`<%l&oa'ZP;=`<%l%gX'eWVP|WOr'^rs&Tsv'^w!^'^!^!_'}!_;'S'^;'S;=`(i<%lO'^W(ST|WOr'}sv'}w;'S'};'S;=`(c<%lO'}W(fP;=`<%l'}X(lP;=`<%l'^h(vV|W!O`Or(ors&osv(owx'}x;'S(o;'S;=`)]<%lO(oh)`P;=`<%l(oi)fP;=`<%l$qo)t`VP|W!O`zUOX$qXY)iYZ)iZ]$q]^)i^p$qpq)iqr$qrs%gsv$qwx'^x!^$q!^!_(o!_;'S$q;'S;=`)c<%lO$qk+PV{YVP!O`Ov%gwx&Tx!^%g!^!_&o!_;'S%g;'S;=`'W<%lO%g~+iast,n![!]-r!c!}-r#R#S-r#T#o-r%W%o-r%p&a-r&b1p-r4U4d-r4e$IS-r$I`$Ib-r$Kh%#t-r&/x&Et-r&FV;'S-r;'S;:j/c?&r?Ah-r?BY?Mn-r~,qQ!Q![,w#l#m-V~,zQ!Q![,w!]!^-Q~-VOX~~-YR!Q![-c!c!i-c#T#Z-c~-fS!Q![-c!]!^-Q!c!i-c#T#Z-c~-ug}!O-r!O!P-r!Q![-r![!]-r!]!^/^!c!}-r#R#S-r#T#o-r$}%O-r%W%o-r%p&a-r&b1p-r1p4U-r4U4d-r4e$IS-r$I`$Ib-r$Je$Jg-r$Kh%#t-r&/x&Et-r&FV;'S-r;'S;:j/c?&r?Ah-r?BY?Mn-r~/cOW~~/fP;=`<%l-rk/rW}bVP|WOr'^rs&Tsv'^w!^'^!^!_'}!_;'S'^;'S;=`(i<%lO'^k0eZVP|W!O`Or$qrs%gsv$qwx'^x}$q}!O1W!O!^$q!^!_(o!_;'S$q;'S;=`)c<%lO$qk1aZVP|W!O`Or$qrs%gsv$qwx'^x!^$q!^!_(o!_!`$q!`!a2S!a;'S$q;'S;=`)c<%lO$qk2_X!PQVP|W!O`Or$qrs%gsv$qwx'^x!^$q!^!_(o!_;'S$q;'S;=`)c<%lO$qm3TZVP|W!O`Or$qrs%gsv$qwx'^x!^$q!^!_(o!_!`$q!`!a3v!a;'S$q;'S;=`)c<%lO$qm4RXdSVP|W!O`Or$qrs%gsv$qwx'^x!^$q!^!_(o!_;'S$q;'S;=`)c<%lO$qo4{!P`S^QVP|W!O`Or$qrs%gsv$qwx'^x}$q}!O4n!O!P4n!P!Q$q!Q![4n![!]4n!]!^$q!^!_(o!_!c$q!c!}4n!}#R$q#R#S4n#S#T$q#T#o4n#o$}$q$}%O4n%O%W$q%W%o4n%o%p$q%p&a4n&a&b$q&b1p4n1p4U4n4U4d4n4d4e$q4e$IS4n$IS$I`$q$I`$Ib4n$Ib$Je$q$Je$Jg4n$Jg$Kh$q$Kh%#t4n%#t&/x$q&/x&Et4n&Et&FV$q&FV;'S4n;'S;:j8O;:j;=`)c<%l?&r$q?&r?Ah4n?Ah?BY$q?BY?Mn4n?MnO$qo8RP;=`<%l4ni8]Y|W!O`Oq(oqr8{rs&osv(owx'}x!a(o!a!b!#U!b;'S(o;'S;=`)]<%lO(oi9S_|W!O`Or(ors&osv(owx'}x}(o}!O:R!O!f(o!f!g;e!g!}(o!}#ODh#O#W(o#W#XLp#X;'S(o;'S;=`)]<%lO(oi:YX|W!O`Or(ors&osv(owx'}x}(o}!O:u!O;'S(o;'S;=`)]<%lO(oi;OV!QP|W!O`Or(ors&osv(owx'}x;'S(o;'S;=`)]<%lO(oi;lX|W!O`Or(ors&osv(owx'}x!q(o!q!r<X!r;'S(o;'S;=`)]<%lO(oi<`X|W!O`Or(ors&osv(owx'}x!e(o!e!f<{!f;'S(o;'S;=`)]<%lO(oi=SX|W!O`Or(ors&osv(owx'}x!v(o!v!w=o!w;'S(o;'S;=`)]<%lO(oi=vX|W!O`Or(ors&osv(owx'}x!{(o!{!|>c!|;'S(o;'S;=`)]<%lO(oi>jX|W!O`Or(ors&osv(owx'}x!r(o!r!s?V!s;'S(o;'S;=`)]<%lO(oi?^X|W!O`Or(ors&osv(owx'}x!g(o!g!h?y!h;'S(o;'S;=`)]<%lO(oi@QY|W!O`Or?yrs@psv?yvwA[wxBdx!`?y!`!aCr!a;'S?y;'S;=`Db<%lO?ya@uV!O`Ov@pvxA[x!`@p!`!aAy!a;'S@p;'S;=`B^<%lO@pPA_TO!`A[!`!aAn!a;'SA[;'S;=`As<%lOA[PAsOiPPAvP;=`<%lA[aBQSiP!O`Ov&ox;'S&o;'S;=`'Q<%lO&oaBaP;=`<%l@pXBiX|WOrBdrsA[svBdvwA[w!`Bd!`!aCU!a;'SBd;'S;=`Cl<%lOBdXC]TiP|WOr'}sv'}w;'S'};'S;=`(c<%lO'}XCoP;=`<%lBdiC{ViP|W!O`Or(ors&osv(owx'}x;'S(o;'S;=`)]<%lO(oiDeP;=`<%l?yiDoZ|W!O`Or(ors&osv(owx'}x!e(o!e!fEb!f#V(o#V#WIr#W;'S(o;'S;=`)]<%lO(oiEiX|W!O`Or(ors&osv(owx'}x!f(o!f!gFU!g;'S(o;'S;=`)]<%lO(oiF]X|W!O`Or(ors&osv(owx'}x!c(o!c!dFx!d;'S(o;'S;=`)]<%lO(oiGPX|W!O`Or(ors&osv(owx'}x!v(o!v!wGl!w;'S(o;'S;=`)]<%lO(oiGsX|W!O`Or(ors&osv(owx'}x!c(o!c!dH`!d;'S(o;'S;=`)]<%lO(oiHgX|W!O`Or(ors&osv(owx'}x!}(o!}#OIS#O;'S(o;'S;=`)]<%lO(oiI]V|W!O`yPOr(ors&osv(owx'}x;'S(o;'S;=`)]<%lO(oiIyX|W!O`Or(ors&osv(owx'}x#W(o#W#XJf#X;'S(o;'S;=`)]<%lO(oiJmX|W!O`Or(ors&osv(owx'}x#T(o#T#UKY#U;'S(o;'S;=`)]<%lO(oiKaX|W!O`Or(ors&osv(owx'}x#h(o#h#iK|#i;'S(o;'S;=`)]<%lO(oiLTX|W!O`Or(ors&osv(owx'}x#T(o#T#UH`#U;'S(o;'S;=`)]<%lO(oiLwX|W!O`Or(ors&osv(owx'}x#c(o#c#dMd#d;'S(o;'S;=`)]<%lO(oiMkX|W!O`Or(ors&osv(owx'}x#V(o#V#WNW#W;'S(o;'S;=`)]<%lO(oiN_X|W!O`Or(ors&osv(owx'}x#h(o#h#iNz#i;'S(o;'S;=`)]<%lO(oi! RX|W!O`Or(ors&osv(owx'}x#m(o#m#n! n#n;'S(o;'S;=`)]<%lO(oi! uX|W!O`Or(ors&osv(owx'}x#d(o#d#e!!b#e;'S(o;'S;=`)]<%lO(oi!!iX|W!O`Or(ors&osv(owx'}x#X(o#X#Y?y#Y;'S(o;'S;=`)]<%lO(oi!#_V!SP|W!O`Or(ors&osv(owx'}x;'S(o;'S;=`)]<%lO(ok!$PXaQVP|W!O`Or$qrs%gsv$qwx'^x!^$q!^!_(o!_;'S$q;'S;=`)c<%lO$qo!$wX[UVP|W!O`Or$qrs%gsv$qwx'^x!^$q!^!_(o!_;'S$q;'S;=`)c<%lO$qk!%mZVP|W!O`Or$qrs%gsv$qwx'^x!^$q!^!_(o!_!`$q!`!a!&`!a;'S$q;'S;=`)c<%lO$qk!&kX!RQVP|W!O`Or$qrs%gsv$qwx'^x!^$q!^!_(o!_;'S$q;'S;=`)c<%lO$qk!'aZVP|W!O`Or$qrs%gsv$qwx'^x!^$q!^!_(o!_#P$q#P#Q!(S#Q;'S$q;'S;=`)c<%lO$qk!(]ZVP|W!O`Or$qrs%gsv$qwx'^x!^$q!^!_(o!_!`$q!`!a!)O!a;'S$q;'S;=`)c<%lO$qk!)ZXxQVP|W!O`Or$qrs%gsv$qwx'^x!^$q!^!_(o!_;'S$q;'S;=`)c<%lO$q",tokenizers:[o0,l0,a0,h0,0,1,2,3,4],topRules:{Document:[0,6]},tokenPrec:0});var gr;const gi=new K;function u0(n){return F.define({combine:n?e=>e.concat(n):void 0})}const d0=new K;class et{constructor(e,t,i=[],s=""){this.data=e,this.name=s,j.prototype.hasOwnProperty("tree")||Object.defineProperty(j.prototype,"tree",{get(){return ge(this)}}),this.parser=t,this.extension=[jt.of(this),j.languageData.of((r,o,l)=>{let a=Ya(r,o,l),h=a.type.prop(gi);if(!h)return[];let c=r.facet(h),f=a.type.prop(d0);if(f){let u=a.resolve(o-a.from,l);for(let d of f)if(d.test(u,r)){let m=r.facet(d.facet);return d.type=="replace"?m:m.concat(c)}}return c})].concat(i)}isActiveAt(e,t,i=-1){return Ya(e,t,i).type.prop(gi)==this.data}findRegions(e){let t=e.facet(jt);if((t==null?void 0:t.data)==this.data)return[{from:0,to:e.doc.length}];if(!t||!t.allowsNesting)return[];let i=[],s=(r,o)=>{if(r.prop(gi)==this.data){i.push({from:o,to:o+r.length});return}let l=r.prop(K.mounted);if(l){if(l.tree.prop(gi)==this.data){if(l.overlay)for(let a of l.overlay)i.push({from:a.from+o,to:a.to+o});else i.push({from:o,to:o+r.length});return}else if(l.overlay){let a=i.length;if(s(l.tree,l.overlay[0].from+o),i.length>a)return}}for(let a=0;a<r.children.length;a++){let h=r.children[a];h instanceof le&&s(h,r.positions[a]+o)}};return s(ge(e),0),i}get allowsNesting(){return!0}}et.setState=_.define();function Ya(n,e,t){let i=n.facet(jt),s=ge(n).topNode;if(!i||i.allowsNesting)for(let r=s;r;r=r.enter(e,t,pe.ExcludeBuffers))r.type.isTop&&(s=r);return s}class cn extends et{constructor(e,t,i){super(e,t,[],i),this.parser=t}static define(e){let t=u0(e.languageData);return new cn(t,e.parser.configure({props:[gi.add(i=>i.isTop?t:void 0)]}),e.name)}configure(e,t){return new cn(this.data,this.parser.configure(e),t||this.name)}get allowsNesting(){return this.parser.hasWrappers()}}function ge(n){let e=n.field(et.state,!1);return e?e.tree:le.empty}class p0{constructor(e){this.doc=e,this.cursorPos=0,this.string="",this.cursor=e.iter()}get length(){return this.doc.length}syncTo(e){return this.string=this.cursor.next(e-this.cursorPos).value,this.cursorPos=e+this.string.length,this.cursorPos-this.string.length}chunk(e){return this.syncTo(e),this.string}get lineChunks(){return!0}read(e,t){let i=this.cursorPos-this.string.length;return e<i||t>=this.cursorPos?this.doc.sliceString(e,t):this.string.slice(e-i,t-i)}}let $i=null;class ks{constructor(e,t,i=[],s,r,o,l,a){this.parser=e,this.state=t,this.fragments=i,this.tree=s,this.treeLen=r,this.viewport=o,this.skipped=l,this.scheduleOn=a,this.parse=null,this.tempSkipped=[]}static create(e,t,i){return new ks(e,t,[],le.empty,0,i,[],null)}startParse(){return this.parser.startParse(new p0(this.state.doc),this.fragments)}work(e,t){return t!=null&&t>=this.state.doc.length&&(t=void 0),this.tree!=le.empty&&this.isDone(t??this.state.doc.length)?(this.takeTree(),!0):this.withContext(()=>{var i;if(typeof e=="number"){let s=Date.now()+e;e=()=>Date.now()>s}for(this.parse||(this.parse=this.startParse()),t!=null&&(this.parse.stoppedAt==null||this.parse.stoppedAt>t)&&t<this.state.doc.length&&this.parse.stopAt(t);;){let s=this.parse.advance();if(s)if(this.fragments=this.withoutTempSkipped(ei.addTree(s,this.fragments,this.parse.stoppedAt!=null)),this.treeLen=(i=this.parse.stoppedAt)!==null&&i!==void 0?i:this.state.doc.length,this.tree=s,this.parse=null,this.treeLen<(t??this.state.doc.length))this.parse=this.startParse();else return!0;if(e())return!1}})}takeTree(){let e,t;this.parse&&(e=this.parse.parsedPos)>=this.treeLen&&((this.parse.stoppedAt==null||this.parse.stoppedAt>e)&&this.parse.stopAt(e),this.withContext(()=>{for(;!(t=this.parse.advance()););}),this.treeLen=e,this.tree=t,this.fragments=this.withoutTempSkipped(ei.addTree(this.tree,this.fragments,!0)),this.parse=null)}withContext(e){let t=$i;$i=this;try{return e()}finally{$i=t}}withoutTempSkipped(e){for(let t;t=this.tempSkipped.pop();)e=Ja(e,t.from,t.to);return e}changes(e,t){let{fragments:i,tree:s,treeLen:r,viewport:o,skipped:l}=this;if(this.takeTree(),!e.empty){let a=[];if(e.iterChangedRanges((h,c,f,u)=>a.push({fromA:h,toA:c,fromB:f,toB:u})),i=ei.applyChanges(i,a),s=le.empty,r=0,o={from:e.mapPos(o.from,-1),to:e.mapPos(o.to,1)},this.skipped.length){l=[];for(let h of this.skipped){let c=e.mapPos(h.from,1),f=e.mapPos(h.to,-1);c<f&&l.push({from:c,to:f})}}}return new ks(this.parser,t,i,s,r,o,l,this.scheduleOn)}updateViewport(e){if(this.viewport.from==e.from&&this.viewport.to==e.to)return!1;this.viewport=e;let t=this.skipped.length;for(let i=0;i<this.skipped.length;i++){let{from:s,to:r}=this.skipped[i];s<e.to&&r>e.from&&(this.fragments=Ja(this.fragments,s,r),this.skipped.splice(i--,1))}return this.skipped.length>=t?!1:(this.reset(),!0)}reset(){this.parse&&(this.takeTree(),this.parse=null)}skipUntilInView(e,t){this.skipped.push({from:e,to:t})}static getSkippingParser(e){return new class extends Af{createParse(t,i,s){let r=s[0].from,o=s[s.length-1].to;return{parsedPos:r,advance(){let a=$i;if(a){for(let h of s)a.tempSkipped.push(h);e&&(a.scheduleOn=a.scheduleOn?Promise.all([a.scheduleOn,e]):e)}return this.parsedPos=o,new le(We.none,[],[],o-r)},stoppedAt:null,stopAt(){}}}}}isDone(e){e=Math.min(e,this.state.doc.length);let t=this.fragments;return this.treeLen>=e&&t.length&&t[0].from==0&&t[0].to>=e}static get(){return $i}}function Ja(n,e,t){return ei.applyChanges(n,[{fromA:e,toA:t,fromB:e,toB:t}])}class Ei{constructor(e){this.context=e,this.tree=e.tree}apply(e){if(!e.docChanged&&this.tree==this.context.tree)return this;let t=this.context.changes(e.changes,e.state),i=this.context.treeLen==e.startState.doc.length?void 0:Math.max(e.changes.mapPos(this.context.treeLen),t.viewport.to);return t.work(20,i)||t.takeTree(),new Ei(t)}static init(e){let t=Math.min(3e3,e.doc.length),i=ks.create(e.facet(jt).parser,e,{from:0,to:t});return i.work(20,t)||i.takeTree(),new Ei(i)}}et.state=ue.define({create:Ei.init,update(n,e){for(let t of e.effects)if(t.is(et.setState))return t.value;return e.startState.facet(jt)!=e.state.facet(jt)?Ei.init(e.state):n.apply(e)}});let Df=n=>{let e=setTimeout(()=>n(),500);return()=>clearTimeout(e)};typeof requestIdleCallback<"u"&&(Df=n=>{let e=-1,t=setTimeout(()=>{e=requestIdleCallback(n,{timeout:400})},100);return()=>e<0?clearTimeout(t):cancelIdleCallback(e)});const yr=typeof navigator<"u"&&(!((gr=navigator.scheduling)===null||gr===void 0)&&gr.isInputPending)?()=>navigator.scheduling.isInputPending():null,m0=ne.fromClass(class{constructor(e){this.view=e,this.working=null,this.workScheduled=0,this.chunkEnd=-1,this.chunkBudget=-1,this.work=this.work.bind(this),this.scheduleWork()}update(e){let t=this.view.state.field(et.state).context;(t.updateViewport(e.view.viewport)||this.view.viewport.to>t.treeLen)&&this.scheduleWork(),(e.docChanged||e.selectionSet)&&(this.view.hasFocus&&(this.chunkBudget+=50),this.scheduleWork()),this.checkAsyncSchedule(t)}scheduleWork(){if(this.working)return;let{state:e}=this.view,t=e.field(et.state);(t.tree!=t.context.tree||!t.context.isDone(e.doc.length))&&(this.working=Df(this.work))}work(e){this.working=null;let t=Date.now();if(this.chunkEnd<t&&(this.chunkEnd<0||this.view.hasFocus)&&(this.chunkEnd=t+3e4,this.chunkBudget=3e3),this.chunkBudget<=0)return;let{state:i,viewport:{to:s}}=this.view,r=i.field(et.state);if(r.tree==r.context.tree&&r.context.isDone(s+1e5))return;let o=Date.now()+Math.min(this.chunkBudget,100,e&&!yr?Math.max(25,e.timeRemaining()-5):1e9),l=r.context.treeLen<s&&i.doc.length>s+1e3,a=r.context.work(()=>yr&&yr()||Date.now()>o,s+(l?0:1e5));this.chunkBudget-=Date.now()-t,(a||this.chunkBudget<=0)&&(r.context.takeTree(),this.view.dispatch({effects:et.setState.of(new Ei(r.context))})),this.chunkBudget>0&&!(a&&!l)&&this.scheduleWork(),this.checkAsyncSchedule(r.context)}checkAsyncSchedule(e){e.scheduleOn&&(this.workScheduled++,e.scheduleOn.then(()=>this.scheduleWork()).catch(t=>De(this.view.state,t)).then(()=>this.workScheduled--),e.scheduleOn=null)}destroy(){this.working&&this.working()}isWorking(){return!!(this.working||this.workScheduled>0)}},{eventHandlers:{focus(){this.scheduleWork()}}}),jt=F.define({combine(n){return n.length?n[0]:null},enables:n=>[et.state,m0,R.contentAttributes.compute([n],e=>{let t=e.facet(n);return t&&t.name?{"data-language":t.name}:{}})]});class Pf{constructor(e,t=[]){this.language=e,this.support=t,this.extension=[e,t]}}const g0=F.define(),_s=F.define({combine:n=>{if(!n.length)return"  ";let e=n[0];if(!e||/\S/.test(e)||Array.from(e).some(t=>t!=e[0]))throw new Error("Invalid indent unit: "+JSON.stringify(n[0]));return e}});function Cs(n){let e=n.facet(_s);return e.charCodeAt(0)==9?n.tabSize*e.length:e.length}function fn(n,e){let t="",i=n.tabSize,s=n.facet(_s)[0];if(s=="	"){for(;e>=i;)t+="	",e-=i;s=" "}for(let r=0;r<e;r++)t+=s;return t}function cl(n,e){n instanceof j&&(n=new $s(n));for(let i of n.state.facet(g0)){let s=i(n,e);if(s!==void 0)return s}let t=ge(n.state);return t.length>=e?y0(n,t,e):null}class $s{constructor(e,t={}){this.state=e,this.options=t,this.unit=Cs(e)}lineAt(e,t=1){let i=this.state.doc.lineAt(e),{simulateBreak:s,simulateDoubleBreak:r}=this.options;return s!=null&&s>=i.from&&s<=i.to?r&&s==e?{text:"",from:e}:(t<0?s<e:s<=e)?{text:i.text.slice(s-i.from),from:s}:{text:i.text.slice(0,s-i.from),from:i.from}:i}textAfterPos(e,t=1){if(this.options.simulateDoubleBreak&&e==this.options.simulateBreak)return"";let{text:i,from:s}=this.lineAt(e,t);return i.slice(e-s,Math.min(i.length,e+100-s))}column(e,t=1){let{text:i,from:s}=this.lineAt(e,t),r=this.countColumn(i,e-s),o=this.options.overrideIndentation?this.options.overrideIndentation(s):-1;return o>-1&&(r+=o-this.countColumn(i,i.search(/\S|$/))),r}countColumn(e,t=e.length){return Ni(e,this.state.tabSize,t)}lineIndent(e,t=1){let{text:i,from:s}=this.lineAt(e,t),r=this.options.overrideIndentation;if(r){let o=r(s);if(o>-1)return o}return this.countColumn(i,i.search(/\S|$/))}get simulatedBreak(){return this.options.simulateBreak||null}}const fl=new K;function y0(n,e,t){let i=e.resolveStack(t),s=e.resolveInner(t,-1).resolve(t,0).enterUnfinishedNodesBefore(t);if(s!=i.node){let r=[];for(let o=s;o&&!(o.from<i.node.from||o.to>i.node.to||o.from==i.node.from&&o.type==i.node.type);o=o.parent)r.push(o);for(let o=r.length-1;o>=0;o--)i={node:r[o],next:i}}return Nf(i,n,t)}function Nf(n,e,t){for(let i=n;i;i=i.next){let s=x0(i.node);if(s)return s(ul.create(e,t,i))}return 0}function b0(n){return n.pos==n.options.simulateBreak&&n.options.simulateDoubleBreak}function x0(n){let e=n.type.prop(fl);if(e)return e;let t=n.firstChild,i;if(t&&(i=t.type.prop(K.closedBy))){let s=n.lastChild,r=s&&i.indexOf(s.name)>-1;return o=>O0(o,!0,1,void 0,r&&!b0(o)?s.from:void 0)}return n.parent==null?v0:null}function v0(){return 0}class ul extends $s{constructor(e,t,i){super(e.state,e.options),this.base=e,this.pos=t,this.context=i}get node(){return this.context.node}static create(e,t,i){return new ul(e,t,i)}get textAfter(){return this.textAfterPos(this.pos)}get baseIndent(){return this.baseIndentFor(this.node)}baseIndentFor(e){let t=this.state.doc.lineAt(e.from);for(;;){let i=e.resolve(t.from);for(;i.parent&&i.parent.from==i.from;)i=i.parent;if(w0(i,e))break;t=this.state.doc.lineAt(i.from)}return this.lineIndent(t.from)}continue(){return Nf(this.context.next,this.base,this.pos)}}function w0(n,e){for(let t=e;t;t=t.parent)if(n==t)return!0;return!1}function S0(n){let e=n.node,t=e.childAfter(e.from),i=e.lastChild;if(!t)return null;let s=n.options.simulateBreak,r=n.state.doc.lineAt(t.from),o=s==null||s<=r.from?r.to:Math.min(r.to,s);for(let l=t.to;;){let a=e.childAfter(l);if(!a||a==i)return null;if(!a.type.isSkipped){if(a.from>=o)return null;let h=/^ */.exec(r.text.slice(t.to-r.from))[0].length;return{from:t.from,to:t.to+h}}l=a.to}}function O0(n,e,t,i,s){let r=n.textAfter,o=r.match(/^\s*/)[0].length,l=i&&r.slice(o,o+i.length)==i||s==n.pos+o,a=S0(n);return a?l?n.column(a.from):n.column(a.to):n.baseIndent+(l?0:n.unit*t)}function Za({except:n,units:e=1}={}){return t=>{let i=n&&n.test(t.textAfter);return t.baseIndent+(i?0:e*t.unit)}}const k0=200;function C0(){return j.transactionFilter.of(n=>{if(!n.docChanged||!n.isUserEvent("input.type")&&!n.isUserEvent("input.complete"))return n;let e=n.startState.languageDataAt("indentOnInput",n.startState.selection.main.head);if(!e.length)return n;let t=n.newDoc,{head:i}=n.newSelection.main,s=t.lineAt(i);if(i>s.from+k0)return n;let r=t.sliceString(s.from,i);if(!e.some(h=>h.test(r)))return n;let{state:o}=n,l=-1,a=[];for(let{head:h}of o.selection.ranges){let c=o.doc.lineAt(h);if(c.from==l)continue;l=c.from;let f=cl(o,c.from);if(f==null)continue;let u=/^\s*/.exec(c.text)[0],d=fn(o,f);u!=d&&a.push({from:c.from,to:c.from+u.length,insert:d})}return a.length?[n,{changes:a,sequential:!0}]:n})}const A0=F.define(),dl=new K;function T0(n){let e=n.firstChild,t=n.lastChild;return e&&e.to<t.from?{from:e.to,to:t.type.isError?n.to:t.from}:null}function M0(n,e,t){let i=ge(n);if(i.length<t)return null;let s=i.resolveStack(t,1),r=null;for(let o=s;o;o=o.next){let l=o.node;if(l.to<=t||l.from>t)continue;if(r&&l.from<e)break;let a=l.type.prop(dl);if(a&&(l.to<i.length-50||i.length==n.doc.length||!E0(l))){let h=a(l,n);h&&h.from<=t&&h.from>=e&&h.to>t&&(r=h)}}return r}function E0(n){let e=n.lastChild;return e&&e.to==n.to&&e.type.isError}function As(n,e,t){for(let i of n.facet(A0)){let s=i(n,e,t);if(s)return s}return M0(n,e,t)}function Rf(n,e){let t=e.mapPos(n.from,1),i=e.mapPos(n.to,-1);return t>=i?void 0:{from:t,to:i}}const qs=_.define({map:Rf}),Sn=_.define({map:Rf});function Bf(n){let e=[];for(let{head:t}of n.state.selection.ranges)e.some(i=>i.from<=t&&i.to>=t)||e.push(n.lineBlockAt(t));return e}const oi=ue.define({create(){return W.none},update(n,e){e.isUserEvent("delete")&&e.changes.iterChangedRanges((t,i)=>n=eh(n,t,i)),n=n.map(e.changes);for(let t of e.effects)if(t.is(qs)&&!D0(n,t.value.from,t.value.to)){let{preparePlaceholder:i}=e.state.facet(Ff),s=i?W.replace({widget:new F0(i(e.state,t.value))}):th;n=n.update({add:[s.range(t.value.from,t.value.to)]})}else t.is(Sn)&&(n=n.update({filter:(i,s)=>t.value.from!=i||t.value.to!=s,filterFrom:t.value.from,filterTo:t.value.to}));return e.selection&&(n=eh(n,e.selection.main.head)),n},provide:n=>R.decorations.from(n),toJSON(n,e){let t=[];return n.between(0,e.doc.length,(i,s)=>{t.push(i,s)}),t},fromJSON(n){if(!Array.isArray(n)||n.length%2)throw new RangeError("Invalid JSON for fold state");let e=[];for(let t=0;t<n.length;){let i=n[t++],s=n[t++];if(typeof i!="number"||typeof s!="number")throw new RangeError("Invalid JSON for fold state");e.push(th.range(i,s))}return W.set(e,!0)}});function eh(n,e,t=e){let i=!1;return n.between(e,t,(s,r)=>{s<t&&r>e&&(i=!0)}),i?n.update({filterFrom:e,filterTo:t,filter:(s,r)=>s>=t||r<=e}):n}function Ts(n,e,t){var i;let s=null;return(i=n.field(oi,!1))===null||i===void 0||i.between(e,t,(r,o)=>{(!s||s.from>r)&&(s={from:r,to:o})}),s}function D0(n,e,t){let i=!1;return n.between(e,e,(s,r)=>{s==e&&r==t&&(i=!0)}),i}function If(n,e){return n.field(oi,!1)?e:e.concat(_.appendConfig.of(Vf()))}const P0=n=>{for(let e of Bf(n)){let t=As(n.state,e.from,e.to);if(t)return n.dispatch({effects:If(n.state,[qs.of(t),Lf(n,t)])}),!0}return!1},N0=n=>{if(!n.state.field(oi,!1))return!1;let e=[];for(let t of Bf(n)){let i=Ts(n.state,t.from,t.to);i&&e.push(Sn.of(i),Lf(n,i,!1))}return e.length&&n.dispatch({effects:e}),e.length>0};function Lf(n,e,t=!0){let i=n.state.doc.lineAt(e.from).number,s=n.state.doc.lineAt(e.to).number;return R.announce.of(`${n.state.phrase(t?"Folded lines":"Unfolded lines")} ${i} ${n.state.phrase("to")} ${s}.`)}const R0=n=>{let{state:e}=n,t=[];for(let i=0;i<e.doc.length;){let s=n.lineBlockAt(i),r=As(e,s.from,s.to);r&&t.push(qs.of(r)),i=(r?n.lineBlockAt(r.to):s).to+1}return t.length&&n.dispatch({effects:If(n.state,t)}),!!t.length},B0=n=>{let e=n.state.field(oi,!1);if(!e||!e.size)return!1;let t=[];return e.between(0,n.state.doc.length,(i,s)=>{t.push(Sn.of({from:i,to:s}))}),n.dispatch({effects:t}),!0},I0=[{key:"Ctrl-Shift-[",mac:"Cmd-Alt-[",run:P0},{key:"Ctrl-Shift-]",mac:"Cmd-Alt-]",run:N0},{key:"Ctrl-Alt-[",run:R0},{key:"Ctrl-Alt-]",run:B0}],L0={placeholderDOM:null,preparePlaceholder:null,placeholderText:"…"},Ff=F.define({combine(n){return nt(n,L0)}});function Vf(n){return[oi,_0]}function Wf(n,e){let{state:t}=n,i=t.facet(Ff),s=o=>{let l=n.lineBlockAt(n.posAtDOM(o.target)),a=Ts(n.state,l.from,l.to);a&&n.dispatch({effects:Sn.of(a)}),o.preventDefault()};if(i.placeholderDOM)return i.placeholderDOM(n,s,e);let r=document.createElement("span");return r.textContent=i.placeholderText,r.setAttribute("aria-label",t.phrase("folded code")),r.title=t.phrase("unfold"),r.className="cm-foldPlaceholder",r.onclick=s,r}const th=W.replace({widget:new class extends Et{toDOM(n){return Wf(n,null)}}});class F0 extends Et{constructor(e){super(),this.value=e}eq(e){return this.value==e.value}toDOM(e){return Wf(e,this.value)}}const V0={openText:"⌄",closedText:"›",markerDOM:null,domEventHandlers:{},foldingChanged:()=>!1};class br extends bt{constructor(e,t){super(),this.config=e,this.open=t}eq(e){return this.config==e.config&&this.open==e.open}toDOM(e){if(this.config.markerDOM)return this.config.markerDOM(this.open);let t=document.createElement("span");return t.textContent=this.open?this.config.openText:this.config.closedText,t.title=e.state.phrase(this.open?"Fold line":"Unfold line"),t}}function W0(n={}){let e={...V0,...n},t=new br(e,!0),i=new br(e,!1),s=ne.fromClass(class{constructor(o){this.from=o.viewport.from,this.markers=this.buildMarkers(o)}update(o){(o.docChanged||o.viewportChanged||o.startState.facet(jt)!=o.state.facet(jt)||o.startState.field(oi,!1)!=o.state.field(oi,!1)||ge(o.startState)!=ge(o.state)||e.foldingChanged(o))&&(this.markers=this.buildMarkers(o.view))}buildMarkers(o){let l=new At;for(let a of o.viewportLineBlocks){let h=Ts(o.state,a.from,a.to)?i:As(o.state,a.from,a.to)?t:null;h&&l.add(a.from,a.from,h)}return l.finish()}}),{domEventHandlers:r}=e;return[s,xf({class:"cm-foldGutter",markers(o){var l;return((l=o.plugin(s))===null||l===void 0?void 0:l.markers)||U.empty},initialSpacer(){return new br(e,!1)},domEventHandlers:{...r,click:(o,l,a)=>{if(r.click&&r.click(o,l,a))return!0;let h=Ts(o.state,l.from,l.to);if(h)return o.dispatch({effects:Sn.of(h)}),!0;let c=As(o.state,l.from,l.to);return c?(o.dispatch({effects:qs.of(c)}),!0):!1}}}),Vf()]}const _0=R.baseTheme({".cm-foldPlaceholder":{backgroundColor:"#eee",border:"1px solid #ddd",color:"#888",borderRadius:".2em",margin:"0 1px",padding:"0 1px",cursor:"pointer"},".cm-foldGutter span":{padding:"0 1px",cursor:"pointer"}});class Ri{constructor(e,t){this.specs=e;let i;function s(l){let a=$t.newName();return(i||(i=Object.create(null)))["."+a]=l,a}const r=typeof t.all=="string"?t.all:t.all?s(t.all):void 0,o=t.scope;this.scope=o instanceof et?l=>l.prop(gi)==o.data:o?l=>l==o:void 0,this.style=Ef(e.map(l=>({tag:l.tag,class:l.class||s(Object.assign({},l,{tag:null}))})),{all:r}).style,this.module=i?new $t(i):null,this.themeType=t.themeType}static define(e,t){return new Ri(e,t||{})}}const To=F.define(),_f=F.define({combine(n){return n.length?[n[0]]:null}});function xr(n){let e=n.facet(To);return e.length?e:n.facet(_f)}function pl(n,e){let t=[q0],i;return n instanceof Ri&&(n.module&&t.push(R.styleModule.of(n.module)),i=n.themeType),e!=null&&e.fallback?t.push(_f.of(n)):i?t.push(To.computeN([R.darkTheme],s=>s.facet(R.darkTheme)==(i=="dark")?[n]:[])):t.push(To.of(n)),t}class $0{constructor(e){this.markCache=Object.create(null),this.tree=ge(e.state),this.decorations=this.buildDeco(e,xr(e.state)),this.decoratedTo=e.viewport.to}update(e){let t=ge(e.state),i=xr(e.state),s=i!=xr(e.startState),{viewport:r}=e.view,o=e.changes.mapPos(this.decoratedTo,1);t.length<r.to&&!s&&t.type==this.tree.type&&o>=r.to?(this.decorations=this.decorations.map(e.changes),this.decoratedTo=o):(t!=this.tree||e.viewportChanged||s)&&(this.tree=t,this.decorations=this.buildDeco(e.view,i),this.decoratedTo=r.to)}buildDeco(e,t){if(!t||!this.tree.length)return W.none;let i=new At;for(let{from:s,to:r}of e.visibleRanges)jg(this.tree,t,(o,l,a)=>{i.add(o,l,this.markCache[a]||(this.markCache[a]=W.mark({class:a})))},s,r);return i.finish()}}const q0=li.high(ne.fromClass($0,{decorations:n=>n.decorations})),H0=Ri.define([{tag:A.meta,color:"#404740"},{tag:A.link,textDecoration:"underline"},{tag:A.heading,textDecoration:"underline",fontWeight:"bold"},{tag:A.emphasis,fontStyle:"italic"},{tag:A.strong,fontWeight:"bold"},{tag:A.strikethrough,textDecoration:"line-through"},{tag:A.keyword,color:"#708"},{tag:[A.atom,A.bool,A.url,A.contentSeparator,A.labelName],color:"#219"},{tag:[A.literal,A.inserted],color:"#164"},{tag:[A.string,A.deleted],color:"#a11"},{tag:[A.regexp,A.escape,A.special(A.string)],color:"#e40"},{tag:A.definition(A.variableName),color:"#00f"},{tag:A.local(A.variableName),color:"#30a"},{tag:[A.typeName,A.namespace],color:"#085"},{tag:A.className,color:"#167"},{tag:[A.special(A.variableName),A.macroName],color:"#256"},{tag:A.definition(A.propertyName),color:"#00c"},{tag:A.comment,color:"#940"},{tag:A.invalid,color:"#f00"}]),z0=R.baseTheme({"&.cm-focused .cm-matchingBracket":{backgroundColor:"#328c8252"},"&.cm-focused .cm-nonmatchingBracket":{backgroundColor:"#bb555544"}}),$f=1e4,qf="()[]{}",Hf=F.define({combine(n){return nt(n,{afterCursor:!0,brackets:qf,maxScanDistance:$f,renderMatch:U0})}}),j0=W.mark({class:"cm-matchingBracket"}),K0=W.mark({class:"cm-nonmatchingBracket"});function U0(n){let e=[],t=n.matched?j0:K0;return e.push(t.range(n.start.from,n.start.to)),n.end&&e.push(t.range(n.end.from,n.end.to)),e}const G0=ue.define({create(){return W.none},update(n,e){if(!e.docChanged&&!e.selection)return n;let t=[],i=e.state.facet(Hf);for(let s of e.state.selection.ranges){if(!s.empty)continue;let r=dt(e.state,s.head,-1,i)||s.head>0&&dt(e.state,s.head-1,1,i)||i.afterCursor&&(dt(e.state,s.head,1,i)||s.head<e.state.doc.length&&dt(e.state,s.head+1,-1,i));r&&(t=t.concat(i.renderMatch(r,e.state)))}return W.set(t,!0)},provide:n=>R.decorations.from(n)}),X0=[G0,z0];function Q0(n={}){return[Hf.of(n),X0]}const zf=new K;function Mo(n,e,t){let i=n.prop(e<0?K.openedBy:K.closedBy);if(i)return i;if(n.name.length==1){let s=t.indexOf(n.name);if(s>-1&&s%2==(e<0?1:0))return[t[s+e]]}return null}function Eo(n){let e=n.type.prop(zf);return e?e(n.node):n}function dt(n,e,t,i={}){let s=i.maxScanDistance||$f,r=i.brackets||qf,o=ge(n),l=o.resolveInner(e,t);for(let a=l;a;a=a.parent){let h=Mo(a.type,t,r);if(h&&a.from<a.to){let c=Eo(a);if(c&&(t>0?e>=c.from&&e<c.to:e>c.from&&e<=c.to))return Y0(n,e,t,a,c,h,r)}}return J0(n,e,t,o,l.type,s,r)}function Y0(n,e,t,i,s,r,o){let l=i.parent,a={from:s.from,to:s.to},h=0,c=l==null?void 0:l.cursor();if(c&&(t<0?c.childBefore(i.from):c.childAfter(i.to)))do if(t<0?c.to<=i.from:c.from>=i.to){if(h==0&&r.indexOf(c.type.name)>-1&&c.from<c.to){let f=Eo(c);return{start:a,end:f?{from:f.from,to:f.to}:void 0,matched:!0}}else if(Mo(c.type,t,o))h++;else if(Mo(c.type,-t,o)){if(h==0){let f=Eo(c);return{start:a,end:f&&f.from<f.to?{from:f.from,to:f.to}:void 0,matched:!1}}h--}}while(t<0?c.prevSibling():c.nextSibling());return{start:a,matched:!1}}function J0(n,e,t,i,s,r,o){let l=t<0?n.sliceDoc(e-1,e):n.sliceDoc(e,e+1),a=o.indexOf(l);if(a<0||a%2==0!=t>0)return null;let h={from:t<0?e-1:e,to:t>0?e+1:e},c=n.doc.iterRange(e,t>0?n.doc.length:0),f=0;for(let u=0;!c.next().done&&u<=r;){let d=c.value;t<0&&(u+=d.length);let m=e+u*t;for(let x=t>0?0:d.length-1,S=t>0?d.length:-1;x!=S;x+=t){let y=o.indexOf(d[x]);if(!(y<0||i.resolveInner(m+x,1).type!=s))if(y%2==0==t>0)f++;else{if(f==1)return{start:h,end:{from:m+x,to:m+x+1},matched:y>>1==a>>1};f--}}t>0&&(u+=d.length)}return c.done?{start:h,matched:!1}:null}const Z0=Object.create(null),ih=[We.none],nh=[],sh=Object.create(null),ey=Object.create(null);for(let[n,e]of[["variable","variableName"],["variable-2","variableName.special"],["string-2","string.special"],["def","variableName.definition"],["tag","tagName"],["attribute","attributeName"],["type","typeName"],["builtin","variableName.standard"],["qualifier","modifier"],["error","invalid"],["header","heading"],["property","propertyName"]])ey[n]=ty(Z0,e);function vr(n,e){nh.indexOf(n)>-1||(nh.push(n),console.warn(e))}function ty(n,e){let t=[];for(let l of e.split(" ")){let a=[];for(let h of l.split(".")){let c=n[h]||A[h];c?typeof c=="function"?a.length?a=a.map(c):vr(h,`Modifier ${h} used at start of tag`):a.length?vr(h,`Tag ${h} used as modifier`):a=Array.isArray(c)?c:[c]:vr(h,`Unknown highlighting tag ${h}`)}for(let h of a)t.push(h)}if(!t.length)return 0;let i=e.replace(/ /g,"_"),s=i+" "+t.map(l=>l.id),r=sh[s];if(r)return r.id;let o=sh[s]=We.define({id:ih.length,name:i,props:[al({[i]:t})]});return ih.push(o),o.id}ee.RTL,ee.LTR;function ls(n,e){let t=e&&e.getChild("TagName");return t?n.sliceString(t.from,t.to):""}function wr(n,e){let t=e&&e.firstChild;return!t||t.name!="OpenTag"?"":ls(n,t)}function iy(n,e,t){let i=e&&e.getChildren("Attribute").find(r=>r.from<=t&&r.to>=t),s=i&&i.getChild("AttributeName");return s?n.sliceString(s.from,s.to):""}function Sr(n){for(let e=n&&n.parent;e;e=e.parent)if(e.name=="Element")return e;return null}function ny(n,e){var t;let i=ge(n).resolveInner(e,-1),s=null;for(let r=i;!s&&r.parent;r=r.parent)(r.name=="OpenTag"||r.name=="CloseTag"||r.name=="SelfClosingTag"||r.name=="MismatchedCloseTag")&&(s=r);if(s&&(s.to>e||s.lastChild.type.isError)){let r=s.parent;if(i.name=="TagName")return s.name=="CloseTag"||s.name=="MismatchedCloseTag"?{type:"closeTag",from:i.from,context:r}:{type:"openTag",from:i.from,context:Sr(r)};if(i.name=="AttributeName")return{type:"attrName",from:i.from,context:s};if(i.name=="AttributeValue")return{type:"attrValue",from:i.from,context:s};let o=i==s||i.name=="Attribute"?i.childBefore(e):i;return(o==null?void 0:o.name)=="StartTag"?{type:"openTag",from:e,context:Sr(r)}:(o==null?void 0:o.name)=="StartCloseTag"&&o.to<=e?{type:"closeTag",from:e,context:r}:(o==null?void 0:o.name)=="Is"?{type:"attrValue",from:e,context:s}:o?{type:"attrName",from:e,context:s}:null}else if(i.name=="StartCloseTag")return{type:"closeTag",from:e,context:i.parent};for(;i.parent&&i.to==e&&!(!((t=i.lastChild)===null||t===void 0)&&t.type.isError);)i=i.parent;return i.name=="Element"||i.name=="Text"||i.name=="Document"?{type:"tag",from:e,context:i.name=="Element"?i:Sr(i)}:null}class sy{constructor(e,t,i){this.attrs=t,this.attrValues=i,this.children=[],this.name=e.name,this.completion=Object.assign(Object.assign({type:"type"},e.completion||{}),{label:this.name}),this.openCompletion=Object.assign(Object.assign({},this.completion),{label:"<"+this.name}),this.closeCompletion=Object.assign(Object.assign({},this.completion),{label:"</"+this.name+">",boost:2}),this.closeNameCompletion=Object.assign(Object.assign({},this.completion),{label:this.name+">"}),this.text=e.textContent?e.textContent.map(s=>({label:s,type:"text"})):[]}}const Or=/^[:\-\.\w\u00b7-\uffff]*$/;function rh(n){return Object.assign(Object.assign({type:"property"},n.completion||{}),{label:n.name})}function oh(n){return typeof n=="string"?{label:`"${n}"`,type:"constant"}:/^"/.test(n.label)?n:Object.assign(Object.assign({},n),{label:`"${n.label}"`})}function ry(n,e){let t=[],i=[],s=Object.create(null);for(let a of e){let h=rh(a);t.push(h),a.global&&i.push(h),a.values&&(s[a.name]=a.values.map(oh))}let r=[],o=[],l=Object.create(null);for(let a of n){let h=i,c=s;a.attributes&&(h=h.concat(a.attributes.map(u=>typeof u=="string"?t.find(d=>d.label==u)||{label:u,type:"property"}:(u.values&&(c==s&&(c=Object.create(c)),c[u.name]=u.values.map(oh)),rh(u)))));let f=new sy(a,h,c);l[f.name]=f,r.push(f),a.top&&o.push(f)}o.length||(o=r);for(let a=0;a<r.length;a++){let h=n[a],c=r[a];if(h.children)for(let f of h.children)l[f]&&c.children.push(l[f]);else c.children=r}return a=>{var h;let{doc:c}=a.state,f=ny(a.state,a.pos);if(!f||f.type=="tag"&&!a.explicit)return null;let{type:u,from:d,context:m}=f;if(u=="openTag"){let x=o,S=wr(c,m);if(S){let y=l[S];x=(y==null?void 0:y.children)||r}return{from:d,options:x.map(y=>y.completion),validFor:Or}}else if(u=="closeTag"){let x=wr(c,m);return x?{from:d,to:a.pos+(c.sliceString(a.pos,a.pos+1)==">"?1:0),options:[((h=l[x])===null||h===void 0?void 0:h.closeNameCompletion)||{label:x+">",type:"type"}],validFor:Or}:null}else if(u=="attrName"){let x=l[ls(c,m)];return{from:d,options:(x==null?void 0:x.attrs)||i,validFor:Or}}else if(u=="attrValue"){let x=iy(c,m,d);if(!x)return null;let S=l[ls(c,m)],y=((S==null?void 0:S.attrValues)||s)[x];return!y||!y.length?null:{from:d,to:a.pos+(c.sliceString(a.pos,a.pos+1)=='"'?1:0),options:y,validFor:/^"[^"]*"?$/}}else if(u=="tag"){let x=wr(c,m),S=l[x],y=[],O=m&&m.lastChild;x&&(!O||O.name!="CloseTag"||ls(c,O)!=x)&&y.push(S?S.closeCompletion:{label:"</"+x+">",type:"type",boost:2});let v=y.concat(((S==null?void 0:S.children)||(m?r:o)).map(g=>g.openCompletion));if(m&&(S!=null&&S.text.length)){let g=m.firstChild;g.to>a.pos-20&&!/\S/.test(a.state.sliceDoc(g.to,a.pos))&&(v=v.concat(S.text))}return{from:d,options:v,validFor:/^<\/?[:\-\.\w\u00b7-\uffff]*$/}}else return null}}const Do=cn.define({name:"xml",parser:f0.configure({props:[fl.add({Element(n){let e=/^\s*<\//.test(n.textAfter);return n.lineIndent(n.node.from)+(e?0:n.unit)},"OpenTag CloseTag SelfClosingTag"(n){return n.column(n.node.from)+n.unit}}),dl.add({Element(n){let e=n.firstChild,t=n.lastChild;return!e||e.name!="OpenTag"?null:{from:e.to,to:t.name=="CloseTag"?t.from:n.to}}}),zf.add({"OpenTag CloseTag":n=>n.getChild("TagName")})]}),languageData:{commentTokens:{block:{open:"<!--",close:"-->"}},indentOnInput:/^\s*<\/$/}});function oy(n={}){let e=[Do.data.of({autocomplete:ry(n.elements||[],n.attributes||[])})];return n.autoCloseTags!==!1&&e.push(ly),new Pf(Do,e)}function lh(n,e,t=n.length){if(!e)return"";let i=e.firstChild,s=i&&i.getChild("TagName");return s?n.sliceString(s.from,Math.min(s.to,t)):""}const ly=R.inputHandler.of((n,e,t,i,s)=>{if(n.composing||n.state.readOnly||e!=t||i!=">"&&i!="/"||!Do.isActiveAt(n.state,e,-1))return!1;let r=s(),{state:o}=r,l=o.changeByRange(a=>{var h,c,f;let{head:u}=a,d=o.doc.sliceString(u-1,u)==i,m=ge(o).resolveInner(u,-1),x;if(d&&i==">"&&m.name=="EndTag"){let S=m.parent;if(((c=(h=S.parent)===null||h===void 0?void 0:h.lastChild)===null||c===void 0?void 0:c.name)!="CloseTag"&&(x=lh(o.doc,S.parent,u))){let y=u+(o.doc.sliceString(u,u+1)===">"?1:0),O=`</${x}>`;return{range:a,changes:{from:u,to:y,insert:O}}}}else if(d&&i=="/"&&m.name=="StartCloseTag"){let S=m.parent;if(m.from==u-2&&((f=S.lastChild)===null||f===void 0?void 0:f.name)!="CloseTag"&&(x=lh(o.doc,S,u))){let y=u+(o.doc.sliceString(u,u+1)===">"?1:0),O=`${x}>`;return{range:E.cursor(u+O.length,-1),changes:{from:u,to:y,insert:O}}}}return{range:a}});return l.changes.empty?!1:(n.dispatch([r,o.update(l,{userEvent:"input.complete",scrollIntoView:!0})]),!0)}),ay=n=>{let{state:e}=n,t=e.doc.lineAt(e.selection.main.from),i=gl(n.state,t.from);return i.line?hy(n):i.block?fy(n):!1};function ml(n,e){return({state:t,dispatch:i})=>{if(t.readOnly)return!1;let s=n(e,t);return s?(i(t.update(s)),!0):!1}}const hy=ml(py,0),cy=ml(jf,0),fy=ml((n,e)=>jf(n,e,dy(e)),0);function gl(n,e){let t=n.languageDataAt("commentTokens",e,1);return t.length?t[0]:{}}const qi=50;function uy(n,{open:e,close:t},i,s){let r=n.sliceDoc(i-qi,i),o=n.sliceDoc(s,s+qi),l=/\s*$/.exec(r)[0].length,a=/^\s*/.exec(o)[0].length,h=r.length-l;if(r.slice(h-e.length,h)==e&&o.slice(a,a+t.length)==t)return{open:{pos:i-l,margin:l&&1},close:{pos:s+a,margin:a&&1}};let c,f;s-i<=2*qi?c=f=n.sliceDoc(i,s):(c=n.sliceDoc(i,i+qi),f=n.sliceDoc(s-qi,s));let u=/^\s*/.exec(c)[0].length,d=/\s*$/.exec(f)[0].length,m=f.length-d-t.length;return c.slice(u,u+e.length)==e&&f.slice(m,m+t.length)==t?{open:{pos:i+u+e.length,margin:/\s/.test(c.charAt(u+e.length))?1:0},close:{pos:s-d-t.length,margin:/\s/.test(f.charAt(m-1))?1:0}}:null}function dy(n){let e=[];for(let t of n.selection.ranges){let i=n.doc.lineAt(t.from),s=t.to<=i.to?i:n.doc.lineAt(t.to);s.from>i.from&&s.from==t.to&&(s=t.to==i.to+1?i:n.doc.lineAt(t.to-1));let r=e.length-1;r>=0&&e[r].to>i.from?e[r].to=s.to:e.push({from:i.from+/^\s*/.exec(i.text)[0].length,to:s.to})}return e}function jf(n,e,t=e.selection.ranges){let i=t.map(r=>gl(e,r.from).block);if(!i.every(r=>r))return null;let s=t.map((r,o)=>uy(e,i[o],r.from,r.to));if(n!=2&&!s.every(r=>r))return{changes:e.changes(t.map((r,o)=>s[o]?[]:[{from:r.from,insert:i[o].open+" "},{from:r.to,insert:" "+i[o].close}]))};if(n!=1&&s.some(r=>r)){let r=[];for(let o=0,l;o<s.length;o++)if(l=s[o]){let a=i[o],{open:h,close:c}=l;r.push({from:h.pos-a.open.length,to:h.pos+h.margin},{from:c.pos-c.margin,to:c.pos+a.close.length})}return{changes:r}}return null}function py(n,e,t=e.selection.ranges){let i=[],s=-1;for(let{from:r,to:o}of t){let l=i.length,a=1e9,h=gl(e,r).line;if(h){for(let c=r;c<=o;){let f=e.doc.lineAt(c);if(f.from>s&&(r==o||o>f.from)){s=f.from;let u=/^\s*/.exec(f.text)[0].length,d=u==f.length,m=f.text.slice(u,u+h.length)==h?u:-1;u<f.text.length&&u<a&&(a=u),i.push({line:f,comment:m,token:h,indent:u,empty:d,single:!1})}c=f.to+1}if(a<1e9)for(let c=l;c<i.length;c++)i[c].indent<i[c].line.text.length&&(i[c].indent=a);i.length==l+1&&(i[l].single=!0)}}if(n!=2&&i.some(r=>r.comment<0&&(!r.empty||r.single))){let r=[];for(let{line:l,token:a,indent:h,empty:c,single:f}of i)(f||!c)&&r.push({from:l.from+h,insert:a+" "});let o=e.changes(r);return{changes:o,selection:e.selection.map(o,1)}}else if(n!=1&&i.some(r=>r.comment>=0)){let r=[];for(let{line:o,comment:l,token:a}of i)if(l>=0){let h=o.from+l,c=h+a.length;o.text[c-o.from]==" "&&c++,r.push({from:h,to:c})}return{changes:r}}return null}const Po=Mt.define(),my=Mt.define(),gy=F.define(),Kf=F.define({combine(n){return nt(n,{minDepth:100,newGroupDelay:500,joinToEvent:(e,t)=>t},{minDepth:Math.max,newGroupDelay:Math.min,joinToEvent:(e,t)=>(i,s)=>e(i,s)||t(i,s)})}}),Uf=ue.define({create(){return pt.empty},update(n,e){let t=e.state.facet(Kf),i=e.annotation(Po);if(i){let a=Ve.fromTransaction(e,i.selection),h=i.side,c=h==0?n.undone:n.done;return a?c=Ms(c,c.length,t.minDepth,a):c=Qf(c,e.startState.selection),new pt(h==0?i.rest:c,h==0?c:i.rest)}let s=e.annotation(my);if((s=="full"||s=="before")&&(n=n.isolate()),e.annotation(fe.addToHistory)===!1)return e.changes.empty?n:n.addMapping(e.changes.desc);let r=Ve.fromTransaction(e),o=e.annotation(fe.time),l=e.annotation(fe.userEvent);return r?n=n.addChanges(r,o,l,t,e):e.selection&&(n=n.addSelection(e.startState.selection,o,l,t.newGroupDelay)),(s=="full"||s=="after")&&(n=n.isolate()),n},toJSON(n){return{done:n.done.map(e=>e.toJSON()),undone:n.undone.map(e=>e.toJSON())}},fromJSON(n){return new pt(n.done.map(Ve.fromJSON),n.undone.map(Ve.fromJSON))}});function yy(n={}){return[Uf,Kf.of(n),R.domEventHandlers({beforeinput(e,t){let i=e.inputType=="historyUndo"?Gf:e.inputType=="historyRedo"?No:null;return i?(e.preventDefault(),i(t)):!1}})]}function Hs(n,e){return function({state:t,dispatch:i}){if(!e&&t.readOnly)return!1;let s=t.field(Uf,!1);if(!s)return!1;let r=s.pop(n,t,e);return r?(i(r),!0):!1}}const Gf=Hs(0,!1),No=Hs(1,!1),by=Hs(0,!0),xy=Hs(1,!0);class Ve{constructor(e,t,i,s,r){this.changes=e,this.effects=t,this.mapped=i,this.startSelection=s,this.selectionsAfter=r}setSelAfter(e){return new Ve(this.changes,this.effects,this.mapped,this.startSelection,e)}toJSON(){var e,t,i;return{changes:(e=this.changes)===null||e===void 0?void 0:e.toJSON(),mapped:(t=this.mapped)===null||t===void 0?void 0:t.toJSON(),startSelection:(i=this.startSelection)===null||i===void 0?void 0:i.toJSON(),selectionsAfter:this.selectionsAfter.map(s=>s.toJSON())}}static fromJSON(e){return new Ve(e.changes&&ce.fromJSON(e.changes),[],e.mapped&&gt.fromJSON(e.mapped),e.startSelection&&E.fromJSON(e.startSelection),e.selectionsAfter.map(E.fromJSON))}static fromTransaction(e,t){let i=Ge;for(let s of e.startState.facet(gy)){let r=s(e);r.length&&(i=i.concat(r))}return!i.length&&e.changes.empty?null:new Ve(e.changes.invert(e.startState.doc),i,void 0,t||e.startState.selection,Ge)}static selection(e){return new Ve(void 0,Ge,void 0,void 0,e)}}function Ms(n,e,t,i){let s=e+1>t+20?e-t-1:0,r=n.slice(s,e);return r.push(i),r}function vy(n,e){let t=[],i=!1;return n.iterChangedRanges((s,r)=>t.push(s,r)),e.iterChangedRanges((s,r,o,l)=>{for(let a=0;a<t.length;){let h=t[a++],c=t[a++];l>=h&&o<=c&&(i=!0)}}),i}function wy(n,e){return n.ranges.length==e.ranges.length&&n.ranges.filter((t,i)=>t.empty!=e.ranges[i].empty).length===0}function Xf(n,e){return n.length?e.length?n.concat(e):n:e}const Ge=[],Sy=200;function Qf(n,e){if(n.length){let t=n[n.length-1],i=t.selectionsAfter.slice(Math.max(0,t.selectionsAfter.length-Sy));return i.length&&i[i.length-1].eq(e)?n:(i.push(e),Ms(n,n.length-1,1e9,t.setSelAfter(i)))}else return[Ve.selection([e])]}function Oy(n){let e=n[n.length-1],t=n.slice();return t[n.length-1]=e.setSelAfter(e.selectionsAfter.slice(0,e.selectionsAfter.length-1)),t}function kr(n,e){if(!n.length)return n;let t=n.length,i=Ge;for(;t;){let s=ky(n[t-1],e,i);if(s.changes&&!s.changes.empty||s.effects.length){let r=n.slice(0,t);return r[t-1]=s,r}else e=s.mapped,t--,i=s.selectionsAfter}return i.length?[Ve.selection(i)]:Ge}function ky(n,e,t){let i=Xf(n.selectionsAfter.length?n.selectionsAfter.map(l=>l.map(e)):Ge,t);if(!n.changes)return Ve.selection(i);let s=n.changes.map(e),r=e.mapDesc(n.changes,!0),o=n.mapped?n.mapped.composeDesc(r):r;return new Ve(s,_.mapEffects(n.effects,e),o,n.startSelection.map(r),i)}const Cy=/^(input\.type|delete)($|\.)/;class pt{constructor(e,t,i=0,s=void 0){this.done=e,this.undone=t,this.prevTime=i,this.prevUserEvent=s}isolate(){return this.prevTime?new pt(this.done,this.undone):this}addChanges(e,t,i,s,r){let o=this.done,l=o[o.length-1];return l&&l.changes&&!l.changes.empty&&e.changes&&(!i||Cy.test(i))&&(!l.selectionsAfter.length&&t-this.prevTime<s.newGroupDelay&&s.joinToEvent(r,vy(l.changes,e.changes))||i=="input.type.compose")?o=Ms(o,o.length-1,s.minDepth,new Ve(e.changes.compose(l.changes),Xf(_.mapEffects(e.effects,l.changes),l.effects),l.mapped,l.startSelection,Ge)):o=Ms(o,o.length,s.minDepth,e),new pt(o,Ge,t,i)}addSelection(e,t,i,s){let r=this.done.length?this.done[this.done.length-1].selectionsAfter:Ge;return r.length>0&&t-this.prevTime<s&&i==this.prevUserEvent&&i&&/^select($|\.)/.test(i)&&wy(r[r.length-1],e)?this:new pt(Qf(this.done,e),this.undone,t,i)}addMapping(e){return new pt(kr(this.done,e),kr(this.undone,e),this.prevTime,this.prevUserEvent)}pop(e,t,i){let s=e==0?this.done:this.undone;if(s.length==0)return null;let r=s[s.length-1],o=r.selectionsAfter[0]||t.selection;if(i&&r.selectionsAfter.length)return t.update({selection:r.selectionsAfter[r.selectionsAfter.length-1],annotations:Po.of({side:e,rest:Oy(s),selection:o}),userEvent:e==0?"select.undo":"select.redo",scrollIntoView:!0});if(r.changes){let l=s.length==1?Ge:s.slice(0,s.length-1);return r.mapped&&(l=kr(l,r.mapped)),t.update({changes:r.changes,selection:r.startSelection,effects:r.effects,annotations:Po.of({side:e,rest:l,selection:o}),filter:!1,userEvent:e==0?"undo":"redo",scrollIntoView:!0})}else return null}}pt.empty=new pt(Ge,Ge);const Ay=[{key:"Mod-z",run:Gf,preventDefault:!0},{key:"Mod-y",mac:"Mod-Shift-z",run:No,preventDefault:!0},{linux:"Ctrl-Shift-z",run:No,preventDefault:!0},{key:"Mod-u",run:by,preventDefault:!0},{key:"Alt-u",mac:"Mod-Shift-u",run:xy,preventDefault:!0}];function Bi(n,e){return E.create(n.ranges.map(e),n.mainIndex)}function xt(n,e){return n.update({selection:e,scrollIntoView:!0,userEvent:"select"})}function st({state:n,dispatch:e},t){let i=Bi(n.selection,t);return i.eq(n.selection,!0)?!1:(e(xt(n,i)),!0)}function zs(n,e){return E.cursor(e?n.to:n.from)}function Yf(n,e){return st(n,t=>t.empty?n.moveByChar(t,e):zs(t,e))}function Te(n){return n.textDirectionAt(n.state.selection.main.head)==ee.LTR}const Jf=n=>Yf(n,!Te(n)),Zf=n=>Yf(n,Te(n));function eu(n,e){return st(n,t=>t.empty?n.moveByGroup(t,e):zs(t,e))}const Ty=n=>eu(n,!Te(n)),My=n=>eu(n,Te(n));function Ey(n,e,t){if(e.type.prop(t))return!0;let i=e.to-e.from;return i&&(i>2||/[^\s,.;:]/.test(n.sliceDoc(e.from,e.to)))||e.firstChild}function js(n,e,t){let i=ge(n).resolveInner(e.head),s=t?K.closedBy:K.openedBy;for(let a=e.head;;){let h=t?i.childAfter(a):i.childBefore(a);if(!h)break;Ey(n,h,s)?i=h:a=t?h.to:h.from}let r=i.type.prop(s),o,l;return r&&(o=t?dt(n,i.from,1):dt(n,i.to,-1))&&o.matched?l=t?o.end.to:o.end.from:l=t?i.to:i.from,E.cursor(l,t?-1:1)}const Dy=n=>st(n,e=>js(n.state,e,!Te(n))),Py=n=>st(n,e=>js(n.state,e,Te(n)));function tu(n,e){return st(n,t=>{if(!t.empty)return zs(t,e);let i=n.moveVertically(t,e);return i.head!=t.head?i:n.moveToLineBoundary(t,e)})}const iu=n=>tu(n,!1),nu=n=>tu(n,!0);function su(n){let e=n.scrollDOM.clientHeight<n.scrollDOM.scrollHeight-2,t=0,i=0,s;if(e){for(let r of n.state.facet(R.scrollMargins)){let o=r(n);o!=null&&o.top&&(t=Math.max(o==null?void 0:o.top,t)),o!=null&&o.bottom&&(i=Math.max(o==null?void 0:o.bottom,i))}s=n.scrollDOM.clientHeight-t-i}else s=(n.dom.ownerDocument.defaultView||window).innerHeight;return{marginTop:t,marginBottom:i,selfScroll:e,height:Math.max(n.defaultLineHeight,s-5)}}function ru(n,e){let t=su(n),{state:i}=n,s=Bi(i.selection,o=>o.empty?n.moveVertically(o,e,t.height):zs(o,e));if(s.eq(i.selection))return!1;let r;if(t.selfScroll){let o=n.coordsAtPos(i.selection.main.head),l=n.scrollDOM.getBoundingClientRect(),a=l.top+t.marginTop,h=l.bottom-t.marginBottom;o&&o.top>a&&o.bottom<h&&(r=R.scrollIntoView(s.main.head,{y:"start",yMargin:o.top-a}))}return n.dispatch(xt(i,s),{effects:r}),!0}const ah=n=>ru(n,!1),Ro=n=>ru(n,!0);function Kt(n,e,t){let i=n.lineBlockAt(e.head),s=n.moveToLineBoundary(e,t);if(s.head==e.head&&s.head!=(t?i.to:i.from)&&(s=n.moveToLineBoundary(e,t,!1)),!t&&s.head==i.from&&i.length){let r=/^\s*/.exec(n.state.sliceDoc(i.from,Math.min(i.from+100,i.to)))[0].length;r&&e.head!=i.from+r&&(s=E.cursor(i.from+r))}return s}const Ny=n=>st(n,e=>Kt(n,e,!0)),Ry=n=>st(n,e=>Kt(n,e,!1)),By=n=>st(n,e=>Kt(n,e,!Te(n))),Iy=n=>st(n,e=>Kt(n,e,Te(n))),Ly=n=>st(n,e=>E.cursor(n.lineBlockAt(e.head).from,1)),Fy=n=>st(n,e=>E.cursor(n.lineBlockAt(e.head).to,-1));function Vy(n,e,t){let i=!1,s=Bi(n.selection,r=>{let o=dt(n,r.head,-1)||dt(n,r.head,1)||r.head>0&&dt(n,r.head-1,1)||r.head<n.doc.length&&dt(n,r.head+1,-1);if(!o||!o.end)return r;i=!0;let l=o.start.from==r.head?o.end.to:o.end.from;return E.cursor(l)});return i?(e(xt(n,s)),!0):!1}const Wy=({state:n,dispatch:e})=>Vy(n,e);function Ze(n,e){let t=Bi(n.state.selection,i=>{let s=e(i);return E.range(i.anchor,s.head,s.goalColumn,s.bidiLevel||void 0)});return t.eq(n.state.selection)?!1:(n.dispatch(xt(n.state,t)),!0)}function ou(n,e){return Ze(n,t=>n.moveByChar(t,e))}const lu=n=>ou(n,!Te(n)),au=n=>ou(n,Te(n));function hu(n,e){return Ze(n,t=>n.moveByGroup(t,e))}const _y=n=>hu(n,!Te(n)),$y=n=>hu(n,Te(n)),qy=n=>Ze(n,e=>js(n.state,e,!Te(n))),Hy=n=>Ze(n,e=>js(n.state,e,Te(n)));function cu(n,e){return Ze(n,t=>n.moveVertically(t,e))}const fu=n=>cu(n,!1),uu=n=>cu(n,!0);function du(n,e){return Ze(n,t=>n.moveVertically(t,e,su(n).height))}const hh=n=>du(n,!1),ch=n=>du(n,!0),zy=n=>Ze(n,e=>Kt(n,e,!0)),jy=n=>Ze(n,e=>Kt(n,e,!1)),Ky=n=>Ze(n,e=>Kt(n,e,!Te(n))),Uy=n=>Ze(n,e=>Kt(n,e,Te(n))),Gy=n=>Ze(n,e=>E.cursor(n.lineBlockAt(e.head).from)),Xy=n=>Ze(n,e=>E.cursor(n.lineBlockAt(e.head).to)),fh=({state:n,dispatch:e})=>(e(xt(n,{anchor:0})),!0),uh=({state:n,dispatch:e})=>(e(xt(n,{anchor:n.doc.length})),!0),dh=({state:n,dispatch:e})=>(e(xt(n,{anchor:n.selection.main.anchor,head:0})),!0),ph=({state:n,dispatch:e})=>(e(xt(n,{anchor:n.selection.main.anchor,head:n.doc.length})),!0),Qy=({state:n,dispatch:e})=>(e(n.update({selection:{anchor:0,head:n.doc.length},userEvent:"select"})),!0),Yy=({state:n,dispatch:e})=>{let t=Ks(n).map(({from:i,to:s})=>E.range(i,Math.min(s+1,n.doc.length)));return e(n.update({selection:E.create(t),userEvent:"select"})),!0},Jy=({state:n,dispatch:e})=>{let t=Bi(n.selection,i=>{let s=ge(n),r=s.resolveStack(i.from,1);if(i.empty){let o=s.resolveStack(i.from,-1);o.node.from>=r.node.from&&o.node.to<=r.node.to&&(r=o)}for(let o=r;o;o=o.next){let{node:l}=o;if((l.from<i.from&&l.to>=i.to||l.to>i.to&&l.from<=i.from)&&o.next)return E.range(l.to,l.from)}return i});return t.eq(n.selection)?!1:(e(xt(n,t)),!0)},Zy=({state:n,dispatch:e})=>{let t=n.selection,i=null;return t.ranges.length>1?i=E.create([t.main]):t.main.empty||(i=E.create([E.cursor(t.main.head)])),i?(e(xt(n,i)),!0):!1};function On(n,e){if(n.state.readOnly)return!1;let t="delete.selection",{state:i}=n,s=i.changeByRange(r=>{let{from:o,to:l}=r;if(o==l){let a=e(r);a<o?(t="delete.backward",a=zn(n,a,!1)):a>o&&(t="delete.forward",a=zn(n,a,!0)),o=Math.min(o,a),l=Math.max(l,a)}else o=zn(n,o,!1),l=zn(n,l,!0);return o==l?{range:r}:{changes:{from:o,to:l},range:E.cursor(o,o<r.head?-1:1)}});return s.changes.empty?!1:(n.dispatch(i.update(s,{scrollIntoView:!0,userEvent:t,effects:t=="delete.selection"?R.announce.of(i.phrase("Selection deleted")):void 0})),!0)}function zn(n,e,t){if(n instanceof R)for(let i of n.state.facet(R.atomicRanges).map(s=>s(n)))i.between(e,e,(s,r)=>{s<e&&r>e&&(e=t?r:s)});return e}const pu=(n,e,t)=>On(n,i=>{let s=i.from,{state:r}=n,o=r.doc.lineAt(s),l,a;if(t&&!e&&s>o.from&&s<o.from+200&&!/[^ \t]/.test(l=o.text.slice(0,s-o.from))){if(l[l.length-1]=="	")return s-1;let h=Ni(l,r.tabSize),c=h%Cs(r)||Cs(r);for(let f=0;f<c&&l[l.length-1-f]==" ";f++)s--;a=s}else a=Se(o.text,s-o.from,e,e)+o.from,a==s&&o.number!=(e?r.doc.lines:1)?a+=e?1:-1:!e&&/[\ufe00-\ufe0f]/.test(o.text.slice(a-o.from,s-o.from))&&(a=Se(o.text,a-o.from,!1,!1)+o.from);return a}),Bo=n=>pu(n,!1,!0),mu=n=>pu(n,!0,!1),gu=(n,e)=>On(n,t=>{let i=t.head,{state:s}=n,r=s.doc.lineAt(i),o=s.charCategorizer(i);for(let l=null;;){if(i==(e?r.to:r.from)){i==t.head&&r.number!=(e?s.doc.lines:1)&&(i+=e?1:-1);break}let a=Se(r.text,i-r.from,e)+r.from,h=r.text.slice(Math.min(i,a)-r.from,Math.max(i,a)-r.from),c=o(h);if(l!=null&&c!=l)break;(h!=" "||i!=t.head)&&(l=c),i=a}return i}),yu=n=>gu(n,!1),eb=n=>gu(n,!0),tb=n=>On(n,e=>{let t=n.lineBlockAt(e.head).to;return e.head<t?t:Math.min(n.state.doc.length,e.head+1)}),ib=n=>On(n,e=>{let t=n.moveToLineBoundary(e,!1).head;return e.head>t?t:Math.max(0,e.head-1)}),nb=n=>On(n,e=>{let t=n.moveToLineBoundary(e,!0).head;return e.head<t?t:Math.min(n.state.doc.length,e.head+1)}),sb=({state:n,dispatch:e})=>{if(n.readOnly)return!1;let t=n.changeByRange(i=>({changes:{from:i.from,to:i.to,insert:G.of(["",""])},range:E.cursor(i.from)}));return e(n.update(t,{scrollIntoView:!0,userEvent:"input"})),!0},rb=({state:n,dispatch:e})=>{if(n.readOnly)return!1;let t=n.changeByRange(i=>{if(!i.empty||i.from==0||i.from==n.doc.length)return{range:i};let s=i.from,r=n.doc.lineAt(s),o=s==r.from?s-1:Se(r.text,s-r.from,!1)+r.from,l=s==r.to?s+1:Se(r.text,s-r.from,!0)+r.from;return{changes:{from:o,to:l,insert:n.doc.slice(s,l).append(n.doc.slice(o,s))},range:E.cursor(l)}});return t.changes.empty?!1:(e(n.update(t,{scrollIntoView:!0,userEvent:"move.character"})),!0)};function Ks(n){let e=[],t=-1;for(let i of n.selection.ranges){let s=n.doc.lineAt(i.from),r=n.doc.lineAt(i.to);if(!i.empty&&i.to==r.from&&(r=n.doc.lineAt(i.to-1)),t>=s.number){let o=e[e.length-1];o.to=r.to,o.ranges.push(i)}else e.push({from:s.from,to:r.to,ranges:[i]});t=r.number+1}return e}function bu(n,e,t){if(n.readOnly)return!1;let i=[],s=[];for(let r of Ks(n)){if(t?r.to==n.doc.length:r.from==0)continue;let o=n.doc.lineAt(t?r.to+1:r.from-1),l=o.length+1;if(t){i.push({from:r.to,to:o.to},{from:r.from,insert:o.text+n.lineBreak});for(let a of r.ranges)s.push(E.range(Math.min(n.doc.length,a.anchor+l),Math.min(n.doc.length,a.head+l)))}else{i.push({from:o.from,to:r.from},{from:r.to,insert:n.lineBreak+o.text});for(let a of r.ranges)s.push(E.range(a.anchor-l,a.head-l))}}return i.length?(e(n.update({changes:i,scrollIntoView:!0,selection:E.create(s,n.selection.mainIndex),userEvent:"move.line"})),!0):!1}const ob=({state:n,dispatch:e})=>bu(n,e,!1),lb=({state:n,dispatch:e})=>bu(n,e,!0);function xu(n,e,t){if(n.readOnly)return!1;let i=[];for(let s of Ks(n))t?i.push({from:s.from,insert:n.doc.slice(s.from,s.to)+n.lineBreak}):i.push({from:s.to,insert:n.lineBreak+n.doc.slice(s.from,s.to)});return e(n.update({changes:i,scrollIntoView:!0,userEvent:"input.copyline"})),!0}const ab=({state:n,dispatch:e})=>xu(n,e,!1),hb=({state:n,dispatch:e})=>xu(n,e,!0),cb=n=>{if(n.state.readOnly)return!1;let{state:e}=n,t=e.changes(Ks(e).map(({from:s,to:r})=>(s>0?s--:r<e.doc.length&&r++,{from:s,to:r}))),i=Bi(e.selection,s=>{let r;if(n.lineWrapping){let o=n.lineBlockAt(s.head),l=n.coordsAtPos(s.head,s.assoc||1);l&&(r=o.bottom+n.documentTop-l.bottom+n.defaultLineHeight/2)}return n.moveVertically(s,!0,r)}).map(t);return n.dispatch({changes:t,selection:i,scrollIntoView:!0,userEvent:"delete.line"}),!0};function fb(n,e){if(/\(\)|\[\]|\{\}/.test(n.sliceDoc(e-1,e+1)))return{from:e,to:e};let t=ge(n).resolveInner(e),i=t.childBefore(e),s=t.childAfter(e),r;return i&&s&&i.to<=e&&s.from>=e&&(r=i.type.prop(K.closedBy))&&r.indexOf(s.name)>-1&&n.doc.lineAt(i.to).from==n.doc.lineAt(s.from).from&&!/\S/.test(n.sliceDoc(i.to,s.from))?{from:i.to,to:s.from}:null}const mh=vu(!1),ub=vu(!0);function vu(n){return({state:e,dispatch:t})=>{if(e.readOnly)return!1;let i=e.changeByRange(s=>{let{from:r,to:o}=s,l=e.doc.lineAt(r),a=!n&&r==o&&fb(e,r);n&&(r=o=(o<=l.to?l:e.doc.lineAt(o)).to);let h=new $s(e,{simulateBreak:r,simulateDoubleBreak:!!a}),c=cl(h,r);for(c==null&&(c=Ni(/^\s*/.exec(e.doc.lineAt(r).text)[0],e.tabSize));o<l.to&&/\s/.test(l.text[o-l.from]);)o++;a?{from:r,to:o}=a:r>l.from&&r<l.from+100&&!/\S/.test(l.text.slice(0,r))&&(r=l.from);let f=["",fn(e,c)];return a&&f.push(fn(e,h.lineIndent(l.from,-1))),{changes:{from:r,to:o,insert:G.of(f)},range:E.cursor(r+1+f[1].length)}});return t(e.update(i,{scrollIntoView:!0,userEvent:"input"})),!0}}function yl(n,e){let t=-1;return n.changeByRange(i=>{let s=[];for(let o=i.from;o<=i.to;){let l=n.doc.lineAt(o);l.number>t&&(i.empty||i.to>l.from)&&(e(l,s,i),t=l.number),o=l.to+1}let r=n.changes(s);return{changes:s,range:E.range(r.mapPos(i.anchor,1),r.mapPos(i.head,1))}})}const db=({state:n,dispatch:e})=>{if(n.readOnly)return!1;let t=Object.create(null),i=new $s(n,{overrideIndentation:r=>{let o=t[r];return o??-1}}),s=yl(n,(r,o,l)=>{let a=cl(i,r.from);if(a==null)return;/\S/.test(r.text)||(a=0);let h=/^\s*/.exec(r.text)[0],c=fn(n,a);(h!=c||l.from<r.from+h.length)&&(t[r.from]=a,o.push({from:r.from,to:r.from+h.length,insert:c}))});return s.changes.empty||e(n.update(s,{userEvent:"indent"})),!0},wu=({state:n,dispatch:e})=>n.readOnly?!1:(e(n.update(yl(n,(t,i)=>{i.push({from:t.from,insert:n.facet(_s)})}),{userEvent:"input.indent"})),!0),Su=({state:n,dispatch:e})=>n.readOnly?!1:(e(n.update(yl(n,(t,i)=>{let s=/^\s*/.exec(t.text)[0];if(!s)return;let r=Ni(s,n.tabSize),o=0,l=fn(n,Math.max(0,r-Cs(n)));for(;o<s.length&&o<l.length&&s.charCodeAt(o)==l.charCodeAt(o);)o++;i.push({from:t.from+o,to:t.from+s.length,insert:l.slice(o)})}),{userEvent:"delete.dedent"})),!0),pb=n=>(n.setTabFocusMode(),!0),mb=[{key:"Ctrl-b",run:Jf,shift:lu,preventDefault:!0},{key:"Ctrl-f",run:Zf,shift:au},{key:"Ctrl-p",run:iu,shift:fu},{key:"Ctrl-n",run:nu,shift:uu},{key:"Ctrl-a",run:Ly,shift:Gy},{key:"Ctrl-e",run:Fy,shift:Xy},{key:"Ctrl-d",run:mu},{key:"Ctrl-h",run:Bo},{key:"Ctrl-k",run:tb},{key:"Ctrl-Alt-h",run:yu},{key:"Ctrl-o",run:sb},{key:"Ctrl-t",run:rb},{key:"Ctrl-v",run:Ro}],gb=[{key:"ArrowLeft",run:Jf,shift:lu,preventDefault:!0},{key:"Mod-ArrowLeft",mac:"Alt-ArrowLeft",run:Ty,shift:_y,preventDefault:!0},{mac:"Cmd-ArrowLeft",run:By,shift:Ky,preventDefault:!0},{key:"ArrowRight",run:Zf,shift:au,preventDefault:!0},{key:"Mod-ArrowRight",mac:"Alt-ArrowRight",run:My,shift:$y,preventDefault:!0},{mac:"Cmd-ArrowRight",run:Iy,shift:Uy,preventDefault:!0},{key:"ArrowUp",run:iu,shift:fu,preventDefault:!0},{mac:"Cmd-ArrowUp",run:fh,shift:dh},{mac:"Ctrl-ArrowUp",run:ah,shift:hh},{key:"ArrowDown",run:nu,shift:uu,preventDefault:!0},{mac:"Cmd-ArrowDown",run:uh,shift:ph},{mac:"Ctrl-ArrowDown",run:Ro,shift:ch},{key:"PageUp",run:ah,shift:hh},{key:"PageDown",run:Ro,shift:ch},{key:"Home",run:Ry,shift:jy,preventDefault:!0},{key:"Mod-Home",run:fh,shift:dh},{key:"End",run:Ny,shift:zy,preventDefault:!0},{key:"Mod-End",run:uh,shift:ph},{key:"Enter",run:mh,shift:mh},{key:"Mod-a",run:Qy},{key:"Backspace",run:Bo,shift:Bo},{key:"Delete",run:mu},{key:"Mod-Backspace",mac:"Alt-Backspace",run:yu},{key:"Mod-Delete",mac:"Alt-Delete",run:eb},{mac:"Mod-Backspace",run:ib},{mac:"Mod-Delete",run:nb}].concat(mb.map(n=>({mac:n.key,run:n.run,shift:n.shift}))),Ou=[{key:"Alt-ArrowLeft",mac:"Ctrl-ArrowLeft",run:Dy,shift:qy},{key:"Alt-ArrowRight",mac:"Ctrl-ArrowRight",run:Py,shift:Hy},{key:"Alt-ArrowUp",run:ob},{key:"Shift-Alt-ArrowUp",run:ab},{key:"Alt-ArrowDown",run:lb},{key:"Shift-Alt-ArrowDown",run:hb},{key:"Escape",run:Zy},{key:"Mod-Enter",run:ub},{key:"Alt-l",mac:"Ctrl-l",run:Yy},{key:"Mod-i",run:Jy,preventDefault:!0},{key:"Mod-[",run:Su},{key:"Mod-]",run:wu},{key:"Mod-Alt-\\",run:db},{key:"Shift-Mod-k",run:cb},{key:"Shift-Mod-\\",run:Wy},{key:"Mod-/",run:ay},{key:"Alt-A",run:cy},{key:"Ctrl-m",mac:"Shift-Alt-m",run:pb}].concat(gb),ku={key:"Tab",run:wu,shift:Su};class gh{constructor(e,t,i){this.from=e,this.to=t,this.diagnostic=i}}class Qt{constructor(e,t,i){this.diagnostics=e,this.panel=t,this.selected=i}static init(e,t,i){let s=i.facet(mt).markerFilter;s&&(e=s(e,i));let r=e.slice().sort((c,f)=>c.from-f.from||c.to-f.to),o=new At,l=[],a=0;for(let c=0;;){let f=c==r.length?null:r[c];if(!f&&!l.length)break;let u,d;for(l.length?(u=a,d=l.reduce((x,S)=>Math.min(x,S.to),f&&f.from>u?f.from:1e8)):(u=f.from,d=f.to,l.push(f),c++);c<r.length;){let x=r[c];if(x.from==u&&(x.to>x.from||x.to==u))l.push(x),c++,d=Math.min(x.to,d);else{d=Math.min(x.from,d);break}}let m=Pu(l);if(l.some(x=>x.from==x.to||x.from==x.to-1&&i.doc.lineAt(x.from).to==x.from))o.add(u,u,W.widget({widget:new Ab(m),diagnostics:l.slice()}));else{let x=l.reduce((S,y)=>y.markClass?S+" "+y.markClass:S,"");o.add(u,d,W.mark({class:"cm-lintRange cm-lintRange-"+m+x,diagnostics:l.slice(),inclusiveEnd:l.some(S=>S.to>d)}))}a=d;for(let x=0;x<l.length;x++)l[x].to<=a&&l.splice(x--,1)}let h=o.finish();return new Qt(h,t,Di(h))}}function Di(n,e=null,t=0){let i=null;return n.between(t,1e9,(s,r,{spec:o})=>{if(!(e&&o.diagnostics.indexOf(e)<0))if(!i)i=new gh(s,r,e||o.diagnostics[0]);else{if(o.diagnostics.indexOf(i.diagnostic)<0)return!1;i=new gh(i.from,r,i.diagnostic)}}),i}function Cu(n,e){let t=e.pos,i=e.end||t,s=n.state.facet(mt).hideOn(n,t,i);if(s!=null)return s;let r=n.startState.doc.lineAt(e.pos);return!!(n.effects.some(o=>o.is(Us))||n.changes.touchesRange(r.from,Math.max(r.to,i)))}function Au(n,e){return n.field(He,!1)?e:e.concat(_.appendConfig.of(Bu))}function yb(n,e){return{effects:Au(n,[Us.of(e)])}}const Us=_.define(),bl=_.define(),Tu=_.define(),He=ue.define({create(){return new Qt(W.none,null,null)},update(n,e){if(e.docChanged&&n.diagnostics.size){let t=n.diagnostics.map(e.changes),i=null,s=n.panel;if(n.selected){let r=e.changes.mapPos(n.selected.from,1);i=Di(t,n.selected.diagnostic,r)||Di(t,null,r)}!t.size&&s&&e.state.facet(mt).autoPanel&&(s=null),n=new Qt(t,s,i)}for(let t of e.effects)if(t.is(Us)){let i=e.state.facet(mt).autoPanel?t.value.length?un.open:null:n.panel;n=Qt.init(t.value,i,e.state)}else t.is(bl)?n=new Qt(n.diagnostics,t.value?un.open:null,n.selected):t.is(Tu)&&(n=new Qt(n.diagnostics,n.panel,t.value));return n},provide:n=>[ln.from(n,e=>e.panel),R.decorations.from(n,e=>e.diagnostics)]}),bb=W.mark({class:"cm-lintRange cm-lintRange-active"});function xb(n,e,t){let{diagnostics:i}=n.state.field(He),s,r=-1,o=-1;i.between(e-(t<0?1:0),e+(t>0?1:0),(a,h,{spec:c})=>{if(e>=a&&e<=h&&(a==h||(e>a||t>0)&&(e<h||t<0)))return s=c.diagnostics,r=a,o=h,!1});let l=n.state.facet(mt).tooltipFilter;return s&&l&&(s=l(s,n.state)),s?{pos:r,end:o,above:n.state.doc.lineAt(r).to<o,create(){return{dom:Mu(n,s)}}}:null}function Mu(n,e){return Q("ul",{class:"cm-tooltip-lint"},e.map(t=>Du(n,t,!1)))}const vb=n=>{let e=n.state.field(He,!1);(!e||!e.panel)&&n.dispatch({effects:Au(n.state,[bl.of(!0)])});let t=on(n,un.open);return t&&t.dom.querySelector(".cm-panel-lint ul").focus(),!0},yh=n=>{let e=n.state.field(He,!1);return!e||!e.panel?!1:(n.dispatch({effects:bl.of(!1)}),!0)},wb=n=>{let e=n.state.field(He,!1);if(!e)return!1;let t=n.state.selection.main,i=e.diagnostics.iter(t.to+1);return!i.value&&(i=e.diagnostics.iter(0),!i.value||i.from==t.from&&i.to==t.to)?!1:(n.dispatch({selection:{anchor:i.from,head:i.to},scrollIntoView:!0}),!0)},Sb=[{key:"Mod-Shift-m",run:vb,preventDefault:!0},{key:"F8",run:wb}],Ob=ne.fromClass(class{constructor(n){this.view=n,this.timeout=-1,this.set=!0;let{delay:e}=n.state.facet(mt);this.lintTime=Date.now()+e,this.run=this.run.bind(this),this.timeout=setTimeout(this.run,e)}run(){clearTimeout(this.timeout);let n=Date.now();if(n<this.lintTime-10)this.timeout=setTimeout(this.run,this.lintTime-n);else{this.set=!1;let{state:e}=this.view,{sources:t}=e.facet(mt);t.length&&kb(t.map(i=>Promise.resolve(i(this.view))),i=>{this.view.state.doc==e.doc&&this.view.dispatch(yb(this.view.state,i.reduce((s,r)=>s.concat(r))))},i=>{De(this.view.state,i)})}}update(n){let e=n.state.facet(mt);(n.docChanged||e!=n.startState.facet(mt)||e.needsRefresh&&e.needsRefresh(n))&&(this.lintTime=Date.now()+e.delay,this.set||(this.set=!0,this.timeout=setTimeout(this.run,e.delay)))}force(){this.set&&(this.lintTime=Date.now(),this.run())}destroy(){clearTimeout(this.timeout)}});function kb(n,e,t){let i=[],s=-1;for(let r of n)r.then(o=>{i.push(o),clearTimeout(s),i.length==n.length?e(i):s=setTimeout(()=>e(i),200)},t)}const mt=F.define({combine(n){return Object.assign({sources:n.map(e=>e.source).filter(e=>e!=null)},nt(n.map(e=>e.config),{delay:750,markerFilter:null,tooltipFilter:null,needsRefresh:null,hideOn:()=>null},{needsRefresh:(e,t)=>e?t?i=>e(i)||t(i):e:t}))}});function Cb(n,e={}){return[mt.of({source:n,config:e}),Ob,Bu]}function Eu(n){let e=[];if(n)e:for(let{name:t}of n){for(let i=0;i<t.length;i++){let s=t[i];if(/[a-zA-Z]/.test(s)&&!e.some(r=>r.toLowerCase()==s.toLowerCase())){e.push(s);continue e}}e.push("")}return e}function Du(n,e,t){var i;let s=t?Eu(e.actions):[];return Q("li",{class:"cm-diagnostic cm-diagnostic-"+e.severity},Q("span",{class:"cm-diagnosticText"},e.renderMessage?e.renderMessage(n):e.message),(i=e.actions)===null||i===void 0?void 0:i.map((r,o)=>{let l=!1,a=u=>{if(u.preventDefault(),l)return;l=!0;let d=Di(n.state.field(He).diagnostics,e);d&&r.apply(n,d.from,d.to)},{name:h}=r,c=s[o]?h.indexOf(s[o]):-1,f=c<0?h:[h.slice(0,c),Q("u",h.slice(c,c+1)),h.slice(c+1)];return Q("button",{type:"button",class:"cm-diagnosticAction",onclick:a,onmousedown:a,"aria-label":` Action: ${h}${c<0?"":` (access key "${s[o]})"`}.`},f)}),e.source&&Q("div",{class:"cm-diagnosticSource"},e.source))}class Ab extends Et{constructor(e){super(),this.sev=e}eq(e){return e.sev==this.sev}toDOM(){return Q("span",{class:"cm-lintPoint cm-lintPoint-"+this.sev})}}class bh{constructor(e,t){this.diagnostic=t,this.id="item_"+Math.floor(Math.random()*4294967295).toString(16),this.dom=Du(e,t,!0),this.dom.id=this.id,this.dom.setAttribute("role","option")}}class un{constructor(e){this.view=e,this.items=[];let t=s=>{if(s.keyCode==27)yh(this.view),this.view.focus();else if(s.keyCode==38||s.keyCode==33)this.moveSelection((this.selectedIndex-1+this.items.length)%this.items.length);else if(s.keyCode==40||s.keyCode==34)this.moveSelection((this.selectedIndex+1)%this.items.length);else if(s.keyCode==36)this.moveSelection(0);else if(s.keyCode==35)this.moveSelection(this.items.length-1);else if(s.keyCode==13)this.view.focus();else if(s.keyCode>=65&&s.keyCode<=90&&this.selectedIndex>=0){let{diagnostic:r}=this.items[this.selectedIndex],o=Eu(r.actions);for(let l=0;l<o.length;l++)if(o[l].toUpperCase().charCodeAt(0)==s.keyCode){let a=Di(this.view.state.field(He).diagnostics,r);a&&r.actions[l].apply(e,a.from,a.to)}}else return;s.preventDefault()},i=s=>{for(let r=0;r<this.items.length;r++)this.items[r].dom.contains(s.target)&&this.moveSelection(r)};this.list=Q("ul",{tabIndex:0,role:"listbox","aria-label":this.view.state.phrase("Diagnostics"),onkeydown:t,onclick:i}),this.dom=Q("div",{class:"cm-panel-lint"},this.list,Q("button",{type:"button",name:"close","aria-label":this.view.state.phrase("close"),onclick:()=>yh(this.view)},"×")),this.update()}get selectedIndex(){let e=this.view.state.field(He).selected;if(!e)return-1;for(let t=0;t<this.items.length;t++)if(this.items[t].diagnostic==e.diagnostic)return t;return-1}update(){let{diagnostics:e,selected:t}=this.view.state.field(He),i=0,s=!1,r=null,o=new Set;for(e.between(0,this.view.state.doc.length,(l,a,{spec:h})=>{for(let c of h.diagnostics){if(o.has(c))continue;o.add(c);let f=-1,u;for(let d=i;d<this.items.length;d++)if(this.items[d].diagnostic==c){f=d;break}f<0?(u=new bh(this.view,c),this.items.splice(i,0,u),s=!0):(u=this.items[f],f>i&&(this.items.splice(i,f-i),s=!0)),t&&u.diagnostic==t.diagnostic?u.dom.hasAttribute("aria-selected")||(u.dom.setAttribute("aria-selected","true"),r=u):u.dom.hasAttribute("aria-selected")&&u.dom.removeAttribute("aria-selected"),i++}});i<this.items.length&&!(this.items.length==1&&this.items[0].diagnostic.from<0);)s=!0,this.items.pop();this.items.length==0&&(this.items.push(new bh(this.view,{from:-1,to:-1,severity:"info",message:this.view.state.phrase("No diagnostics")})),s=!0),r?(this.list.setAttribute("aria-activedescendant",r.id),this.view.requestMeasure({key:this,read:()=>({sel:r.dom.getBoundingClientRect(),panel:this.list.getBoundingClientRect()}),write:({sel:l,panel:a})=>{let h=a.height/this.list.offsetHeight;l.top<a.top?this.list.scrollTop-=(a.top-l.top)/h:l.bottom>a.bottom&&(this.list.scrollTop+=(l.bottom-a.bottom)/h)}})):this.selectedIndex<0&&this.list.removeAttribute("aria-activedescendant"),s&&this.sync()}sync(){let e=this.list.firstChild;function t(){let i=e;e=i.nextSibling,i.remove()}for(let i of this.items)if(i.dom.parentNode==this.list){for(;e!=i.dom;)t();e=i.dom.nextSibling}else this.list.insertBefore(i.dom,e);for(;e;)t()}moveSelection(e){if(this.selectedIndex<0)return;let t=this.view.state.field(He),i=Di(t.diagnostics,this.items[e].diagnostic);i&&this.view.dispatch({selection:{anchor:i.from,head:i.to},scrollIntoView:!0,effects:Tu.of(i)})}static open(e){return new un(e)}}function as(n,e='viewBox="0 0 40 40"'){return`url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" ${e}>${encodeURIComponent(n)}</svg>')`}function jn(n){return as(`<path d="m0 2.5 l2 -1.5 l1 0 l2 1.5 l1 0" stroke="${n}" fill="none" stroke-width=".7"/>`,'width="6" height="3"')}const Tb=R.baseTheme({".cm-diagnostic":{padding:"3px 6px 3px 8px",marginLeft:"-1px",display:"block",whiteSpace:"pre-wrap"},".cm-diagnostic-error":{borderLeft:"5px solid #d11"},".cm-diagnostic-warning":{borderLeft:"5px solid orange"},".cm-diagnostic-info":{borderLeft:"5px solid #999"},".cm-diagnostic-hint":{borderLeft:"5px solid #66d"},".cm-diagnosticAction":{font:"inherit",border:"none",padding:"2px 4px",backgroundColor:"#444",color:"white",borderRadius:"3px",marginLeft:"8px",cursor:"pointer"},".cm-diagnosticSource":{fontSize:"70%",opacity:.7},".cm-lintRange":{backgroundPosition:"left bottom",backgroundRepeat:"repeat-x",paddingBottom:"0.7px"},".cm-lintRange-error":{backgroundImage:jn("#d11")},".cm-lintRange-warning":{backgroundImage:jn("orange")},".cm-lintRange-info":{backgroundImage:jn("#999")},".cm-lintRange-hint":{backgroundImage:jn("#66d")},".cm-lintRange-active":{backgroundColor:"#ffdd9980"},".cm-tooltip-lint":{padding:0,margin:0},".cm-lintPoint":{position:"relative","&:after":{content:'""',position:"absolute",bottom:0,left:"-2px",borderLeft:"3px solid transparent",borderRight:"3px solid transparent",borderBottom:"4px solid #d11"}},".cm-lintPoint-warning":{"&:after":{borderBottomColor:"orange"}},".cm-lintPoint-info":{"&:after":{borderBottomColor:"#999"}},".cm-lintPoint-hint":{"&:after":{borderBottomColor:"#66d"}},".cm-panel.cm-panel-lint":{position:"relative","& ul":{maxHeight:"100px",overflowY:"auto","& [aria-selected]":{backgroundColor:"#ddd","& u":{textDecoration:"underline"}},"&:focus [aria-selected]":{background_fallback:"#bdf",backgroundColor:"Highlight",color_fallback:"white",color:"HighlightText"},"& u":{textDecoration:"none"},padding:0,margin:0},"& [name=close]":{position:"absolute",top:"0",right:"2px",background:"inherit",border:"none",font:"inherit",padding:0,margin:0}}});function Mb(n){return n=="error"?4:n=="warning"?3:n=="info"?2:1}function Pu(n){let e="hint",t=1;for(let i of n){let s=Mb(i.severity);s>t&&(t=s,e=i.severity)}return e}class Nu extends bt{constructor(e){super(),this.diagnostics=e,this.severity=Pu(e)}toDOM(e){let t=document.createElement("div");t.className="cm-lint-marker cm-lint-marker-"+this.severity;let i=this.diagnostics,s=e.state.facet(Gs).tooltipFilter;return s&&(i=s(i,e.state)),i.length&&(t.onmouseover=()=>Db(e,t,i)),t}}function Eb(n,e){let t=i=>{let s=e.getBoundingClientRect();if(!(i.clientX>s.left-10&&i.clientX<s.right+10&&i.clientY>s.top-10&&i.clientY<s.bottom+10)){for(let r=i.target;r;r=r.parentNode)if(r.nodeType==1&&r.classList.contains("cm-tooltip-lint"))return;window.removeEventListener("mousemove",t),n.state.field(Ru)&&n.dispatch({effects:xl.of(null)})}};window.addEventListener("mousemove",t)}function Db(n,e,t){function i(){let o=n.elementAtHeight(e.getBoundingClientRect().top+5-n.documentTop);n.coordsAtPos(o.from)&&n.dispatch({effects:xl.of({pos:o.from,above:!1,clip:!1,create(){return{dom:Mu(n,t),getCoords:()=>e.getBoundingClientRect()}}})}),e.onmouseout=e.onmousemove=null,Eb(n,e)}let{hoverTime:s}=n.state.facet(Gs),r=setTimeout(i,s);e.onmouseout=()=>{clearTimeout(r),e.onmouseout=e.onmousemove=null},e.onmousemove=()=>{clearTimeout(r),r=setTimeout(i,s)}}function Pb(n,e){let t=Object.create(null);for(let s of e){let r=n.lineAt(s.from);(t[r.from]||(t[r.from]=[])).push(s)}let i=[];for(let s in t)i.push(new Nu(t[s]).range(+s));return U.of(i,!0)}const Nb=xf({class:"cm-gutter-lint",markers:n=>n.state.field(Io),widgetMarker:(n,e,t)=>{let i=[];return n.state.field(Io).between(t.from,t.to,(s,r,o)=>{s>t.from&&s<t.to&&i.push(...o.diagnostics)}),i.length?new Nu(i):null}}),Io=ue.define({create(){return U.empty},update(n,e){n=n.map(e.changes);let t=e.state.facet(Gs).markerFilter;for(let i of e.effects)if(i.is(Us)){let s=i.value;t&&(s=t(s||[],e.state)),n=Pb(e.state.doc,s.slice(0))}return n}}),xl=_.define(),Ru=ue.define({create(){return null},update(n,e){return n&&e.docChanged&&(n=Cu(e,n)?null:Object.assign(Object.assign({},n),{pos:e.changes.mapPos(n.pos)})),e.effects.reduce((t,i)=>i.is(xl)?i.value:t,n)},provide:n=>Vs.from(n)}),Rb=R.baseTheme({".cm-gutter-lint":{width:"1.4em","& .cm-gutterElement":{padding:".2em"}},".cm-lint-marker":{width:"1em",height:"1em"},".cm-lint-marker-info":{content:as('<path fill="#aaf" stroke="#77e" stroke-width="6" stroke-linejoin="round" d="M5 5L35 5L35 35L5 35Z"/>')},".cm-lint-marker-warning":{content:as('<path fill="#fe8" stroke="#fd7" stroke-width="6" stroke-linejoin="round" d="M20 6L37 35L3 35Z"/>')},".cm-lint-marker-error":{content:as('<circle cx="20" cy="20" r="15" fill="#f87" stroke="#f43" stroke-width="6"/>')}}),Bu=[He,R.decorations.compute([He],n=>{let{selected:e,panel:t}=n.field(He);return!e||!t||e.from==e.to?W.none:W.set([bb.range(e.from,e.to)])}),cg(xb,{hideOn:Cu}),Tb],Gs=F.define({combine(n){return nt(n,{hoverTime:300,markerFilter:null,tooltipFilter:null})}});function Bb(n={}){return[Gs.of(n),Io,Nb,Rb,Ru]}var Cr={},xh;function Ib(){return xh||(xh=1,function(n){(function(e){e.parser=function(b,p){return new i(b,p)},e.SAXParser=i,e.SAXStream=c,e.createStream=h,e.MAX_BUFFER_LENGTH=64*1024;var t=["comment","sgmlDecl","textNode","tagName","doctype","procInstName","procInstBody","entity","attribName","attribValue","cdata","script"];e.EVENTS=["text","processinginstruction","sgmldeclaration","doctype","comment","opentagstart","attribute","opentag","closetag","opencdata","cdata","closecdata","error","end","ready","script","opennamespace","closenamespace"];function i(b,p){if(!(this instanceof i))return new i(b,p);var D=this;r(D),D.q=D.c="",D.bufferCheckPosition=e.MAX_BUFFER_LENGTH,D.opt=p||{},D.opt.lowercase=D.opt.lowercase||D.opt.lowercasetags,D.looseCase=D.opt.lowercase?"toLowerCase":"toUpperCase",D.tags=[],D.closed=D.closedRoot=D.sawRoot=!1,D.tag=D.error=null,D.strict=!!b,D.noscript=!!(b||D.opt.noscript),D.state=w.BEGIN,D.strictEntities=D.opt.strictEntities,D.ENTITIES=D.strictEntities?Object.create(e.XML_ENTITIES):Object.create(e.ENTITIES),D.attribList=[],D.opt.xmlns&&(D.ns=Object.create(x)),D.opt.unquotedAttributeValues===void 0&&(D.opt.unquotedAttributeValues=!b),D.trackPosition=D.opt.position!==!1,D.trackPosition&&(D.position=D.line=D.column=0),V(D,"onready")}Object.create||(Object.create=function(b){function p(){}p.prototype=b;var D=new p;return D}),Object.keys||(Object.keys=function(b){var p=[];for(var D in b)b.hasOwnProperty(D)&&p.push(D);return p});function s(b){for(var p=Math.max(e.MAX_BUFFER_LENGTH,10),D=0,C=0,X=t.length;C<X;C++){var ae=b[t[C]].length;if(ae>p)switch(t[C]){case"textNode":z(b);break;case"cdata":I(b,"oncdata",b.cdata),b.cdata="";break;case"script":I(b,"onscript",b.script),b.script="";break;default:q(b,"Max buffer length exceeded: "+t[C])}D=Math.max(D,ae)}var he=e.MAX_BUFFER_LENGTH-D;b.bufferCheckPosition=he+b.position}function r(b){for(var p=0,D=t.length;p<D;p++)b[t[p]]=""}function o(b){z(b),b.cdata!==""&&(I(b,"oncdata",b.cdata),b.cdata=""),b.script!==""&&(I(b,"onscript",b.script),b.script="")}i.prototype={end:function(){de(this)},write:ai,resume:function(){return this.error=null,this},close:function(){return this.write(null)},flush:function(){o(this)}};var l;try{l=Pl.Stream}catch{l=function(){}}l||(l=function(){});var a=e.EVENTS.filter(function(b){return b!=="error"&&b!=="end"});function h(b,p){return new c(b,p)}function c(b,p){if(!(this instanceof c))return new c(b,p);l.apply(this),this._parser=new i(b,p),this.writable=!0,this.readable=!0;var D=this;this._parser.onend=function(){D.emit("end")},this._parser.onerror=function(C){D.emit("error",C),D._parser.error=null},this._decoder=null,a.forEach(function(C){Object.defineProperty(D,"on"+C,{get:function(){return D._parser["on"+C]},set:function(X){if(!X)return D.removeAllListeners(C),D._parser["on"+C]=X,X;D.on(C,X)},enumerable:!0,configurable:!1})})}c.prototype=Object.create(l.prototype,{constructor:{value:c}}),c.prototype.write=function(b){if(typeof Buffer=="function"&&typeof Buffer.isBuffer=="function"&&Buffer.isBuffer(b)){if(!this._decoder){var p=Pl.StringDecoder;this._decoder=new p("utf8")}b=this._decoder.write(b)}return this._parser.write(b.toString()),this.emit("data",b),!0},c.prototype.end=function(b){return b&&b.length&&this.write(b),this._parser.end(),!0},c.prototype.on=function(b,p){var D=this;return!D._parser["on"+b]&&a.indexOf(b)!==-1&&(D._parser["on"+b]=function(){var C=arguments.length===1?[arguments[0]]:Array.apply(null,arguments);C.splice(0,0,b),D.emit.apply(D,C)}),l.prototype.on.call(D,b,p)};var f="[CDATA[",u="DOCTYPE",d="http://www.w3.org/XML/1998/namespace",m="http://www.w3.org/2000/xmlns/",x={xml:d,xmlns:m},S=/[:_A-Za-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD]/,y=/[:_A-Za-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\u00B7\u0300-\u036F\u203F-\u2040.\d-]/,O=/[#:_A-Za-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD]/,v=/[#:_A-Za-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\u00B7\u0300-\u036F\u203F-\u2040.\d-]/;function g(b){return b===" "||b===`
`||b==="\r"||b==="	"}function k(b){return b==='"'||b==="'"}function T(b){return b===">"||g(b)}function M(b,p){return b.test(p)}function N(b,p){return!M(b,p)}var w=0;e.STATE={BEGIN:w++,BEGIN_WHITESPACE:w++,TEXT:w++,TEXT_ENTITY:w++,OPEN_WAKA:w++,SGML_DECL:w++,SGML_DECL_QUOTED:w++,DOCTYPE:w++,DOCTYPE_QUOTED:w++,DOCTYPE_DTD:w++,DOCTYPE_DTD_QUOTED:w++,COMMENT_STARTING:w++,COMMENT:w++,COMMENT_ENDING:w++,COMMENT_ENDED:w++,CDATA:w++,CDATA_ENDING:w++,CDATA_ENDING_2:w++,PROC_INST:w++,PROC_INST_BODY:w++,PROC_INST_ENDING:w++,OPEN_TAG:w++,OPEN_TAG_SLASH:w++,ATTRIB:w++,ATTRIB_NAME:w++,ATTRIB_NAME_SAW_WHITE:w++,ATTRIB_VALUE:w++,ATTRIB_VALUE_QUOTED:w++,ATTRIB_VALUE_CLOSED:w++,ATTRIB_VALUE_UNQUOTED:w++,ATTRIB_VALUE_ENTITY_Q:w++,ATTRIB_VALUE_ENTITY_U:w++,CLOSE_TAG:w++,CLOSE_TAG_SAW_WHITE:w++,SCRIPT:w++,SCRIPT_ENDING:w++},e.XML_ENTITIES={amp:"&",gt:">",lt:"<",quot:'"',apos:"'"},e.ENTITIES={amp:"&",gt:">",lt:"<",quot:'"',apos:"'",AElig:198,Aacute:193,Acirc:194,Agrave:192,Aring:197,Atilde:195,Auml:196,Ccedil:199,ETH:208,Eacute:201,Ecirc:202,Egrave:200,Euml:203,Iacute:205,Icirc:206,Igrave:204,Iuml:207,Ntilde:209,Oacute:211,Ocirc:212,Ograve:210,Oslash:216,Otilde:213,Ouml:214,THORN:222,Uacute:218,Ucirc:219,Ugrave:217,Uuml:220,Yacute:221,aacute:225,acirc:226,aelig:230,agrave:224,aring:229,atilde:227,auml:228,ccedil:231,eacute:233,ecirc:234,egrave:232,eth:240,euml:235,iacute:237,icirc:238,igrave:236,iuml:239,ntilde:241,oacute:243,ocirc:244,ograve:242,oslash:248,otilde:245,ouml:246,szlig:223,thorn:254,uacute:250,ucirc:251,ugrave:249,uuml:252,yacute:253,yuml:255,copy:169,reg:174,nbsp:160,iexcl:161,cent:162,pound:163,curren:164,yen:165,brvbar:166,sect:167,uml:168,ordf:170,laquo:171,not:172,shy:173,macr:175,deg:176,plusmn:177,sup1:185,sup2:178,sup3:179,acute:180,micro:181,para:182,middot:183,cedil:184,ordm:186,raquo:187,frac14:188,frac12:189,frac34:190,iquest:191,times:215,divide:247,OElig:338,oelig:339,Scaron:352,scaron:353,Yuml:376,fnof:402,circ:710,tilde:732,Alpha:913,Beta:914,Gamma:915,Delta:916,Epsilon:917,Zeta:918,Eta:919,Theta:920,Iota:921,Kappa:922,Lambda:923,Mu:924,Nu:925,Xi:926,Omicron:927,Pi:928,Rho:929,Sigma:931,Tau:932,Upsilon:933,Phi:934,Chi:935,Psi:936,Omega:937,alpha:945,beta:946,gamma:947,delta:948,epsilon:949,zeta:950,eta:951,theta:952,iota:953,kappa:954,lambda:955,mu:956,nu:957,xi:958,omicron:959,pi:960,rho:961,sigmaf:962,sigma:963,tau:964,upsilon:965,phi:966,chi:967,psi:968,omega:969,thetasym:977,upsih:978,piv:982,ensp:8194,emsp:8195,thinsp:8201,zwnj:8204,zwj:8205,lrm:8206,rlm:8207,ndash:8211,mdash:8212,lsquo:8216,rsquo:8217,sbquo:8218,ldquo:8220,rdquo:8221,bdquo:8222,dagger:8224,Dagger:8225,bull:8226,hellip:8230,permil:8240,prime:8242,Prime:8243,lsaquo:8249,rsaquo:8250,oline:8254,frasl:8260,euro:8364,image:8465,weierp:8472,real:8476,trade:8482,alefsym:8501,larr:8592,uarr:8593,rarr:8594,darr:8595,harr:8596,crarr:8629,lArr:8656,uArr:8657,rArr:8658,dArr:8659,hArr:8660,forall:8704,part:8706,exist:8707,empty:8709,nabla:8711,isin:8712,notin:8713,ni:8715,prod:8719,sum:8721,minus:8722,lowast:8727,radic:8730,prop:8733,infin:8734,ang:8736,and:8743,or:8744,cap:8745,cup:8746,int:8747,there4:8756,sim:8764,cong:8773,asymp:8776,ne:8800,equiv:8801,le:8804,ge:8805,sub:8834,sup:8835,nsub:8836,sube:8838,supe:8839,oplus:8853,otimes:8855,perp:8869,sdot:8901,lceil:8968,rceil:8969,lfloor:8970,rfloor:8971,lang:9001,rang:9002,loz:9674,spades:9824,clubs:9827,hearts:9829,diams:9830},Object.keys(e.ENTITIES).forEach(function(b){var p=e.ENTITIES[b],D=typeof p=="number"?String.fromCharCode(p):p;e.ENTITIES[b]=D});for(var P in e.STATE)e.STATE[e.STATE[P]]=P;w=e.STATE;function V(b,p,D){b[p]&&b[p](D)}function I(b,p,D){b.textNode&&z(b),V(b,p,D)}function z(b){b.textNode=$(b.opt,b.textNode),b.textNode&&V(b,"ontext",b.textNode),b.textNode=""}function $(b,p){return b.trim&&(p=p.trim()),b.normalize&&(p=p.replace(/\s+/g," ")),p}function q(b,p){return z(b),b.trackPosition&&(p+=`
Line: `+b.line+`
Column: `+b.column+`
Char: `+b.c),p=new Error(p),b.error=p,V(b,"onerror",p),b}function de(b){return b.sawRoot&&!b.closedRoot&&H(b,"Unclosed root tag"),b.state!==w.BEGIN&&b.state!==w.BEGIN_WHITESPACE&&b.state!==w.TEXT&&q(b,"Unexpected end"),z(b),b.c="",b.closed=!0,V(b,"onend"),i.call(b,b.strict,b.opt),b}function H(b,p){if(typeof b!="object"||!(b instanceof i))throw new Error("bad call to strictFail");b.strict&&q(b,p)}function ze(b){b.strict||(b.tagName=b.tagName[b.looseCase]());var p=b.tags[b.tags.length-1]||b,D=b.tag={name:b.tagName,attributes:{}};b.opt.xmlns&&(D.ns=p.ns),b.attribList.length=0,I(b,"onopentagstart",D)}function se(b,p){var D=b.indexOf(":"),C=D<0?["",b]:b.split(":"),X=C[0],ae=C[1];return p&&b==="xmlns"&&(X="xmlns",ae=""),{prefix:X,local:ae}}function Oe(b){if(b.strict||(b.attribName=b.attribName[b.looseCase]()),b.attribList.indexOf(b.attribName)!==-1||b.tag.attributes.hasOwnProperty(b.attribName)){b.attribName=b.attribValue="";return}if(b.opt.xmlns){var p=se(b.attribName,!0),D=p.prefix,C=p.local;if(D==="xmlns")if(C==="xml"&&b.attribValue!==d)H(b,"xml: prefix must be bound to "+d+`
Actual: `+b.attribValue);else if(C==="xmlns"&&b.attribValue!==m)H(b,"xmlns: prefix must be bound to "+m+`
Actual: `+b.attribValue);else{var X=b.tag,ae=b.tags[b.tags.length-1]||b;X.ns===ae.ns&&(X.ns=Object.create(ae.ns)),X.ns[C]=b.attribValue}b.attribList.push([b.attribName,b.attribValue])}else b.tag.attributes[b.attribName]=b.attribValue,I(b,"onattribute",{name:b.attribName,value:b.attribValue});b.attribName=b.attribValue=""}function ye(b,p){if(b.opt.xmlns){var D=b.tag,C=se(b.tagName);D.prefix=C.prefix,D.local=C.local,D.uri=D.ns[C.prefix]||"",D.prefix&&!D.uri&&(H(b,"Unbound namespace prefix: "+JSON.stringify(b.tagName)),D.uri=C.prefix);var X=b.tags[b.tags.length-1]||b;D.ns&&X.ns!==D.ns&&Object.keys(D.ns).forEach(function(Dl){I(b,"onopennamespace",{prefix:Dl,uri:D.ns[Dl]})});for(var ae=0,he=b.attribList.length;ae<he;ae++){var Re=b.attribList[ae],Be=Re[0],hi=Re[1],be=se(Be,!0),wt=be.prefix,nd=be.local,El=wt===""?"":D.ns[wt]||"",Qs={name:Be,value:hi,prefix:wt,local:nd,uri:El};wt&&wt!=="xmlns"&&!El&&(H(b,"Unbound namespace prefix: "+JSON.stringify(wt)),Qs.uri=wt),b.tag.attributes[Be]=Qs,I(b,"onattribute",Qs)}b.attribList.length=0}b.tag.isSelfClosing=!!p,b.sawRoot=!0,b.tags.push(b.tag),I(b,"onopentag",b.tag),p||(!b.noscript&&b.tagName.toLowerCase()==="script"?b.state=w.SCRIPT:b.state=w.TEXT,b.tag=null,b.tagName=""),b.attribName=b.attribValue="",b.attribList.length=0}function ke(b){if(!b.tagName){H(b,"Weird empty close tag."),b.textNode+="</>",b.state=w.TEXT;return}if(b.script){if(b.tagName!=="script"){b.script+="</"+b.tagName+">",b.tagName="",b.state=w.SCRIPT;return}I(b,"onscript",b.script),b.script=""}var p=b.tags.length,D=b.tagName;b.strict||(D=D[b.looseCase]());for(var C=D;p--;){var X=b.tags[p];if(X.name!==C)H(b,"Unexpected close tag");else break}if(p<0){H(b,"Unmatched closing tag: "+b.tagName),b.textNode+="</"+b.tagName+">",b.state=w.TEXT;return}b.tagName=D;for(var ae=b.tags.length;ae-- >p;){var he=b.tag=b.tags.pop();b.tagName=b.tag.name,I(b,"onclosetag",b.tagName);var Re={};for(var Be in he.ns)Re[Be]=he.ns[Be];var hi=b.tags[b.tags.length-1]||b;b.opt.xmlns&&he.ns!==hi.ns&&Object.keys(he.ns).forEach(function(be){var wt=he.ns[be];I(b,"onclosenamespace",{prefix:be,uri:wt})})}p===0&&(b.closedRoot=!0),b.tagName=b.attribValue=b.attribName="",b.attribList.length=0,b.state=w.TEXT}function je(b){var p=b.entity,D=p.toLowerCase(),C,X="";return b.ENTITIES[p]?b.ENTITIES[p]:b.ENTITIES[D]?b.ENTITIES[D]:(p=D,p.charAt(0)==="#"&&(p.charAt(1)==="x"?(p=p.slice(2),C=parseInt(p,16),X=C.toString(16)):(p=p.slice(1),C=parseInt(p,10),X=C.toString(10))),p=p.replace(/^0+/,""),isNaN(C)||X.toLowerCase()!==p?(H(b,"Invalid character entity"),"&"+b.entity+";"):String.fromCodePoint(C))}function rt(b,p){p==="<"?(b.state=w.OPEN_WAKA,b.startTagPosition=b.position):g(p)||(H(b,"Non-whitespace before first tag."),b.textNode=p,b.state=w.TEXT)}function vt(b,p){var D="";return p<b.length&&(D=b.charAt(p)),D}function ai(b){var p=this;if(this.error)throw this.error;if(p.closed)return q(p,"Cannot write after close. Assign an onready handler.");if(b===null)return de(p);typeof b=="object"&&(b=b.toString());for(var D=0,C="";C=vt(b,D++),p.c=C,!!C;)switch(p.trackPosition&&(p.position++,C===`
`?(p.line++,p.column=0):p.column++),p.state){case w.BEGIN:if(p.state=w.BEGIN_WHITESPACE,C==="\uFEFF")continue;rt(p,C);continue;case w.BEGIN_WHITESPACE:rt(p,C);continue;case w.TEXT:if(p.sawRoot&&!p.closedRoot){for(var X=D-1;C&&C!=="<"&&C!=="&";)C=vt(b,D++),C&&p.trackPosition&&(p.position++,C===`
`?(p.line++,p.column=0):p.column++);p.textNode+=b.substring(X,D-1)}C==="<"&&!(p.sawRoot&&p.closedRoot&&!p.strict)?(p.state=w.OPEN_WAKA,p.startTagPosition=p.position):(!g(C)&&(!p.sawRoot||p.closedRoot)&&H(p,"Text data outside of root node."),C==="&"?p.state=w.TEXT_ENTITY:p.textNode+=C);continue;case w.SCRIPT:C==="<"?p.state=w.SCRIPT_ENDING:p.script+=C;continue;case w.SCRIPT_ENDING:C==="/"?p.state=w.CLOSE_TAG:(p.script+="<"+C,p.state=w.SCRIPT);continue;case w.OPEN_WAKA:if(C==="!")p.state=w.SGML_DECL,p.sgmlDecl="";else if(!g(C))if(M(S,C))p.state=w.OPEN_TAG,p.tagName=C;else if(C==="/")p.state=w.CLOSE_TAG,p.tagName="";else if(C==="?")p.state=w.PROC_INST,p.procInstName=p.procInstBody="";else{if(H(p,"Unencoded <"),p.startTagPosition+1<p.position){var ae=p.position-p.startTagPosition;C=new Array(ae).join(" ")+C}p.textNode+="<"+C,p.state=w.TEXT}continue;case w.SGML_DECL:if(p.sgmlDecl+C==="--"){p.state=w.COMMENT,p.comment="",p.sgmlDecl="";continue}p.doctype&&p.doctype!==!0&&p.sgmlDecl?(p.state=w.DOCTYPE_DTD,p.doctype+="<!"+p.sgmlDecl+C,p.sgmlDecl=""):(p.sgmlDecl+C).toUpperCase()===f?(I(p,"onopencdata"),p.state=w.CDATA,p.sgmlDecl="",p.cdata=""):(p.sgmlDecl+C).toUpperCase()===u?(p.state=w.DOCTYPE,(p.doctype||p.sawRoot)&&H(p,"Inappropriately located doctype declaration"),p.doctype="",p.sgmlDecl=""):C===">"?(I(p,"onsgmldeclaration",p.sgmlDecl),p.sgmlDecl="",p.state=w.TEXT):(k(C)&&(p.state=w.SGML_DECL_QUOTED),p.sgmlDecl+=C);continue;case w.SGML_DECL_QUOTED:C===p.q&&(p.state=w.SGML_DECL,p.q=""),p.sgmlDecl+=C;continue;case w.DOCTYPE:C===">"?(p.state=w.TEXT,I(p,"ondoctype",p.doctype),p.doctype=!0):(p.doctype+=C,C==="["?p.state=w.DOCTYPE_DTD:k(C)&&(p.state=w.DOCTYPE_QUOTED,p.q=C));continue;case w.DOCTYPE_QUOTED:p.doctype+=C,C===p.q&&(p.q="",p.state=w.DOCTYPE);continue;case w.DOCTYPE_DTD:C==="]"?(p.doctype+=C,p.state=w.DOCTYPE):C==="<"?(p.state=w.OPEN_WAKA,p.startTagPosition=p.position):k(C)?(p.doctype+=C,p.state=w.DOCTYPE_DTD_QUOTED,p.q=C):p.doctype+=C;continue;case w.DOCTYPE_DTD_QUOTED:p.doctype+=C,C===p.q&&(p.state=w.DOCTYPE_DTD,p.q="");continue;case w.COMMENT:C==="-"?p.state=w.COMMENT_ENDING:p.comment+=C;continue;case w.COMMENT_ENDING:C==="-"?(p.state=w.COMMENT_ENDED,p.comment=$(p.opt,p.comment),p.comment&&I(p,"oncomment",p.comment),p.comment=""):(p.comment+="-"+C,p.state=w.COMMENT);continue;case w.COMMENT_ENDED:C!==">"?(H(p,"Malformed comment"),p.comment+="--"+C,p.state=w.COMMENT):p.doctype&&p.doctype!==!0?p.state=w.DOCTYPE_DTD:p.state=w.TEXT;continue;case w.CDATA:C==="]"?p.state=w.CDATA_ENDING:p.cdata+=C;continue;case w.CDATA_ENDING:C==="]"?p.state=w.CDATA_ENDING_2:(p.cdata+="]"+C,p.state=w.CDATA);continue;case w.CDATA_ENDING_2:C===">"?(p.cdata&&I(p,"oncdata",p.cdata),I(p,"onclosecdata"),p.cdata="",p.state=w.TEXT):C==="]"?p.cdata+="]":(p.cdata+="]]"+C,p.state=w.CDATA);continue;case w.PROC_INST:C==="?"?p.state=w.PROC_INST_ENDING:g(C)?p.state=w.PROC_INST_BODY:p.procInstName+=C;continue;case w.PROC_INST_BODY:if(!p.procInstBody&&g(C))continue;C==="?"?p.state=w.PROC_INST_ENDING:p.procInstBody+=C;continue;case w.PROC_INST_ENDING:C===">"?(I(p,"onprocessinginstruction",{name:p.procInstName,body:p.procInstBody}),p.procInstName=p.procInstBody="",p.state=w.TEXT):(p.procInstBody+="?"+C,p.state=w.PROC_INST_BODY);continue;case w.OPEN_TAG:M(y,C)?p.tagName+=C:(ze(p),C===">"?ye(p):C==="/"?p.state=w.OPEN_TAG_SLASH:(g(C)||H(p,"Invalid character in tag name"),p.state=w.ATTRIB));continue;case w.OPEN_TAG_SLASH:C===">"?(ye(p,!0),ke(p)):(H(p,"Forward-slash in opening tag not followed by >"),p.state=w.ATTRIB);continue;case w.ATTRIB:if(g(C))continue;C===">"?ye(p):C==="/"?p.state=w.OPEN_TAG_SLASH:M(S,C)?(p.attribName=C,p.attribValue="",p.state=w.ATTRIB_NAME):H(p,"Invalid attribute name");continue;case w.ATTRIB_NAME:C==="="?p.state=w.ATTRIB_VALUE:C===">"?(H(p,"Attribute without value"),p.attribValue=p.attribName,Oe(p),ye(p)):g(C)?p.state=w.ATTRIB_NAME_SAW_WHITE:M(y,C)?p.attribName+=C:H(p,"Invalid attribute name");continue;case w.ATTRIB_NAME_SAW_WHITE:if(C==="=")p.state=w.ATTRIB_VALUE;else{if(g(C))continue;H(p,"Attribute without value"),p.tag.attributes[p.attribName]="",p.attribValue="",I(p,"onattribute",{name:p.attribName,value:""}),p.attribName="",C===">"?ye(p):M(S,C)?(p.attribName=C,p.state=w.ATTRIB_NAME):(H(p,"Invalid attribute name"),p.state=w.ATTRIB)}continue;case w.ATTRIB_VALUE:if(g(C))continue;k(C)?(p.q=C,p.state=w.ATTRIB_VALUE_QUOTED):(p.opt.unquotedAttributeValues||q(p,"Unquoted attribute value"),p.state=w.ATTRIB_VALUE_UNQUOTED,p.attribValue=C);continue;case w.ATTRIB_VALUE_QUOTED:if(C!==p.q){C==="&"?p.state=w.ATTRIB_VALUE_ENTITY_Q:p.attribValue+=C;continue}Oe(p),p.q="",p.state=w.ATTRIB_VALUE_CLOSED;continue;case w.ATTRIB_VALUE_CLOSED:g(C)?p.state=w.ATTRIB:C===">"?ye(p):C==="/"?p.state=w.OPEN_TAG_SLASH:M(S,C)?(H(p,"No whitespace between attributes"),p.attribName=C,p.attribValue="",p.state=w.ATTRIB_NAME):H(p,"Invalid attribute name");continue;case w.ATTRIB_VALUE_UNQUOTED:if(!T(C)){C==="&"?p.state=w.ATTRIB_VALUE_ENTITY_U:p.attribValue+=C;continue}Oe(p),C===">"?ye(p):p.state=w.ATTRIB;continue;case w.CLOSE_TAG:if(p.tagName)C===">"?ke(p):M(y,C)?p.tagName+=C:p.script?(p.script+="</"+p.tagName,p.tagName="",p.state=w.SCRIPT):(g(C)||H(p,"Invalid tagname in closing tag"),p.state=w.CLOSE_TAG_SAW_WHITE);else{if(g(C))continue;N(S,C)?p.script?(p.script+="</"+C,p.state=w.SCRIPT):H(p,"Invalid tagname in closing tag."):p.tagName=C}continue;case w.CLOSE_TAG_SAW_WHITE:if(g(C))continue;C===">"?ke(p):H(p,"Invalid characters in closing tag");continue;case w.TEXT_ENTITY:case w.ATTRIB_VALUE_ENTITY_Q:case w.ATTRIB_VALUE_ENTITY_U:var he,Re;switch(p.state){case w.TEXT_ENTITY:he=w.TEXT,Re="textNode";break;case w.ATTRIB_VALUE_ENTITY_Q:he=w.ATTRIB_VALUE_QUOTED,Re="attribValue";break;case w.ATTRIB_VALUE_ENTITY_U:he=w.ATTRIB_VALUE_UNQUOTED,Re="attribValue";break}if(C===";"){var Be=je(p);p.opt.unparsedEntities&&!Object.values(e.XML_ENTITIES).includes(Be)?(p.entity="",p.state=he,p.write(Be)):(p[Re]+=Be,p.entity="",p.state=he)}else M(p.entity.length?v:O,C)?p.entity+=C:(H(p,"Invalid character in entity name"),p[Re]+="&"+p.entity+C,p.entity="",p.state=he);continue;default:throw new Error(p,"Unknown state: "+p.state)}return p.position>=p.bufferCheckPosition&&s(p),p}/*! http://mths.be/fromcodepoint v0.1.0 by @mathias */String.fromCodePoint||function(){var b=String.fromCharCode,p=Math.floor,D=function(){var C=16384,X=[],ae,he,Re=-1,Be=arguments.length;if(!Be)return"";for(var hi="";++Re<Be;){var be=Number(arguments[Re]);if(!isFinite(be)||be<0||be>1114111||p(be)!==be)throw RangeError("Invalid code point: "+be);be<=65535?X.push(be):(be-=65536,ae=(be>>10)+55296,he=be%1024+56320,X.push(ae,he)),(Re+1===Be||X.length>C)&&(hi+=b.apply(null,X),X.length=0)}return hi};Object.defineProperty?Object.defineProperty(String,"fromCodePoint",{value:D,configurable:!0,writable:!0}):String.fromCodePoint=D}()})(n)}(Cr)),Cr}var Ar,vh;function vl(){return vh||(vh=1,Ar={isArray:function(n){return Array.isArray?Array.isArray(n):Object.prototype.toString.call(n)==="[object Array]"}}),Ar}var Tr,wh;function wl(){if(wh)return Tr;wh=1;var n=vl().isArray;return Tr={copyOptions:function(e){var t,i={};for(t in e)e.hasOwnProperty(t)&&(i[t]=e[t]);return i},ensureFlagExists:function(e,t){(!(e in t)||typeof t[e]!="boolean")&&(t[e]=!1)},ensureSpacesExists:function(e){(!("spaces"in e)||typeof e.spaces!="number"&&typeof e.spaces!="string")&&(e.spaces=0)},ensureAlwaysArrayExists:function(e){(!("alwaysArray"in e)||typeof e.alwaysArray!="boolean"&&!n(e.alwaysArray))&&(e.alwaysArray=!1)},ensureKeyExists:function(e,t){(!(e+"Key"in t)||typeof t[e+"Key"]!="string")&&(t[e+"Key"]=t.compact?"_"+e:e)},checkFnExists:function(e,t){return e+"Fn"in t}},Tr}var Mr,Sh;function Iu(){if(Sh)return Mr;Sh=1;var n=Ib(),e=wl(),t=vl().isArray,i,s;function r(y){return i=e.copyOptions(y),e.ensureFlagExists("ignoreDeclaration",i),e.ensureFlagExists("ignoreInstruction",i),e.ensureFlagExists("ignoreAttributes",i),e.ensureFlagExists("ignoreText",i),e.ensureFlagExists("ignoreComment",i),e.ensureFlagExists("ignoreCdata",i),e.ensureFlagExists("ignoreDoctype",i),e.ensureFlagExists("compact",i),e.ensureFlagExists("alwaysChildren",i),e.ensureFlagExists("addParent",i),e.ensureFlagExists("trim",i),e.ensureFlagExists("nativeType",i),e.ensureFlagExists("nativeTypeAttributes",i),e.ensureFlagExists("sanitize",i),e.ensureFlagExists("instructionHasAttributes",i),e.ensureFlagExists("captureSpacesBetweenElements",i),e.ensureAlwaysArrayExists(i),e.ensureKeyExists("declaration",i),e.ensureKeyExists("instruction",i),e.ensureKeyExists("attributes",i),e.ensureKeyExists("text",i),e.ensureKeyExists("comment",i),e.ensureKeyExists("cdata",i),e.ensureKeyExists("doctype",i),e.ensureKeyExists("type",i),e.ensureKeyExists("name",i),e.ensureKeyExists("elements",i),e.ensureKeyExists("parent",i),e.checkFnExists("doctype",i),e.checkFnExists("instruction",i),e.checkFnExists("cdata",i),e.checkFnExists("comment",i),e.checkFnExists("text",i),e.checkFnExists("instructionName",i),e.checkFnExists("elementName",i),e.checkFnExists("attributeName",i),e.checkFnExists("attributeValue",i),e.checkFnExists("attributes",i),i}function o(y){var O=Number(y);if(!isNaN(O))return O;var v=y.toLowerCase();return v==="true"?!0:v==="false"?!1:y}function l(y,O){var v;if(i.compact){if(!s[i[y+"Key"]]&&(t(i.alwaysArray)?i.alwaysArray.indexOf(i[y+"Key"])!==-1:i.alwaysArray)&&(s[i[y+"Key"]]=[]),s[i[y+"Key"]]&&!t(s[i[y+"Key"]])&&(s[i[y+"Key"]]=[s[i[y+"Key"]]]),y+"Fn"in i&&typeof O=="string"&&(O=i[y+"Fn"](O,s)),y==="instruction"&&("instructionFn"in i||"instructionNameFn"in i)){for(v in O)if(O.hasOwnProperty(v))if("instructionFn"in i)O[v]=i.instructionFn(O[v],v,s);else{var g=O[v];delete O[v],O[i.instructionNameFn(v,g,s)]=g}}t(s[i[y+"Key"]])?s[i[y+"Key"]].push(O):s[i[y+"Key"]]=O}else{s[i.elementsKey]||(s[i.elementsKey]=[]);var k={};if(k[i.typeKey]=y,y==="instruction"){for(v in O)if(O.hasOwnProperty(v))break;k[i.nameKey]="instructionNameFn"in i?i.instructionNameFn(v,O,s):v,i.instructionHasAttributes?(k[i.attributesKey]=O[v][i.attributesKey],"instructionFn"in i&&(k[i.attributesKey]=i.instructionFn(k[i.attributesKey],v,s))):("instructionFn"in i&&(O[v]=i.instructionFn(O[v],v,s)),k[i.instructionKey]=O[v])}else y+"Fn"in i&&(O=i[y+"Fn"](O,s)),k[i[y+"Key"]]=O;i.addParent&&(k[i.parentKey]=s),s[i.elementsKey].push(k)}}function a(y){if("attributesFn"in i&&y&&(y=i.attributesFn(y,s)),(i.trim||"attributeValueFn"in i||"attributeNameFn"in i||i.nativeTypeAttributes)&&y){var O;for(O in y)if(y.hasOwnProperty(O)&&(i.trim&&(y[O]=y[O].trim()),i.nativeTypeAttributes&&(y[O]=o(y[O])),"attributeValueFn"in i&&(y[O]=i.attributeValueFn(y[O],O,s)),"attributeNameFn"in i)){var v=y[O];delete y[O],y[i.attributeNameFn(O,y[O],s)]=v}}return y}function h(y){var O={};if(y.body&&(y.name.toLowerCase()==="xml"||i.instructionHasAttributes)){for(var v=/([\w:-]+)\s*=\s*(?:"([^"]*)"|'([^']*)'|(\w+))\s*/g,g;(g=v.exec(y.body))!==null;)O[g[1]]=g[2]||g[3]||g[4];O=a(O)}if(y.name.toLowerCase()==="xml"){if(i.ignoreDeclaration)return;s[i.declarationKey]={},Object.keys(O).length&&(s[i.declarationKey][i.attributesKey]=O),i.addParent&&(s[i.declarationKey][i.parentKey]=s)}else{if(i.ignoreInstruction)return;i.trim&&(y.body=y.body.trim());var k={};i.instructionHasAttributes&&Object.keys(O).length?(k[y.name]={},k[y.name][i.attributesKey]=O):k[y.name]=y.body,l("instruction",k)}}function c(y,O){var v;if(typeof y=="object"&&(O=y.attributes,y=y.name),O=a(O),"elementNameFn"in i&&(y=i.elementNameFn(y,s)),i.compact){if(v={},!i.ignoreAttributes&&O&&Object.keys(O).length){v[i.attributesKey]={};var g;for(g in O)O.hasOwnProperty(g)&&(v[i.attributesKey][g]=O[g])}!(y in s)&&(t(i.alwaysArray)?i.alwaysArray.indexOf(y)!==-1:i.alwaysArray)&&(s[y]=[]),s[y]&&!t(s[y])&&(s[y]=[s[y]]),t(s[y])?s[y].push(v):s[y]=v}else s[i.elementsKey]||(s[i.elementsKey]=[]),v={},v[i.typeKey]="element",v[i.nameKey]=y,!i.ignoreAttributes&&O&&Object.keys(O).length&&(v[i.attributesKey]=O),i.alwaysChildren&&(v[i.elementsKey]=[]),s[i.elementsKey].push(v);v[i.parentKey]=s,s=v}function f(y){i.ignoreText||!y.trim()&&!i.captureSpacesBetweenElements||(i.trim&&(y=y.trim()),i.nativeType&&(y=o(y)),i.sanitize&&(y=y.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;")),l("text",y))}function u(y){i.ignoreComment||(i.trim&&(y=y.trim()),l("comment",y))}function d(y){var O=s[i.parentKey];i.addParent||delete s[i.parentKey],s=O}function m(y){i.ignoreCdata||(i.trim&&(y=y.trim()),l("cdata",y))}function x(y){i.ignoreDoctype||(y=y.replace(/^ /,""),i.trim&&(y=y.trim()),l("doctype",y))}function S(y){y.note=y}return Mr=function(y,O){var v=n.parser(!0,{}),g={};if(s=g,i=r(O),v.opt={strictEntities:!0},v.onopentag=c,v.ontext=f,v.oncomment=u,v.onclosetag=d,v.onerror=S,v.oncdata=m,v.ondoctype=x,v.onprocessinginstruction=h,v.write(y).close(),g[i.elementsKey]){var k=g[i.elementsKey];delete g[i.elementsKey],g[i.elementsKey]=k,delete g.text}return g},Mr}var Er,Oh;function Lb(){if(Oh)return Er;Oh=1;var n=wl(),e=Iu();function t(i){var s=n.copyOptions(i);return n.ensureSpacesExists(s),s}return Er=function(i,s){var r,o,l,a;return r=t(s),o=e(i,r),a="compact"in r&&r.compact?"_parent":"parent","addParent"in r&&r.addParent?l=JSON.stringify(o,function(h,c){return h===a?"_":c},r.spaces):l=JSON.stringify(o,null,r.spaces),l.replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029")},Er}var Dr,kh;function Lu(){if(kh)return Dr;kh=1;var n=wl(),e=vl().isArray,t,i;function s(v){var g=n.copyOptions(v);return n.ensureFlagExists("ignoreDeclaration",g),n.ensureFlagExists("ignoreInstruction",g),n.ensureFlagExists("ignoreAttributes",g),n.ensureFlagExists("ignoreText",g),n.ensureFlagExists("ignoreComment",g),n.ensureFlagExists("ignoreCdata",g),n.ensureFlagExists("ignoreDoctype",g),n.ensureFlagExists("compact",g),n.ensureFlagExists("indentText",g),n.ensureFlagExists("indentCdata",g),n.ensureFlagExists("indentAttributes",g),n.ensureFlagExists("indentInstruction",g),n.ensureFlagExists("fullTagEmptyElement",g),n.ensureFlagExists("noQuotesForNativeAttributes",g),n.ensureSpacesExists(g),typeof g.spaces=="number"&&(g.spaces=Array(g.spaces+1).join(" ")),n.ensureKeyExists("declaration",g),n.ensureKeyExists("instruction",g),n.ensureKeyExists("attributes",g),n.ensureKeyExists("text",g),n.ensureKeyExists("comment",g),n.ensureKeyExists("cdata",g),n.ensureKeyExists("doctype",g),n.ensureKeyExists("type",g),n.ensureKeyExists("name",g),n.ensureKeyExists("elements",g),n.checkFnExists("doctype",g),n.checkFnExists("instruction",g),n.checkFnExists("cdata",g),n.checkFnExists("comment",g),n.checkFnExists("text",g),n.checkFnExists("instructionName",g),n.checkFnExists("elementName",g),n.checkFnExists("attributeName",g),n.checkFnExists("attributeValue",g),n.checkFnExists("attributes",g),n.checkFnExists("fullTagEmptyElement",g),g}function r(v,g,k){return(!k&&v.spaces?`
`:"")+Array(g+1).join(v.spaces)}function o(v,g,k){if(g.ignoreAttributes)return"";"attributesFn"in g&&(v=g.attributesFn(v,i,t));var T,M,N,w,P=[];for(T in v)v.hasOwnProperty(T)&&v[T]!==null&&v[T]!==void 0&&(w=g.noQuotesForNativeAttributes&&typeof v[T]!="string"?"":'"',M=""+v[T],M=M.replace(/"/g,"&quot;"),N="attributeNameFn"in g?g.attributeNameFn(T,M,i,t):T,P.push(g.spaces&&g.indentAttributes?r(g,k+1,!1):" "),P.push(N+"="+w+("attributeValueFn"in g?g.attributeValueFn(M,T,i,t):M)+w));return v&&Object.keys(v).length&&g.spaces&&g.indentAttributes&&P.push(r(g,k,!1)),P.join("")}function l(v,g,k){return t=v,i="xml",g.ignoreDeclaration?"":"<?xml"+o(v[g.attributesKey],g,k)+"?>"}function a(v,g,k){if(g.ignoreInstruction)return"";var T;for(T in v)if(v.hasOwnProperty(T))break;var M="instructionNameFn"in g?g.instructionNameFn(T,v[T],i,t):T;if(typeof v[T]=="object")return t=v,i=M,"<?"+M+o(v[T][g.attributesKey],g,k)+"?>";var N=v[T]?v[T]:"";return"instructionFn"in g&&(N=g.instructionFn(N,T,i,t)),"<?"+M+(N?" "+N:"")+"?>"}function h(v,g){return g.ignoreComment?"":"<!--"+("commentFn"in g?g.commentFn(v,i,t):v)+"-->"}function c(v,g){return g.ignoreCdata?"":"<![CDATA["+("cdataFn"in g?g.cdataFn(v,i,t):v.replace("]]>","]]]]><![CDATA[>"))+"]]>"}function f(v,g){return g.ignoreDoctype?"":"<!DOCTYPE "+("doctypeFn"in g?g.doctypeFn(v,i,t):v)+">"}function u(v,g){return g.ignoreText?"":(v=""+v,v=v.replace(/&amp;/g,"&"),v=v.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;"),"textFn"in g?g.textFn(v,i,t):v)}function d(v,g){var k;if(v.elements&&v.elements.length)for(k=0;k<v.elements.length;++k)switch(v.elements[k][g.typeKey]){case"text":if(g.indentText)return!0;break;case"cdata":if(g.indentCdata)return!0;break;case"instruction":if(g.indentInstruction)return!0;break;case"doctype":case"comment":case"element":return!0;default:return!0}return!1}function m(v,g,k){t=v,i=v.name;var T=[],M="elementNameFn"in g?g.elementNameFn(v.name,v):v.name;T.push("<"+M),v[g.attributesKey]&&T.push(o(v[g.attributesKey],g,k));var N=v[g.elementsKey]&&v[g.elementsKey].length||v[g.attributesKey]&&v[g.attributesKey]["xml:space"]==="preserve";return N||("fullTagEmptyElementFn"in g?N=g.fullTagEmptyElementFn(v.name,v):N=g.fullTagEmptyElement),N?(T.push(">"),v[g.elementsKey]&&v[g.elementsKey].length&&(T.push(x(v[g.elementsKey],g,k+1)),t=v,i=v.name),T.push(g.spaces&&d(v,g)?`
`+Array(k+1).join(g.spaces):""),T.push("</"+M+">")):T.push("/>"),T.join("")}function x(v,g,k,T){return v.reduce(function(M,N){var w=r(g,k,T&&!M);switch(N.type){case"element":return M+w+m(N,g,k);case"comment":return M+w+h(N[g.commentKey],g);case"doctype":return M+w+f(N[g.doctypeKey],g);case"cdata":return M+(g.indentCdata?w:"")+c(N[g.cdataKey],g);case"text":return M+(g.indentText?w:"")+u(N[g.textKey],g);case"instruction":var P={};return P[N[g.nameKey]]=N[g.attributesKey]?N:N[g.instructionKey],M+(g.indentInstruction?w:"")+a(P,g,k)}},"")}function S(v,g,k){var T;for(T in v)if(v.hasOwnProperty(T))switch(T){case g.parentKey:case g.attributesKey:break;case g.textKey:if(g.indentText||k)return!0;break;case g.cdataKey:if(g.indentCdata||k)return!0;break;case g.instructionKey:if(g.indentInstruction||k)return!0;break;case g.doctypeKey:case g.commentKey:return!0;default:return!0}return!1}function y(v,g,k,T,M){t=v,i=g;var N="elementNameFn"in k?k.elementNameFn(g,v):g;if(typeof v>"u"||v===null||v==="")return"fullTagEmptyElementFn"in k&&k.fullTagEmptyElementFn(g,v)||k.fullTagEmptyElement?"<"+N+"></"+N+">":"<"+N+"/>";var w=[];if(g){if(w.push("<"+N),typeof v!="object")return w.push(">"+u(v,k)+"</"+N+">"),w.join("");v[k.attributesKey]&&w.push(o(v[k.attributesKey],k,T));var P=S(v,k,!0)||v[k.attributesKey]&&v[k.attributesKey]["xml:space"]==="preserve";if(P||("fullTagEmptyElementFn"in k?P=k.fullTagEmptyElementFn(g,v):P=k.fullTagEmptyElement),P)w.push(">");else return w.push("/>"),w.join("")}return w.push(O(v,k,T+1,!1)),t=v,i=g,g&&w.push((M?r(k,T,!1):"")+"</"+N+">"),w.join("")}function O(v,g,k,T){var M,N,w,P=[];for(N in v)if(v.hasOwnProperty(N))for(w=e(v[N])?v[N]:[v[N]],M=0;M<w.length;++M){switch(N){case g.declarationKey:P.push(l(w[M],g,k));break;case g.instructionKey:P.push((g.indentInstruction?r(g,k,T):"")+a(w[M],g,k));break;case g.attributesKey:case g.parentKey:break;case g.textKey:P.push((g.indentText?r(g,k,T):"")+u(w[M],g));break;case g.cdataKey:P.push((g.indentCdata?r(g,k,T):"")+c(w[M],g));break;case g.doctypeKey:P.push(r(g,k,T)+f(w[M],g));break;case g.commentKey:P.push(r(g,k,T)+h(w[M],g));break;default:P.push(r(g,k,T)+y(w[M],N,g,k,S(w[M],g)))}T=T&&!P.length}return P.join("")}return Dr=function(v,g){g=s(g);var k=[];return t=v,i="_root_",g.compact?k.push(O(v,g,0,!0)):(v[g.declarationKey]&&k.push(l(v[g.declarationKey],g,0)),v[g.elementsKey]&&v[g.elementsKey].length&&k.push(x(v[g.elementsKey],g,0,!k.length))),k.join("")},Dr}var Pr,Ch;function Fb(){if(Ch)return Pr;Ch=1;var n=Lu();return Pr=function(e,t){e instanceof Buffer&&(e=e.toString());var i=null;if(typeof e=="string")try{i=JSON.parse(e)}catch{throw new Error("The JSON structure is invalid")}else i=e;return n(i,t)},Pr}var Nr,Ah;function Vb(){if(Ah)return Nr;Ah=1;var n=Iu(),e=Lb(),t=Lu(),i=Fb();return Nr={xml2js:n,xml2json:e,js2xml:t,json2xml:i},Nr}Vb();const Wb={key:0,class:"errors"},_b={__name:"xml",props:{modelValue:{type:String,default:""},readonly:{type:Boolean,default:!1}},emits:["update:modelValue"],setup(n,{expose:e,emit:t}){const i=n,s=t,r=ie(null),o=ie(null),l=ie([]),a=ie(!1),h=Ri.define([{tag:A.tagName,color:"#905"},{tag:A.attributeName,color:"#690"},{tag:A.attributeValue,color:"#07a"},{tag:A.comment,color:"#708090"},{tag:A.processingInstruction,color:"#708"},{tag:A.content,color:"#333"},{tag:A.punctuation,color:"#999"},{tag:A.meta,color:"#555"}]),c=m=>{const x={"XML declaration allowed only at the start of the document":"XML声明只能出现在文档开头","StartTag: invalid element name":"开始标签: 无效的元素名称","EndTag: invalid element name":"结束标签: 无效的元素名称","Premature end of data in tag":"标签数据提前结束","Missing end tag for":"缺少结束标签: ","Opening and ending tag mismatch":"开始和结束标签不匹配","Extra content at the end of the document":"文档末尾有多余内容","Invalid character in tag name":"标签名称中包含无效字符","Unclosed token":"未闭合的标记","Unquoted attribute value":"未加引号的属性值","Attribute value missing closing quote":"属性值缺少结束引号","Whitespace expected":"需要空格","XML processing instruction not closed":"XML处理指令未闭合","Comment not closed":"注释未闭合","Entity reference expected":"需要实体引用","Character reference expected":"需要字符引用","Invalid character reference":"无效的字符引用","Invalid character":"无效的字符","Invalid doctype declaration":"无效的DOCTYPE声明","Invalid XML declaration":"无效的XML声明","Invalid XML version":"无效的XML版本","Invalid XML encoding":"无效的XML编码","Invalid XML standalone declaration":"无效的XML独立声明","CDATA section not closed":"CDATA部分未闭合",line:"行",column:"列",errors:"","This page contains the following":"该文件格式错误","error on":"错误位置在",at:"",and:"和",expected:"期望","error parsing tribute name":"解析属性名称时出错"};if(m.includes("line")){const S=m.split(": ");if(S.length>0)for(let y=0;y<S.length;y++){if(y<1){S[0]="文件格式错误";continue}if(S[y].includes("Line ")){const O=S[y].match(/Line (\d+)/);O&&(S[y]=S[y].replace(O[0],`第${O[1]}行`))}if(S[y].includes("line ")){const O=S[y].match(/line (\d+)/);O&&(S[y]=S[y].replace(O[0],`第${O[1]}行`))}if(S[y].includes("column ")){const O=S[y].match(/column (\d+)/);O&&(S[y]=S[y].replace(O[0],`第${O[1]}列`))}for(const[O,v]of Object.entries(x))S[y].includes(O)&&(S[y]=S[y].replace(O,v))}return S.join(": ")}return m},f=()=>m=>{const x=[];try{const S=m.state.doc.toString();if(S.trim()){const v=new DOMParser().parseFromString(S,"application/xml").getElementsByTagName("parsererror");if(v.length>0){const k=v[0].textContent,T=k.match(/第(\d+)行/)||k.match(/on line (\d+)/);let M=T?parseInt(T[1]):1,N=m.state.doc.line(M).text;if(k.includes("Opening and ending tag mismatch")&&N.indexOf("  <")==0){let $=k.split(": ");if($&&$.length>1){const q=$[$.length-1].match(/line (\d+)/);M=q?parseInt(q[1]):M,N=m.state.doc.line(M).text}}const w=m.state.doc.line(M).from,P=m.state.doc.line(M).to;let V=w,I=P,z=c(k);x.push({from:V,to:I,severity:"error",message:z.split(`
`)[0].trim(),line:M})}}}catch(S){const y=m.state.doc.toString();x.push({from:0,to:y.length,severity:"error",message:`解析错误: ${S.message}`,line:1})}return l.value=x.map(S=>({line:S.line,message:S.message})),x},u=()=>{o.value&&o.value.destroy();const m=[nl(),oy(),pl(h),R.updateListener.of(S=>{if(S.docChanged){const y=S.state.doc.toString();s("update:modelValue",y)}}),R.editable.of(!i.readonly),Bb(),Cb(f()),vn.of([...Ou,ku]),R.theme({"&":{height:"100%",fontSize:"15px",border:"1px solid #3a3f4b",borderRadius:"4px"},".cm-gutters":{backgroundColor:"#282c34",borderRight:"1px solid #3a3f4b",color:"#858585",display:"block !important"},".cm-lineNumbers .cm-gutterElement":{padding:"0 10px 0 5px",minWidth:"20px"},".cm-activeLine":{backgroundColor:"#2c313a"},".cm-activeLineGutter":{backgroundColor:"#2c313a",color:"#fff"},".cm-scroller":{fontFamily:'"Fira Code", Menlo, Monaco, Consolas, "Courier New", monospace',lineHeight:"1.6"},".cm-content":{caretColor:"#528bff",padding:"8px 0"},".cm-cursor":{borderLeft:"2px solid #528bff"}})];a.value&&m.push(R.lineWrapping);const x=j.create({doc:i.modelValue,extensions:m});o.value=new R({state:x,parent:r.value})},d=()=>{f()(o.value)};return Ke(()=>i.modelValue,m=>{var S,y;const x=(S=o.value)==null?void 0:S.state.doc.toString();m!==x&&((y=o.value)==null||y.dispatch({changes:{from:0,to:o.value.state.doc.length,insert:m}}))}),Ke(()=>i.readonly,m=>{o.value&&o.value.dispatch({effects:R.editable.of(!m)})}),Wo(()=>{u()}),_o(()=>{o.value&&o.value.destroy()}),e({getErrors:()=>l.value,validateXml:d}),(m,x)=>(Ot(),cs("div",null,[l.value.length?(Ot(),cs("div",Wb,x[0]||(x[0]=[xe("div",{class:"error"}," 文件格式错误！ ",-1)]))):Kh("",!0),xe("div",{ref_key:"editor",ref:r,class:"xml-editor"},null,512)]))}},$b=$o(_b,[["__scopeId","data-v-aaf38324"]]),Th=typeof String.prototype.normalize=="function"?n=>n.normalize("NFKD"):n=>n;class Pi{constructor(e,t,i=0,s=e.length,r,o){this.test=o,this.value={from:0,to:0},this.done=!1,this.matches=[],this.buffer="",this.bufferPos=0,this.iter=e.iterRange(i,s),this.bufferStart=i,this.normalize=r?l=>r(Th(l)):Th,this.query=this.normalize(t)}peek(){if(this.bufferPos==this.buffer.length){if(this.bufferStart+=this.buffer.length,this.iter.next(),this.iter.done)return-1;this.bufferPos=0,this.buffer=this.iter.value}return Ie(this.buffer,this.bufferPos)}next(){for(;this.matches.length;)this.matches.pop();return this.nextOverlapping()}nextOverlapping(){for(;;){let e=this.peek();if(e<0)return this.done=!0,this;let t=qo(e),i=this.bufferStart+this.bufferPos;this.bufferPos+=ft(e);let s=this.normalize(t);if(s.length)for(let r=0,o=i;;r++){let l=s.charCodeAt(r),a=this.match(l,o,this.bufferPos+this.bufferStart);if(r==s.length-1){if(a)return this.value=a,this;break}o==i&&r<t.length&&t.charCodeAt(r)==l&&o++}}}match(e,t,i){let s=null;for(let r=0;r<this.matches.length;r+=2){let o=this.matches[r],l=!1;this.query.charCodeAt(o)==e&&(o==this.query.length-1?s={from:this.matches[r+1],to:i}:(this.matches[r]++,l=!0)),l||(this.matches.splice(r,2),r-=2)}return this.query.charCodeAt(0)==e&&(this.query.length==1?s={from:t,to:i}:this.matches.push(1,t)),s&&this.test&&!this.test(s.from,s.to,this.buffer,this.bufferStart)&&(s=null),s}}typeof Symbol<"u"&&(Pi.prototype[Symbol.iterator]=function(){return this});const Fu={from:-1,to:-1,match:/.*/.exec("")},Sl="gm"+(/x/.unicode==null?"":"u");class Vu{constructor(e,t,i,s=0,r=e.length){if(this.text=e,this.to=r,this.curLine="",this.done=!1,this.value=Fu,/\\[sWDnr]|\n|\r|\[\^/.test(t))return new Wu(e,t,i,s,r);this.re=new RegExp(t,Sl+(i!=null&&i.ignoreCase?"i":"")),this.test=i==null?void 0:i.test,this.iter=e.iter();let o=e.lineAt(s);this.curLineStart=o.from,this.matchPos=Es(e,s),this.getLine(this.curLineStart)}getLine(e){this.iter.next(e),this.iter.lineBreak?this.curLine="":(this.curLine=this.iter.value,this.curLineStart+this.curLine.length>this.to&&(this.curLine=this.curLine.slice(0,this.to-this.curLineStart)),this.iter.next())}nextLine(){this.curLineStart=this.curLineStart+this.curLine.length+1,this.curLineStart>this.to?this.curLine="":this.getLine(0)}next(){for(let e=this.matchPos-this.curLineStart;;){this.re.lastIndex=e;let t=this.matchPos<=this.to&&this.re.exec(this.curLine);if(t){let i=this.curLineStart+t.index,s=i+t[0].length;if(this.matchPos=Es(this.text,s+(i==s?1:0)),i==this.curLineStart+this.curLine.length&&this.nextLine(),(i<s||i>this.value.to)&&(!this.test||this.test(i,s,t)))return this.value={from:i,to:s,match:t},this;e=this.matchPos-this.curLineStart}else if(this.curLineStart+this.curLine.length<this.to)this.nextLine(),e=0;else return this.done=!0,this}}}const Rr=new WeakMap;class Si{constructor(e,t){this.from=e,this.text=t}get to(){return this.from+this.text.length}static get(e,t,i){let s=Rr.get(e);if(!s||s.from>=i||s.to<=t){let l=new Si(t,e.sliceString(t,i));return Rr.set(e,l),l}if(s.from==t&&s.to==i)return s;let{text:r,from:o}=s;return o>t&&(r=e.sliceString(t,o)+r,o=t),s.to<i&&(r+=e.sliceString(s.to,i)),Rr.set(e,new Si(o,r)),new Si(t,r.slice(t-o,i-o))}}class Wu{constructor(e,t,i,s,r){this.text=e,this.to=r,this.done=!1,this.value=Fu,this.matchPos=Es(e,s),this.re=new RegExp(t,Sl+(i!=null&&i.ignoreCase?"i":"")),this.test=i==null?void 0:i.test,this.flat=Si.get(e,s,this.chunkEnd(s+5e3))}chunkEnd(e){return e>=this.to?this.to:this.text.lineAt(e).to}next(){for(;;){let e=this.re.lastIndex=this.matchPos-this.flat.from,t=this.re.exec(this.flat.text);if(t&&!t[0]&&t.index==e&&(this.re.lastIndex=e+1,t=this.re.exec(this.flat.text)),t){let i=this.flat.from+t.index,s=i+t[0].length;if((this.flat.to>=this.to||t.index+t[0].length<=this.flat.text.length-10)&&(!this.test||this.test(i,s,t)))return this.value={from:i,to:s,match:t},this.matchPos=Es(this.text,s+(i==s?1:0)),this}if(this.flat.to==this.to)return this.done=!0,this;this.flat=Si.get(this.text,this.flat.from,this.chunkEnd(this.flat.from+this.flat.text.length*2))}}}typeof Symbol<"u"&&(Vu.prototype[Symbol.iterator]=Wu.prototype[Symbol.iterator]=function(){return this});function qb(n){try{return new RegExp(n,Sl),!0}catch{return!1}}function Es(n,e){if(e>=n.length)return e;let t=n.lineAt(e),i;for(;e<t.to&&(i=t.text.charCodeAt(e-t.from))>=56320&&i<57344;)e++;return e}function Lo(n){let e=String(n.state.doc.lineAt(n.state.selection.main.head).number),t=Q("input",{class:"cm-textfield",name:"line",value:e}),i=Q("form",{class:"cm-gotoLine",onkeydown:r=>{r.keyCode==27?(r.preventDefault(),n.dispatch({effects:Ji.of(!1)}),n.focus()):r.keyCode==13&&(r.preventDefault(),s())},onsubmit:r=>{r.preventDefault(),s()}},Q("label",n.state.phrase("Go to line"),": ",t)," ",Q("button",{class:"cm-button",type:"submit"},n.state.phrase("go")),Q("button",{name:"close",onclick:()=>{n.dispatch({effects:Ji.of(!1)}),n.focus()},"aria-label":n.state.phrase("close"),type:"button"},["×"]));function s(){let r=/^([+-])?(\d+)?(:\d+)?(%)?$/.exec(t.value);if(!r)return;let{state:o}=n,l=o.doc.lineAt(o.selection.main.head),[,a,h,c,f]=r,u=c?+c.slice(1):0,d=h?+h:l.number;if(h&&f){let S=d/100;a&&(S=S*(a=="-"?-1:1)+l.number/o.doc.lines),d=Math.round(o.doc.lines*S)}else h&&a&&(d=d*(a=="-"?-1:1)+l.number);let m=o.doc.line(Math.max(1,Math.min(o.doc.lines,d))),x=E.cursor(m.from+Math.max(0,Math.min(u,m.length)));n.dispatch({effects:[Ji.of(!1),R.scrollIntoView(x.from,{y:"center"})],selection:x}),n.focus()}return{dom:i}}const Ji=_.define(),Mh=ue.define({create(){return!0},update(n,e){for(let t of e.effects)t.is(Ji)&&(n=t.value);return n},provide:n=>ln.from(n,e=>e?Lo:null)}),Hb=n=>{let e=on(n,Lo);if(!e){let t=[Ji.of(!0)];n.state.field(Mh,!1)==null&&t.push(_.appendConfig.of([Mh,zb])),n.dispatch({effects:t}),e=on(n,Lo)}return e&&e.dom.querySelector("input").select(),!0},zb=R.baseTheme({".cm-panel.cm-gotoLine":{padding:"2px 6px 4px",position:"relative","& label":{fontSize:"80%"},"& [name=close]":{position:"absolute",top:"0",bottom:"0",right:"4px",backgroundColor:"inherit",border:"none",font:"inherit",padding:"0"}}}),jb={highlightWordAroundCursor:!1,minSelectionLength:1,maxMatches:100,wholeWords:!1},Kb=F.define({combine(n){return nt(n,jb,{highlightWordAroundCursor:(e,t)=>e||t,minSelectionLength:Math.min,maxMatches:Math.min})}});function Ub(n){return[Jb,Yb]}const Gb=W.mark({class:"cm-selectionMatch"}),Xb=W.mark({class:"cm-selectionMatch cm-selectionMatch-main"});function Eh(n,e,t,i){return(t==0||n(e.sliceDoc(t-1,t))!=te.Word)&&(i==e.doc.length||n(e.sliceDoc(i,i+1))!=te.Word)}function Qb(n,e,t,i){return n(e.sliceDoc(t,t+1))==te.Word&&n(e.sliceDoc(i-1,i))==te.Word}const Yb=ne.fromClass(class{constructor(n){this.decorations=this.getDeco(n)}update(n){(n.selectionSet||n.docChanged||n.viewportChanged)&&(this.decorations=this.getDeco(n.view))}getDeco(n){let e=n.state.facet(Kb),{state:t}=n,i=t.selection;if(i.ranges.length>1)return W.none;let s=i.main,r,o=null;if(s.empty){if(!e.highlightWordAroundCursor)return W.none;let a=t.wordAt(s.head);if(!a)return W.none;o=t.charCategorizer(s.head),r=t.sliceDoc(a.from,a.to)}else{let a=s.to-s.from;if(a<e.minSelectionLength||a>200)return W.none;if(e.wholeWords){if(r=t.sliceDoc(s.from,s.to),o=t.charCategorizer(s.head),!(Eh(o,t,s.from,s.to)&&Qb(o,t,s.from,s.to)))return W.none}else if(r=t.sliceDoc(s.from,s.to),!r)return W.none}let l=[];for(let a of n.visibleRanges){let h=new Pi(t.doc,r,a.from,a.to);for(;!h.next().done;){let{from:c,to:f}=h.value;if((!o||Eh(o,t,c,f))&&(s.empty&&c<=s.from&&f>=s.to?l.push(Xb.range(c,f)):(c>=s.to||f<=s.from)&&l.push(Gb.range(c,f)),l.length>e.maxMatches))return W.none}}return W.set(l)}},{decorations:n=>n.decorations}),Jb=R.baseTheme({".cm-selectionMatch":{backgroundColor:"#99ff7780"},".cm-searchMatch .cm-selectionMatch":{backgroundColor:"transparent"}}),Zb=({state:n,dispatch:e})=>{let{selection:t}=n,i=E.create(t.ranges.map(s=>n.wordAt(s.head)||E.cursor(s.head)),t.mainIndex);return i.eq(t)?!1:(e(n.update({selection:i})),!0)};function e1(n,e){let{main:t,ranges:i}=n.selection,s=n.wordAt(t.head),r=s&&s.from==t.from&&s.to==t.to;for(let o=!1,l=new Pi(n.doc,e,i[i.length-1].to);;)if(l.next(),l.done){if(o)return null;l=new Pi(n.doc,e,0,Math.max(0,i[i.length-1].from-1)),o=!0}else{if(o&&i.some(a=>a.from==l.value.from))continue;if(r){let a=n.wordAt(l.value.from);if(!a||a.from!=l.value.from||a.to!=l.value.to)continue}return l.value}}const t1=({state:n,dispatch:e})=>{let{ranges:t}=n.selection;if(t.some(r=>r.from===r.to))return Zb({state:n,dispatch:e});let i=n.sliceDoc(t[0].from,t[0].to);if(n.selection.ranges.some(r=>n.sliceDoc(r.from,r.to)!=i))return!1;let s=e1(n,i);return s?(e(n.update({selection:n.selection.addRange(E.range(s.from,s.to),!1),effects:R.scrollIntoView(s.to)})),!0):!1},Ii=F.define({combine(n){return nt(n,{top:!1,caseSensitive:!1,literal:!1,regexp:!1,wholeWord:!1,createPanel:e=>new d1(e),scrollToMatch:e=>R.scrollIntoView(e)})}});class _u{constructor(e){this.search=e.search,this.caseSensitive=!!e.caseSensitive,this.literal=!!e.literal,this.regexp=!!e.regexp,this.replace=e.replace||"",this.valid=!!this.search&&(!this.regexp||qb(this.search)),this.unquoted=this.unquote(this.search),this.wholeWord=!!e.wholeWord}unquote(e){return this.literal?e:e.replace(/\\([nrt\\])/g,(t,i)=>i=="n"?`
`:i=="r"?"\r":i=="t"?"	":"\\")}eq(e){return this.search==e.search&&this.replace==e.replace&&this.caseSensitive==e.caseSensitive&&this.regexp==e.regexp&&this.wholeWord==e.wholeWord}create(){return this.regexp?new r1(this):new n1(this)}getCursor(e,t=0,i){let s=e.doc?e:j.create({doc:e});return i==null&&(i=s.doc.length),this.regexp?ui(this,s,t,i):fi(this,s,t,i)}}class $u{constructor(e){this.spec=e}}function fi(n,e,t,i){return new Pi(e.doc,n.unquoted,t,i,n.caseSensitive?void 0:s=>s.toLowerCase(),n.wholeWord?i1(e.doc,e.charCategorizer(e.selection.main.head)):void 0)}function i1(n,e){return(t,i,s,r)=>((r>t||r+s.length<i)&&(r=Math.max(0,t-2),s=n.sliceString(r,Math.min(n.length,i+2))),(e(Ds(s,t-r))!=te.Word||e(Ps(s,t-r))!=te.Word)&&(e(Ps(s,i-r))!=te.Word||e(Ds(s,i-r))!=te.Word))}class n1 extends $u{constructor(e){super(e)}nextMatch(e,t,i){let s=fi(this.spec,e,i,e.doc.length).nextOverlapping();if(s.done){let r=Math.min(e.doc.length,t+this.spec.unquoted.length);s=fi(this.spec,e,0,r).nextOverlapping()}return s.done||s.value.from==t&&s.value.to==i?null:s.value}prevMatchInRange(e,t,i){for(let s=i;;){let r=Math.max(t,s-1e4-this.spec.unquoted.length),o=fi(this.spec,e,r,s),l=null;for(;!o.nextOverlapping().done;)l=o.value;if(l)return l;if(r==t)return null;s-=1e4}}prevMatch(e,t,i){let s=this.prevMatchInRange(e,0,t);return s||(s=this.prevMatchInRange(e,Math.max(0,i-this.spec.unquoted.length),e.doc.length)),s&&(s.from!=t||s.to!=i)?s:null}getReplacement(e){return this.spec.unquote(this.spec.replace)}matchAll(e,t){let i=fi(this.spec,e,0,e.doc.length),s=[];for(;!i.next().done;){if(s.length>=t)return null;s.push(i.value)}return s}highlight(e,t,i,s){let r=fi(this.spec,e,Math.max(0,t-this.spec.unquoted.length),Math.min(i+this.spec.unquoted.length,e.doc.length));for(;!r.next().done;)s(r.value.from,r.value.to)}}function ui(n,e,t,i){return new Vu(e.doc,n.search,{ignoreCase:!n.caseSensitive,test:n.wholeWord?s1(e.charCategorizer(e.selection.main.head)):void 0},t,i)}function Ds(n,e){return n.slice(Se(n,e,!1),e)}function Ps(n,e){return n.slice(e,Se(n,e))}function s1(n){return(e,t,i)=>!i[0].length||(n(Ds(i.input,i.index))!=te.Word||n(Ps(i.input,i.index))!=te.Word)&&(n(Ps(i.input,i.index+i[0].length))!=te.Word||n(Ds(i.input,i.index+i[0].length))!=te.Word)}class r1 extends $u{nextMatch(e,t,i){let s=ui(this.spec,e,i,e.doc.length).next();return s.done&&(s=ui(this.spec,e,0,t).next()),s.done?null:s.value}prevMatchInRange(e,t,i){for(let s=1;;s++){let r=Math.max(t,i-s*1e4),o=ui(this.spec,e,r,i),l=null;for(;!o.next().done;)l=o.value;if(l&&(r==t||l.from>r+10))return l;if(r==t)return null}}prevMatch(e,t,i){return this.prevMatchInRange(e,0,t)||this.prevMatchInRange(e,i,e.doc.length)}getReplacement(e){return this.spec.unquote(this.spec.replace).replace(/\$([$&]|\d+)/g,(t,i)=>{if(i=="&")return e.match[0];if(i=="$")return"$";for(let s=i.length;s>0;s--){let r=+i.slice(0,s);if(r>0&&r<e.match.length)return e.match[r]+i.slice(s)}return t})}matchAll(e,t){let i=ui(this.spec,e,0,e.doc.length),s=[];for(;!i.next().done;){if(s.length>=t)return null;s.push(i.value)}return s}highlight(e,t,i,s){let r=ui(this.spec,e,Math.max(0,t-250),Math.min(i+250,e.doc.length));for(;!r.next().done;)s(r.value.from,r.value.to)}}const dn=_.define(),Ol=_.define(),_t=ue.define({create(n){return new Br(Fo(n).create(),null)},update(n,e){for(let t of e.effects)t.is(dn)?n=new Br(t.value.create(),n.panel):t.is(Ol)&&(n=new Br(n.query,t.value?kl:null));return n},provide:n=>ln.from(n,e=>e.panel)});class Br{constructor(e,t){this.query=e,this.panel=t}}const o1=W.mark({class:"cm-searchMatch"}),l1=W.mark({class:"cm-searchMatch cm-searchMatch-selected"}),a1=ne.fromClass(class{constructor(n){this.view=n,this.decorations=this.highlight(n.state.field(_t))}update(n){let e=n.state.field(_t);(e!=n.startState.field(_t)||n.docChanged||n.selectionSet||n.viewportChanged)&&(this.decorations=this.highlight(e))}highlight({query:n,panel:e}){if(!e||!n.spec.valid)return W.none;let{view:t}=this,i=new At;for(let s=0,r=t.visibleRanges,o=r.length;s<o;s++){let{from:l,to:a}=r[s];for(;s<o-1&&a>r[s+1].from-2*250;)a=r[++s].to;n.highlight(t.state,l,a,(h,c)=>{let f=t.state.selection.ranges.some(u=>u.from==h&&u.to==c);i.add(h,c,f?l1:o1)})}return i.finish()}},{decorations:n=>n.decorations});function kn(n){return e=>{let t=e.state.field(_t,!1);return t&&t.query.spec.valid?n(e,t):zu(e)}}const Ns=kn((n,{query:e})=>{let{to:t}=n.state.selection.main,i=e.nextMatch(n.state,t,t);if(!i)return!1;let s=E.single(i.from,i.to),r=n.state.facet(Ii);return n.dispatch({selection:s,effects:[Cl(n,i),r.scrollToMatch(s.main,n)],userEvent:"select.search"}),Hu(n),!0}),Rs=kn((n,{query:e})=>{let{state:t}=n,{from:i}=t.selection.main,s=e.prevMatch(t,i,i);if(!s)return!1;let r=E.single(s.from,s.to),o=n.state.facet(Ii);return n.dispatch({selection:r,effects:[Cl(n,s),o.scrollToMatch(r.main,n)],userEvent:"select.search"}),Hu(n),!0}),h1=kn((n,{query:e})=>{let t=e.matchAll(n.state,1e3);return!t||!t.length?!1:(n.dispatch({selection:E.create(t.map(i=>E.range(i.from,i.to))),userEvent:"select.search.matches"}),!0)}),c1=({state:n,dispatch:e})=>{let t=n.selection;if(t.ranges.length>1||t.main.empty)return!1;let{from:i,to:s}=t.main,r=[],o=0;for(let l=new Pi(n.doc,n.sliceDoc(i,s));!l.next().done;){if(r.length>1e3)return!1;l.value.from==i&&(o=r.length),r.push(E.range(l.value.from,l.value.to))}return e(n.update({selection:E.create(r,o),userEvent:"select.search.matches"})),!0},Dh=kn((n,{query:e})=>{let{state:t}=n,{from:i,to:s}=t.selection.main;if(t.readOnly)return!1;let r=e.nextMatch(t,i,i);if(!r)return!1;let o=r,l=[],a,h,c=[];o.from==i&&o.to==s&&(h=t.toText(e.getReplacement(o)),l.push({from:o.from,to:o.to,insert:h}),o=e.nextMatch(t,o.from,o.to),c.push(R.announce.of(t.phrase("replaced match on line $",t.doc.lineAt(i).number)+".")));let f=n.state.changes(l);return o&&(a=E.single(o.from,o.to).map(f),c.push(Cl(n,o)),c.push(t.facet(Ii).scrollToMatch(a.main,n))),n.dispatch({changes:f,selection:a,effects:c,userEvent:"input.replace"}),!0}),f1=kn((n,{query:e})=>{if(n.state.readOnly)return!1;let t=e.matchAll(n.state,1e9).map(s=>{let{from:r,to:o}=s;return{from:r,to:o,insert:e.getReplacement(s)}});if(!t.length)return!1;let i=n.state.phrase("replaced $ matches",t.length)+".";return n.dispatch({changes:t,effects:R.announce.of(i),userEvent:"input.replace.all"}),!0});function kl(n){return n.state.facet(Ii).createPanel(n)}function Fo(n,e){var t,i,s,r,o;let l=n.selection.main,a=l.empty||l.to>l.from+100?"":n.sliceDoc(l.from,l.to);if(e&&!a)return e;let h=n.facet(Ii);return new _u({search:((t=e==null?void 0:e.literal)!==null&&t!==void 0?t:h.literal)?a:a.replace(/\n/g,"\\n"),caseSensitive:(i=e==null?void 0:e.caseSensitive)!==null&&i!==void 0?i:h.caseSensitive,literal:(s=e==null?void 0:e.literal)!==null&&s!==void 0?s:h.literal,regexp:(r=e==null?void 0:e.regexp)!==null&&r!==void 0?r:h.regexp,wholeWord:(o=e==null?void 0:e.wholeWord)!==null&&o!==void 0?o:h.wholeWord})}function qu(n){let e=on(n,kl);return e&&e.dom.querySelector("[main-field]")}function Hu(n){let e=qu(n);e&&e==n.root.activeElement&&e.select()}const zu=n=>{let e=n.state.field(_t,!1);if(e&&e.panel){let t=qu(n);if(t&&t!=n.root.activeElement){let i=Fo(n.state,e.query.spec);i.valid&&n.dispatch({effects:dn.of(i)}),t.focus(),t.select()}}else n.dispatch({effects:[Ol.of(!0),e?dn.of(Fo(n.state,e.query.spec)):_.appendConfig.of(m1)]});return!0},ju=n=>{let e=n.state.field(_t,!1);if(!e||!e.panel)return!1;let t=on(n,kl);return t&&t.dom.contains(n.root.activeElement)&&n.focus(),n.dispatch({effects:Ol.of(!1)}),!0},u1=[{key:"Mod-f",run:zu,scope:"editor search-panel"},{key:"F3",run:Ns,shift:Rs,scope:"editor search-panel",preventDefault:!0},{key:"Mod-g",run:Ns,shift:Rs,scope:"editor search-panel",preventDefault:!0},{key:"Escape",run:ju,scope:"editor search-panel"},{key:"Mod-Shift-l",run:c1},{key:"Mod-Alt-g",run:Hb},{key:"Mod-d",run:t1,preventDefault:!0}];class d1{constructor(e){this.view=e;let t=this.query=e.state.field(_t).query.spec;this.commit=this.commit.bind(this),this.searchField=Q("input",{value:t.search,placeholder:$e(e,"Find"),"aria-label":$e(e,"Find"),class:"cm-textfield",name:"search",form:"","main-field":"true",onchange:this.commit,onkeyup:this.commit}),this.replaceField=Q("input",{value:t.replace,placeholder:$e(e,"Replace"),"aria-label":$e(e,"Replace"),class:"cm-textfield",name:"replace",form:"",onchange:this.commit,onkeyup:this.commit}),this.caseField=Q("input",{type:"checkbox",name:"case",form:"",checked:t.caseSensitive,onchange:this.commit}),this.reField=Q("input",{type:"checkbox",name:"re",form:"",checked:t.regexp,onchange:this.commit}),this.wordField=Q("input",{type:"checkbox",name:"word",form:"",checked:t.wholeWord,onchange:this.commit});function i(s,r,o){return Q("button",{class:"cm-button",name:s,onclick:r,type:"button"},o)}this.dom=Q("div",{onkeydown:s=>this.keydown(s),class:"cm-search"},[this.searchField,i("next",()=>Ns(e),[$e(e,"next")]),i("prev",()=>Rs(e),[$e(e,"previous")]),i("select",()=>h1(e),[$e(e,"all")]),Q("label",null,[this.caseField,$e(e,"match case")]),Q("label",null,[this.reField,$e(e,"regexp")]),Q("label",null,[this.wordField,$e(e,"by word")]),...e.state.readOnly?[]:[Q("br"),this.replaceField,i("replace",()=>Dh(e),[$e(e,"replace")]),i("replaceAll",()=>f1(e),[$e(e,"replace all")])],Q("button",{name:"close",onclick:()=>ju(e),"aria-label":$e(e,"close"),type:"button"},["×"])])}commit(){let e=new _u({search:this.searchField.value,caseSensitive:this.caseField.checked,regexp:this.reField.checked,wholeWord:this.wordField.checked,replace:this.replaceField.value});e.eq(this.query)||(this.query=e,this.view.dispatch({effects:dn.of(e)}))}keydown(e){Sm(this.view,e,"search-panel")?e.preventDefault():e.keyCode==13&&e.target==this.searchField?(e.preventDefault(),(e.shiftKey?Rs:Ns)(this.view)):e.keyCode==13&&e.target==this.replaceField&&(e.preventDefault(),Dh(this.view))}update(e){for(let t of e.transactions)for(let i of t.effects)i.is(dn)&&!i.value.eq(this.query)&&this.setQuery(i.value)}setQuery(e){this.query=e,this.searchField.value=e.search,this.replaceField.value=e.replace,this.caseField.checked=e.caseSensitive,this.reField.checked=e.regexp,this.wordField.checked=e.wholeWord}mount(){this.searchField.select()}get pos(){return 80}get top(){return this.view.state.facet(Ii).top}}function $e(n,e){return n.state.phrase(e)}const Kn=30,Un=/[\s\.,:;?!]/;function Cl(n,{from:e,to:t}){let i=n.state.doc.lineAt(e),s=n.state.doc.lineAt(t).to,r=Math.max(i.from,e-Kn),o=Math.min(s,t+Kn),l=n.state.sliceDoc(r,o);if(r!=i.from){for(let a=0;a<Kn;a++)if(!Un.test(l[a+1])&&Un.test(l[a])){l=l.slice(a);break}}if(o!=s){for(let a=l.length-1;a>l.length-Kn;a--)if(!Un.test(l[a-1])&&Un.test(l[a])){l=l.slice(0,a);break}}return R.announce.of(`${n.state.phrase("current match")}. ${l} ${n.state.phrase("on line")} ${i.number}.`)}const p1=R.baseTheme({".cm-panel.cm-search":{padding:"2px 6px 4px",position:"relative","& [name=close]":{position:"absolute",top:"0",right:"4px",backgroundColor:"inherit",border:"none",font:"inherit",padding:0,margin:0},"& input, & button, & label":{margin:".2em .6em .2em 0"},"& input[type=checkbox]":{marginRight:".2em"},"& label":{fontSize:"80%",whiteSpace:"pre"}},"&light .cm-searchMatch":{backgroundColor:"#ffff0054"},"&dark .cm-searchMatch":{backgroundColor:"#00ffff8a"},"&light .cm-searchMatch-selected":{backgroundColor:"#ff6a0054"},"&dark .cm-searchMatch-selected":{backgroundColor:"#ff00ff8a"}}),m1=[_t,li.low(a1),p1];class Ku{constructor(e,t,i,s){this.state=e,this.pos=t,this.explicit=i,this.view=s,this.abortListeners=[],this.abortOnDocChange=!1}tokenBefore(e){let t=ge(this.state).resolveInner(this.pos,-1);for(;t&&e.indexOf(t.name)<0;)t=t.parent;return t?{from:t.from,to:this.pos,text:this.state.sliceDoc(t.from,this.pos),type:t.type}:null}matchBefore(e){let t=this.state.doc.lineAt(this.pos),i=Math.max(t.from,this.pos-250),s=t.text.slice(i-t.from,this.pos-t.from),r=s.search(Uu(e,!1));return r<0?null:{from:i+r,to:this.pos,text:s.slice(r)}}get aborted(){return this.abortListeners==null}addEventListener(e,t,i){e=="abort"&&this.abortListeners&&(this.abortListeners.push(t),i&&i.onDocChange&&(this.abortOnDocChange=!0))}}function Ph(n){let e=Object.keys(n).join(""),t=/\w/.test(e);return t&&(e=e.replace(/\w/g,"")),`[${t?"\\w":""}${e.replace(/[^\w\s]/g,"\\$&")}]`}function g1(n){let e=Object.create(null),t=Object.create(null);for(let{label:s}of n){e[s[0]]=!0;for(let r=1;r<s.length;r++)t[s[r]]=!0}let i=Ph(e)+Ph(t)+"*$";return[new RegExp("^"+i),new RegExp(i)]}function y1(n){let e=n.map(s=>typeof s=="string"?{label:s}:s),[t,i]=e.every(s=>/^\w+$/.test(s.label))?[/\w*$/,/\w+$/]:g1(e);return s=>{let r=s.matchBefore(i);return r||s.explicit?{from:r?r.from:s.pos,options:e,validFor:t}:null}}class Nh{constructor(e,t,i,s){this.completion=e,this.source=t,this.match=i,this.score=s}}function ti(n){return n.selection.main.from}function Uu(n,e){var t;let{source:i}=n,s=e&&i[0]!="^",r=i[i.length-1]!="$";return!s&&!r?n:new RegExp(`${s?"^":""}(?:${i})${r?"$":""}`,(t=n.flags)!==null&&t!==void 0?t:n.ignoreCase?"i":"")}const Gu=Mt.define();function b1(n,e,t,i){let{main:s}=n.selection,r=t-s.from,o=i-s.from;return Object.assign(Object.assign({},n.changeByRange(l=>{if(l!=s&&t!=i&&n.sliceDoc(l.from+r,l.from+o)!=n.sliceDoc(t,i))return{range:l};let a=n.toText(e);return{changes:{from:l.from+r,to:i==s.from?l.to:l.from+o,insert:a},range:E.cursor(l.from+r+a.length)}})),{scrollIntoView:!0,userEvent:"input.complete"})}const Rh=new WeakMap;function x1(n){if(!Array.isArray(n))return n;let e=Rh.get(n);return e||Rh.set(n,e=y1(n)),e}const Bs=_.define(),pn=_.define();class v1{constructor(e){this.pattern=e,this.chars=[],this.folded=[],this.any=[],this.precise=[],this.byWord=[],this.score=0,this.matched=[];for(let t=0;t<e.length;){let i=Ie(e,t),s=ft(i);this.chars.push(i);let r=e.slice(t,t+s),o=r.toUpperCase();this.folded.push(Ie(o==r?r.toLowerCase():o,0)),t+=s}this.astral=e.length!=this.chars.length}ret(e,t){return this.score=e,this.matched=t,this}match(e){if(this.pattern.length==0)return this.ret(-100,[]);if(e.length<this.pattern.length)return null;let{chars:t,folded:i,any:s,precise:r,byWord:o}=this;if(t.length==1){let O=Ie(e,0),v=ft(O),g=v==e.length?0:-100;if(O!=t[0])if(O==i[0])g+=-200;else return null;return this.ret(g,[0,v])}let l=e.indexOf(this.pattern);if(l==0)return this.ret(e.length==this.pattern.length?0:-100,[0,this.pattern.length]);let a=t.length,h=0;if(l<0){for(let O=0,v=Math.min(e.length,200);O<v&&h<a;){let g=Ie(e,O);(g==t[h]||g==i[h])&&(s[h++]=O),O+=ft(g)}if(h<a)return null}let c=0,f=0,u=!1,d=0,m=-1,x=-1,S=/[a-z]/.test(e),y=!0;for(let O=0,v=Math.min(e.length,200),g=0;O<v&&f<a;){let k=Ie(e,O);l<0&&(c<a&&k==t[c]&&(r[c++]=O),d<a&&(k==t[d]||k==i[d]?(d==0&&(m=O),x=O+1,d++):d=0));let T,M=k<255?k>=48&&k<=57||k>=97&&k<=122?2:k>=65&&k<=90?1:0:(T=qo(k))!=T.toLowerCase()?1:T!=T.toUpperCase()?2:0;(!O||M==1&&S||g==0&&M!=0)&&(t[f]==k||i[f]==k&&(u=!0)?o[f++]=O:o.length&&(y=!1)),g=M,O+=ft(k)}return f==a&&o[0]==0&&y?this.result(-100+(u?-200:0),o,e):d==a&&m==0?this.ret(-200-e.length+(x==e.length?0:-100),[0,x]):l>-1?this.ret(-700-e.length,[l,l+this.pattern.length]):d==a?this.ret(-900-e.length,[m,x]):f==a?this.result(-100+(u?-200:0)+-700+(y?0:-1100),o,e):t.length==2?null:this.result((s[0]?-700:0)+-200+-1100,s,e)}result(e,t,i){let s=[],r=0;for(let o of t){let l=o+(this.astral?ft(Ie(i,o)):1);r&&s[r-1]==o?s[r-1]=l:(s[r++]=o,s[r++]=l)}return this.ret(e-i.length,s)}}class w1{constructor(e){this.pattern=e,this.matched=[],this.score=0,this.folded=e.toLowerCase()}match(e){if(e.length<this.pattern.length)return null;let t=e.slice(0,this.pattern.length),i=t==this.pattern?0:t.toLowerCase()==this.folded?-200:null;return i==null?null:(this.matched=[0,t.length],this.score=i+(e.length==this.pattern.length?0:-100),this)}}const me=F.define({combine(n){return nt(n,{activateOnTyping:!0,activateOnCompletion:()=>!1,activateOnTypingDelay:100,selectOnOpen:!0,override:null,closeOnBlur:!0,maxRenderedOptions:100,defaultKeymap:!0,tooltipClass:()=>"",optionClass:()=>"",aboveCursor:!1,icons:!0,addToOptions:[],positionInfo:S1,filterStrict:!1,compareCompletions:(e,t)=>e.label.localeCompare(t.label),interactionDelay:75,updateSyncTime:100},{defaultKeymap:(e,t)=>e&&t,closeOnBlur:(e,t)=>e&&t,icons:(e,t)=>e&&t,tooltipClass:(e,t)=>i=>Bh(e(i),t(i)),optionClass:(e,t)=>i=>Bh(e(i),t(i)),addToOptions:(e,t)=>e.concat(t),filterStrict:(e,t)=>e||t})}});function Bh(n,e){return n?e?n+" "+e:n:e}function S1(n,e,t,i,s,r){let o=n.textDirection==ee.RTL,l=o,a=!1,h="top",c,f,u=e.left-s.left,d=s.right-e.right,m=i.right-i.left,x=i.bottom-i.top;if(l&&u<Math.min(m,d)?l=!1:!l&&d<Math.min(m,u)&&(l=!0),m<=(l?u:d))c=Math.max(s.top,Math.min(t.top,s.bottom-x))-e.top,f=Math.min(400,l?u:d);else{a=!0,f=Math.min(400,(o?e.right:s.right-e.left)-30);let O=s.bottom-e.bottom;O>=x||O>e.top?c=t.bottom-e.top:(h="bottom",c=e.bottom-t.top)}let S=(e.bottom-e.top)/r.offsetHeight,y=(e.right-e.left)/r.offsetWidth;return{style:`${h}: ${c/S}px; max-width: ${f/y}px`,class:"cm-completionInfo-"+(a?o?"left-narrow":"right-narrow":l?"left":"right")}}function O1(n){let e=n.addToOptions.slice();return n.icons&&e.push({render(t){let i=document.createElement("div");return i.classList.add("cm-completionIcon"),t.type&&i.classList.add(...t.type.split(/\s+/g).map(s=>"cm-completionIcon-"+s)),i.setAttribute("aria-hidden","true"),i},position:20}),e.push({render(t,i,s,r){let o=document.createElement("span");o.className="cm-completionLabel";let l=t.displayLabel||t.label,a=0;for(let h=0;h<r.length;){let c=r[h++],f=r[h++];c>a&&o.appendChild(document.createTextNode(l.slice(a,c)));let u=o.appendChild(document.createElement("span"));u.appendChild(document.createTextNode(l.slice(c,f))),u.className="cm-completionMatchedText",a=f}return a<l.length&&o.appendChild(document.createTextNode(l.slice(a))),o},position:50},{render(t){if(!t.detail)return null;let i=document.createElement("span");return i.className="cm-completionDetail",i.textContent=t.detail,i},position:80}),e.sort((t,i)=>t.position-i.position).map(t=>t.render)}function Ir(n,e,t){if(n<=t)return{from:0,to:n};if(e<0&&(e=0),e<=n>>1){let s=Math.floor(e/t);return{from:s*t,to:(s+1)*t}}let i=Math.floor((n-e)/t);return{from:n-(i+1)*t,to:n-i*t}}class k1{constructor(e,t,i){this.view=e,this.stateField=t,this.applyCompletion=i,this.info=null,this.infoDestroy=null,this.placeInfoReq={read:()=>this.measureInfo(),write:a=>this.placeInfo(a),key:this},this.space=null,this.currentClass="";let s=e.state.field(t),{options:r,selected:o}=s.open,l=e.state.facet(me);this.optionContent=O1(l),this.optionClass=l.optionClass,this.tooltipClass=l.tooltipClass,this.range=Ir(r.length,o,l.maxRenderedOptions),this.dom=document.createElement("div"),this.dom.className="cm-tooltip-autocomplete",this.updateTooltipClass(e.state),this.dom.addEventListener("mousedown",a=>{let{options:h}=e.state.field(t).open;for(let c=a.target,f;c&&c!=this.dom;c=c.parentNode)if(c.nodeName=="LI"&&(f=/-(\d+)$/.exec(c.id))&&+f[1]<h.length){this.applyCompletion(e,h[+f[1]]),a.preventDefault();return}}),this.dom.addEventListener("focusout",a=>{let h=e.state.field(this.stateField,!1);h&&h.tooltip&&e.state.facet(me).closeOnBlur&&a.relatedTarget!=e.contentDOM&&e.dispatch({effects:pn.of(null)})}),this.showOptions(r,s.id)}mount(){this.updateSel()}showOptions(e,t){this.list&&this.list.remove(),this.list=this.dom.appendChild(this.createListBox(e,t,this.range)),this.list.addEventListener("scroll",()=>{this.info&&this.view.requestMeasure(this.placeInfoReq)})}update(e){var t;let i=e.state.field(this.stateField),s=e.startState.field(this.stateField);if(this.updateTooltipClass(e.state),i!=s){let{options:r,selected:o,disabled:l}=i.open;(!s.open||s.open.options!=r)&&(this.range=Ir(r.length,o,e.state.facet(me).maxRenderedOptions),this.showOptions(r,i.id)),this.updateSel(),l!=((t=s.open)===null||t===void 0?void 0:t.disabled)&&this.dom.classList.toggle("cm-tooltip-autocomplete-disabled",!!l)}}updateTooltipClass(e){let t=this.tooltipClass(e);if(t!=this.currentClass){for(let i of this.currentClass.split(" "))i&&this.dom.classList.remove(i);for(let i of t.split(" "))i&&this.dom.classList.add(i);this.currentClass=t}}positioned(e){this.space=e,this.info&&this.view.requestMeasure(this.placeInfoReq)}updateSel(){let e=this.view.state.field(this.stateField),t=e.open;if((t.selected>-1&&t.selected<this.range.from||t.selected>=this.range.to)&&(this.range=Ir(t.options.length,t.selected,this.view.state.facet(me).maxRenderedOptions),this.showOptions(t.options,e.id)),this.updateSelectedOption(t.selected)){this.destroyInfo();let{completion:i}=t.options[t.selected],{info:s}=i;if(!s)return;let r=typeof s=="string"?document.createTextNode(s):s(i);if(!r)return;"then"in r?r.then(o=>{o&&this.view.state.field(this.stateField,!1)==e&&this.addInfoPane(o,i)}).catch(o=>De(this.view.state,o,"completion info")):this.addInfoPane(r,i)}}addInfoPane(e,t){this.destroyInfo();let i=this.info=document.createElement("div");if(i.className="cm-tooltip cm-completionInfo",e.nodeType!=null)i.appendChild(e),this.infoDestroy=null;else{let{dom:s,destroy:r}=e;i.appendChild(s),this.infoDestroy=r||null}this.dom.appendChild(i),this.view.requestMeasure(this.placeInfoReq)}updateSelectedOption(e){let t=null;for(let i=this.list.firstChild,s=this.range.from;i;i=i.nextSibling,s++)i.nodeName!="LI"||!i.id?s--:s==e?i.hasAttribute("aria-selected")||(i.setAttribute("aria-selected","true"),t=i):i.hasAttribute("aria-selected")&&i.removeAttribute("aria-selected");return t&&A1(this.list,t),t}measureInfo(){let e=this.dom.querySelector("[aria-selected]");if(!e||!this.info)return null;let t=this.dom.getBoundingClientRect(),i=this.info.getBoundingClientRect(),s=e.getBoundingClientRect(),r=this.space;if(!r){let o=this.dom.ownerDocument.documentElement;r={left:0,top:0,right:o.clientWidth,bottom:o.clientHeight}}return s.top>Math.min(r.bottom,t.bottom)-10||s.bottom<Math.max(r.top,t.top)+10?null:this.view.state.facet(me).positionInfo(this.view,t,s,i,r,this.dom)}placeInfo(e){this.info&&(e?(e.style&&(this.info.style.cssText=e.style),this.info.className="cm-tooltip cm-completionInfo "+(e.class||"")):this.info.style.cssText="top: -1e6px")}createListBox(e,t,i){const s=document.createElement("ul");s.id=t,s.setAttribute("role","listbox"),s.setAttribute("aria-expanded","true"),s.setAttribute("aria-label",this.view.state.phrase("Completions")),s.addEventListener("mousedown",o=>{o.target==s&&o.preventDefault()});let r=null;for(let o=i.from;o<i.to;o++){let{completion:l,match:a}=e[o],{section:h}=l;if(h){let u=typeof h=="string"?h:h.name;if(u!=r&&(o>i.from||i.from==0))if(r=u,typeof h!="string"&&h.header)s.appendChild(h.header(h));else{let d=s.appendChild(document.createElement("completion-section"));d.textContent=u}}const c=s.appendChild(document.createElement("li"));c.id=t+"-"+o,c.setAttribute("role","option");let f=this.optionClass(l);f&&(c.className=f);for(let u of this.optionContent){let d=u(l,this.view.state,this.view,a);d&&c.appendChild(d)}}return i.from&&s.classList.add("cm-completionListIncompleteTop"),i.to<e.length&&s.classList.add("cm-completionListIncompleteBottom"),s}destroyInfo(){this.info&&(this.infoDestroy&&this.infoDestroy(),this.info.remove(),this.info=null)}destroy(){this.destroyInfo()}}function C1(n,e){return t=>new k1(t,n,e)}function A1(n,e){let t=n.getBoundingClientRect(),i=e.getBoundingClientRect(),s=t.height/n.offsetHeight;i.top<t.top?n.scrollTop-=(t.top-i.top)/s:i.bottom>t.bottom&&(n.scrollTop+=(i.bottom-t.bottom)/s)}function Ih(n){return(n.boost||0)*100+(n.apply?10:0)+(n.info?5:0)+(n.type?1:0)}function T1(n,e){let t=[],i=null,s=h=>{t.push(h);let{section:c}=h.completion;if(c){i||(i=[]);let f=typeof c=="string"?c:c.name;i.some(u=>u.name==f)||i.push(typeof c=="string"?{name:f}:c)}},r=e.facet(me);for(let h of n)if(h.hasResult()){let c=h.result.getMatch;if(h.result.filter===!1)for(let f of h.result.options)s(new Nh(f,h.source,c?c(f):[],1e9-t.length));else{let f=e.sliceDoc(h.from,h.to),u,d=r.filterStrict?new w1(f):new v1(f);for(let m of h.result.options)if(u=d.match(m.label)){let x=m.displayLabel?c?c(m,u.matched):[]:u.matched;s(new Nh(m,h.source,x,u.score+(m.boost||0)))}}}if(i){let h=Object.create(null),c=0,f=(u,d)=>{var m,x;return((m=u.rank)!==null&&m!==void 0?m:1e9)-((x=d.rank)!==null&&x!==void 0?x:1e9)||(u.name<d.name?-1:1)};for(let u of i.sort(f))c-=1e5,h[u.name]=c;for(let u of t){let{section:d}=u.completion;d&&(u.score+=h[typeof d=="string"?d:d.name])}}let o=[],l=null,a=r.compareCompletions;for(let h of t.sort((c,f)=>f.score-c.score||a(c.completion,f.completion))){let c=h.completion;!l||l.label!=c.label||l.detail!=c.detail||l.type!=null&&c.type!=null&&l.type!=c.type||l.apply!=c.apply||l.boost!=c.boost?o.push(h):Ih(h.completion)>Ih(l)&&(o[o.length-1]=h),l=h.completion}return o}class yi{constructor(e,t,i,s,r,o){this.options=e,this.attrs=t,this.tooltip=i,this.timestamp=s,this.selected=r,this.disabled=o}setSelected(e,t){return e==this.selected||e>=this.options.length?this:new yi(this.options,Lh(t,e),this.tooltip,this.timestamp,e,this.disabled)}static build(e,t,i,s,r,o){if(s&&!o&&e.some(h=>h.isPending))return s.setDisabled();let l=T1(e,t);if(!l.length)return s&&e.some(h=>h.isPending)?s.setDisabled():null;let a=t.facet(me).selectOnOpen?0:-1;if(s&&s.selected!=a&&s.selected!=-1){let h=s.options[s.selected].completion;for(let c=0;c<l.length;c++)if(l[c].completion==h){a=c;break}}return new yi(l,Lh(i,a),{pos:e.reduce((h,c)=>c.hasResult()?Math.min(h,c.from):h,1e8),create:R1,above:r.aboveCursor},s?s.timestamp:Date.now(),a,!1)}map(e){return new yi(this.options,this.attrs,Object.assign(Object.assign({},this.tooltip),{pos:e.mapPos(this.tooltip.pos)}),this.timestamp,this.selected,this.disabled)}setDisabled(){return new yi(this.options,this.attrs,this.tooltip,this.timestamp,this.selected,!0)}}class Is{constructor(e,t,i){this.active=e,this.id=t,this.open=i}static start(){return new Is(P1,"cm-ac-"+Math.floor(Math.random()*2e6).toString(36),null)}update(e){let{state:t}=e,i=t.facet(me),r=(i.override||t.languageDataAt("autocomplete",ti(t)).map(x1)).map(a=>(this.active.find(c=>c.source==a)||new Xe(a,this.active.some(c=>c.state!=0)?1:0)).update(e,i));r.length==this.active.length&&r.every((a,h)=>a==this.active[h])&&(r=this.active);let o=this.open,l=e.effects.some(a=>a.is(Al));o&&e.docChanged&&(o=o.map(e.changes)),e.selection||r.some(a=>a.hasResult()&&e.changes.touchesRange(a.from,a.to))||!M1(r,this.active)||l?o=yi.build(r,t,this.id,o,i,l):o&&o.disabled&&!r.some(a=>a.isPending)&&(o=null),!o&&r.every(a=>!a.isPending)&&r.some(a=>a.hasResult())&&(r=r.map(a=>a.hasResult()?new Xe(a.source,0):a));for(let a of e.effects)a.is(Qu)&&(o=o&&o.setSelected(a.value,this.id));return r==this.active&&o==this.open?this:new Is(r,this.id,o)}get tooltip(){return this.open?this.open.tooltip:null}get attrs(){return this.open?this.open.attrs:this.active.length?E1:D1}}function M1(n,e){if(n==e)return!0;for(let t=0,i=0;;){for(;t<n.length&&!n[t].hasResult();)t++;for(;i<e.length&&!e[i].hasResult();)i++;let s=t==n.length,r=i==e.length;if(s||r)return s==r;if(n[t++].result!=e[i++].result)return!1}}const E1={"aria-autocomplete":"list"},D1={};function Lh(n,e){let t={"aria-autocomplete":"list","aria-haspopup":"listbox","aria-controls":n};return e>-1&&(t["aria-activedescendant"]=n+"-"+e),t}const P1=[];function Xu(n,e){if(n.isUserEvent("input.complete")){let i=n.annotation(Gu);if(i&&e.activateOnCompletion(i))return 12}let t=n.isUserEvent("input.type");return t&&e.activateOnTyping?5:t?1:n.isUserEvent("delete.backward")?2:n.selection?8:n.docChanged?16:0}class Xe{constructor(e,t,i=!1){this.source=e,this.state=t,this.explicit=i}hasResult(){return!1}get isPending(){return this.state==1}update(e,t){let i=Xu(e,t),s=this;(i&8||i&16&&this.touches(e))&&(s=new Xe(s.source,0)),i&4&&s.state==0&&(s=new Xe(this.source,1)),s=s.updateFor(e,i);for(let r of e.effects)if(r.is(Bs))s=new Xe(s.source,1,r.value);else if(r.is(pn))s=new Xe(s.source,0);else if(r.is(Al))for(let o of r.value)o.source==s.source&&(s=o);return s}updateFor(e,t){return this.map(e.changes)}map(e){return this}touches(e){return e.changes.touchesRange(ti(e.state))}}class Oi extends Xe{constructor(e,t,i,s,r,o){super(e,3,t),this.limit=i,this.result=s,this.from=r,this.to=o}hasResult(){return!0}updateFor(e,t){var i;if(!(t&3))return this.map(e.changes);let s=this.result;s.map&&!e.changes.empty&&(s=s.map(s,e.changes));let r=e.changes.mapPos(this.from),o=e.changes.mapPos(this.to,1),l=ti(e.state);if(l>o||!s||t&2&&(ti(e.startState)==this.from||l<this.limit))return new Xe(this.source,t&4?1:0);let a=e.changes.mapPos(this.limit);return N1(s.validFor,e.state,r,o)?new Oi(this.source,this.explicit,a,s,r,o):s.update&&(s=s.update(s,r,o,new Ku(e.state,l,!1)))?new Oi(this.source,this.explicit,a,s,s.from,(i=s.to)!==null&&i!==void 0?i:ti(e.state)):new Xe(this.source,1,this.explicit)}map(e){return e.empty?this:(this.result.map?this.result.map(this.result,e):this.result)?new Oi(this.source,this.explicit,e.mapPos(this.limit),this.result,e.mapPos(this.from),e.mapPos(this.to,1)):new Xe(this.source,0)}touches(e){return e.changes.touchesRange(this.from,this.to)}}function N1(n,e,t,i){if(!n)return!1;let s=e.sliceDoc(t,i);return typeof n=="function"?n(s,t,i,e):Uu(n,!0).test(s)}const Al=_.define({map(n,e){return n.map(t=>t.map(e))}}),Qu=_.define(),Fe=ue.define({create(){return Is.start()},update(n,e){return n.update(e)},provide:n=>[Vs.from(n,e=>e.tooltip),R.contentAttributes.from(n,e=>e.attrs)]});function Tl(n,e){const t=e.completion.apply||e.completion.label;let i=n.state.field(Fe).active.find(s=>s.source==e.source);return i instanceof Oi?(typeof t=="string"?n.dispatch(Object.assign(Object.assign({},b1(n.state,t,i.from,i.to)),{annotations:Gu.of(e.completion)})):t(n,e.completion,i.from,i.to),!0):!1}const R1=C1(Fe,Tl);function Gn(n,e="option"){return t=>{let i=t.state.field(Fe,!1);if(!i||!i.open||i.open.disabled||Date.now()-i.open.timestamp<t.state.facet(me).interactionDelay)return!1;let s=1,r;e=="page"&&(r=yf(t,i.open.tooltip))&&(s=Math.max(2,Math.floor(r.dom.offsetHeight/r.dom.querySelector("li").offsetHeight)-1));let{length:o}=i.open.options,l=i.open.selected>-1?i.open.selected+s*(n?1:-1):n?0:o-1;return l<0?l=e=="page"?0:o-1:l>=o&&(l=e=="page"?o-1:0),t.dispatch({effects:Qu.of(l)}),!0}}const B1=n=>{let e=n.state.field(Fe,!1);return n.state.readOnly||!e||!e.open||e.open.selected<0||e.open.disabled||Date.now()-e.open.timestamp<n.state.facet(me).interactionDelay?!1:Tl(n,e.open.options[e.open.selected])},Fh=n=>n.state.field(Fe,!1)?(n.dispatch({effects:Bs.of(!0)}),!0):!1,I1=n=>{let e=n.state.field(Fe,!1);return!e||!e.active.some(t=>t.state!=0)?!1:(n.dispatch({effects:pn.of(null)}),!0)};class L1{constructor(e,t){this.active=e,this.context=t,this.time=Date.now(),this.updates=[],this.done=void 0}}const F1=50,V1=1e3,W1=ne.fromClass(class{constructor(n){this.view=n,this.debounceUpdate=-1,this.running=[],this.debounceAccept=-1,this.pendingStart=!1,this.composing=0;for(let e of n.state.field(Fe).active)e.isPending&&this.startQuery(e)}update(n){let e=n.state.field(Fe),t=n.state.facet(me);if(!n.selectionSet&&!n.docChanged&&n.startState.field(Fe)==e)return;let i=n.transactions.some(r=>{let o=Xu(r,t);return o&8||(r.selection||r.docChanged)&&!(o&3)});for(let r=0;r<this.running.length;r++){let o=this.running[r];if(i||o.context.abortOnDocChange&&n.docChanged||o.updates.length+n.transactions.length>F1&&Date.now()-o.time>V1){for(let l of o.context.abortListeners)try{l()}catch(a){De(this.view.state,a)}o.context.abortListeners=null,this.running.splice(r--,1)}else o.updates.push(...n.transactions)}this.debounceUpdate>-1&&clearTimeout(this.debounceUpdate),n.transactions.some(r=>r.effects.some(o=>o.is(Bs)))&&(this.pendingStart=!0);let s=this.pendingStart?50:t.activateOnTypingDelay;if(this.debounceUpdate=e.active.some(r=>r.isPending&&!this.running.some(o=>o.active.source==r.source))?setTimeout(()=>this.startUpdate(),s):-1,this.composing!=0)for(let r of n.transactions)r.isUserEvent("input.type")?this.composing=2:this.composing==2&&r.selection&&(this.composing=3)}startUpdate(){this.debounceUpdate=-1,this.pendingStart=!1;let{state:n}=this.view,e=n.field(Fe);for(let t of e.active)t.isPending&&!this.running.some(i=>i.active.source==t.source)&&this.startQuery(t);this.running.length&&e.open&&e.open.disabled&&(this.debounceAccept=setTimeout(()=>this.accept(),this.view.state.facet(me).updateSyncTime))}startQuery(n){let{state:e}=this.view,t=ti(e),i=new Ku(e,t,n.explicit,this.view),s=new L1(n,i);this.running.push(s),Promise.resolve(n.source(i)).then(r=>{s.context.aborted||(s.done=r||null,this.scheduleAccept())},r=>{this.view.dispatch({effects:pn.of(null)}),De(this.view.state,r)})}scheduleAccept(){this.running.every(n=>n.done!==void 0)?this.accept():this.debounceAccept<0&&(this.debounceAccept=setTimeout(()=>this.accept(),this.view.state.facet(me).updateSyncTime))}accept(){var n;this.debounceAccept>-1&&clearTimeout(this.debounceAccept),this.debounceAccept=-1;let e=[],t=this.view.state.facet(me),i=this.view.state.field(Fe);for(let s=0;s<this.running.length;s++){let r=this.running[s];if(r.done===void 0)continue;if(this.running.splice(s--,1),r.done){let l=ti(r.updates.length?r.updates[0].startState:this.view.state),a=Math.min(l,r.done.from+(r.active.explicit?0:1)),h=new Oi(r.active.source,r.active.explicit,a,r.done,r.done.from,(n=r.done.to)!==null&&n!==void 0?n:l);for(let c of r.updates)h=h.update(c,t);if(h.hasResult()){e.push(h);continue}}let o=i.active.find(l=>l.source==r.active.source);if(o&&o.isPending)if(r.done==null){let l=new Xe(r.active.source,0);for(let a of r.updates)l=l.update(a,t);l.isPending||e.push(l)}else this.startQuery(o)}(e.length||i.open&&i.open.disabled)&&this.view.dispatch({effects:Al.of(e)})}},{eventHandlers:{blur(n){let e=this.view.state.field(Fe,!1);if(e&&e.tooltip&&this.view.state.facet(me).closeOnBlur){let t=e.open&&yf(this.view,e.open.tooltip);(!t||!t.dom.contains(n.relatedTarget))&&setTimeout(()=>this.view.dispatch({effects:pn.of(null)}),10)}},compositionstart(){this.composing=1},compositionend(){this.composing==3&&setTimeout(()=>this.view.dispatch({effects:Bs.of(!1)}),20),this.composing=0}}}),_1=typeof navigator=="object"&&/Win/.test(navigator.platform),$1=li.highest(R.domEventHandlers({keydown(n,e){let t=e.state.field(Fe,!1);if(!t||!t.open||t.open.disabled||t.open.selected<0||n.key.length>1||n.ctrlKey&&!(_1&&n.altKey)||n.metaKey)return!1;let i=t.open.options[t.open.selected],s=t.active.find(o=>o.source==i.source),r=i.completion.commitCharacters||s.result.commitCharacters;return r&&r.indexOf(n.key)>-1&&Tl(e,i),!1}})),q1=R.baseTheme({".cm-tooltip.cm-tooltip-autocomplete":{"& > ul":{fontFamily:"monospace",whiteSpace:"nowrap",overflow:"hidden auto",maxWidth_fallback:"700px",maxWidth:"min(700px, 95vw)",minWidth:"250px",maxHeight:"10em",height:"100%",listStyle:"none",margin:0,padding:0,"& > li, & > completion-section":{padding:"1px 3px",lineHeight:1.2},"& > li":{overflowX:"hidden",textOverflow:"ellipsis",cursor:"pointer"},"& > completion-section":{display:"list-item",borderBottom:"1px solid silver",paddingLeft:"0.5em",opacity:.7}}},"&light .cm-tooltip-autocomplete ul li[aria-selected]":{background:"#17c",color:"white"},"&light .cm-tooltip-autocomplete-disabled ul li[aria-selected]":{background:"#777"},"&dark .cm-tooltip-autocomplete ul li[aria-selected]":{background:"#347",color:"white"},"&dark .cm-tooltip-autocomplete-disabled ul li[aria-selected]":{background:"#444"},".cm-completionListIncompleteTop:before, .cm-completionListIncompleteBottom:after":{content:'"···"',opacity:.5,display:"block",textAlign:"center"},".cm-tooltip.cm-completionInfo":{position:"absolute",padding:"3px 9px",width:"max-content",maxWidth:"400px",boxSizing:"border-box",whiteSpace:"pre-line"},".cm-completionInfo.cm-completionInfo-left":{right:"100%"},".cm-completionInfo.cm-completionInfo-right":{left:"100%"},".cm-completionInfo.cm-completionInfo-left-narrow":{right:"30px"},".cm-completionInfo.cm-completionInfo-right-narrow":{left:"30px"},"&light .cm-snippetField":{backgroundColor:"#00000022"},"&dark .cm-snippetField":{backgroundColor:"#ffffff22"},".cm-snippetFieldPosition":{verticalAlign:"text-top",width:0,height:"1.15em",display:"inline-block",margin:"0 -0.7px -.7em",borderLeft:"1.4px dotted #888"},".cm-completionMatchedText":{textDecoration:"underline"},".cm-completionDetail":{marginLeft:"0.5em",fontStyle:"italic"},".cm-completionIcon":{fontSize:"90%",width:".8em",display:"inline-block",textAlign:"center",paddingRight:".6em",opacity:"0.6",boxSizing:"content-box"},".cm-completionIcon-function, .cm-completionIcon-method":{"&:after":{content:"'ƒ'"}},".cm-completionIcon-class":{"&:after":{content:"'○'"}},".cm-completionIcon-interface":{"&:after":{content:"'◌'"}},".cm-completionIcon-variable":{"&:after":{content:"'𝑥'"}},".cm-completionIcon-constant":{"&:after":{content:"'𝐶'"}},".cm-completionIcon-type":{"&:after":{content:"'𝑡'"}},".cm-completionIcon-enum":{"&:after":{content:"'∪'"}},".cm-completionIcon-property":{"&:after":{content:"'□'"}},".cm-completionIcon-keyword":{"&:after":{content:"'🔑︎'"}},".cm-completionIcon-namespace":{"&:after":{content:"'▢'"}},".cm-completionIcon-text":{"&:after":{content:"'abc'",fontSize:"50%",verticalAlign:"middle"}}}),mn={brackets:["(","[","{","'",'"'],before:")]}:;>",stringPrefixes:[]},Zt=_.define({map(n,e){let t=e.mapPos(n,-1,Ee.TrackAfter);return t??void 0}}),Ml=new class extends ii{};Ml.startSide=1;Ml.endSide=-1;const Yu=ue.define({create(){return U.empty},update(n,e){if(n=n.map(e.changes),e.selection){let t=e.state.doc.lineAt(e.selection.main.head);n=n.update({filter:i=>i>=t.from&&i<=t.to})}for(let t of e.effects)t.is(Zt)&&(n=n.update({add:[Ml.range(t.value,t.value+1)]}));return n}});function H1(){return[j1,Yu]}const Lr="()[]{}<>«»»«［］｛｝";function Ju(n){for(let e=0;e<Lr.length;e+=2)if(Lr.charCodeAt(e)==n)return Lr.charAt(e+1);return qo(n<128?n:n+1)}function Zu(n,e){return n.languageDataAt("closeBrackets",e)[0]||mn}const z1=typeof navigator=="object"&&/Android\b/.test(navigator.userAgent),j1=R.inputHandler.of((n,e,t,i)=>{if((z1?n.composing:n.compositionStarted)||n.state.readOnly)return!1;let s=n.state.selection.main;if(i.length>2||i.length==2&&ft(Ie(i,0))==1||e!=s.from||t!=s.to)return!1;let r=G1(n.state,i);return r?(n.dispatch(r),!0):!1}),K1=({state:n,dispatch:e})=>{if(n.readOnly)return!1;let i=Zu(n,n.selection.main.head).brackets||mn.brackets,s=null,r=n.changeByRange(o=>{if(o.empty){let l=X1(n.doc,o.head);for(let a of i)if(a==l&&Xs(n.doc,o.head)==Ju(Ie(a,0)))return{changes:{from:o.head-a.length,to:o.head+a.length},range:E.cursor(o.head-a.length)}}return{range:s=o}});return s||e(n.update(r,{scrollIntoView:!0,userEvent:"delete.backward"})),!s},U1=[{key:"Backspace",run:K1}];function G1(n,e){let t=Zu(n,n.selection.main.head),i=t.brackets||mn.brackets;for(let s of i){let r=Ju(Ie(s,0));if(e==s)return r==s?J1(n,s,i.indexOf(s+s+s)>-1,t):Q1(n,s,r,t.before||mn.before);if(e==r&&ed(n,n.selection.main.from))return Y1(n,s,r)}return null}function ed(n,e){let t=!1;return n.field(Yu).between(0,n.doc.length,i=>{i==e&&(t=!0)}),t}function Xs(n,e){let t=n.sliceString(e,e+2);return t.slice(0,ft(Ie(t,0)))}function X1(n,e){let t=n.sliceString(e-2,e);return ft(Ie(t,0))==t.length?t:t.slice(1)}function Q1(n,e,t,i){let s=null,r=n.changeByRange(o=>{if(!o.empty)return{changes:[{insert:e,from:o.from},{insert:t,from:o.to}],effects:Zt.of(o.to+e.length),range:E.range(o.anchor+e.length,o.head+e.length)};let l=Xs(n.doc,o.head);return!l||/\s/.test(l)||i.indexOf(l)>-1?{changes:{insert:e+t,from:o.head},effects:Zt.of(o.head+e.length),range:E.cursor(o.head+e.length)}:{range:s=o}});return s?null:n.update(r,{scrollIntoView:!0,userEvent:"input.type"})}function Y1(n,e,t){let i=null,s=n.changeByRange(r=>r.empty&&Xs(n.doc,r.head)==t?{changes:{from:r.head,to:r.head+t.length,insert:t},range:E.cursor(r.head+t.length)}:i={range:r});return i?null:n.update(s,{scrollIntoView:!0,userEvent:"input.type"})}function J1(n,e,t,i){let s=i.stringPrefixes||mn.stringPrefixes,r=null,o=n.changeByRange(l=>{if(!l.empty)return{changes:[{insert:e,from:l.from},{insert:e,from:l.to}],effects:Zt.of(l.to+e.length),range:E.range(l.anchor+e.length,l.head+e.length)};let a=l.head,h=Xs(n.doc,a),c;if(h==e){if(Vh(n,a))return{changes:{insert:e+e,from:a},effects:Zt.of(a+e.length),range:E.cursor(a+e.length)};if(ed(n,a)){let u=t&&n.sliceDoc(a,a+e.length*3)==e+e+e?e+e+e:e;return{changes:{from:a,to:a+u.length,insert:u},range:E.cursor(a+u.length)}}}else{if(t&&n.sliceDoc(a-2*e.length,a)==e+e&&(c=Wh(n,a-2*e.length,s))>-1&&Vh(n,c))return{changes:{insert:e+e+e+e,from:a},effects:Zt.of(a+e.length),range:E.cursor(a+e.length)};if(n.charCategorizer(a)(h)!=te.Word&&Wh(n,a,s)>-1&&!Z1(n,a,e,s))return{changes:{insert:e+e,from:a},effects:Zt.of(a+e.length),range:E.cursor(a+e.length)}}return{range:r=l}});return r?null:n.update(o,{scrollIntoView:!0,userEvent:"input.type"})}function Vh(n,e){let t=ge(n).resolveInner(e+1);return t.parent&&t.from==e}function Z1(n,e,t,i){let s=ge(n).resolveInner(e,-1),r=i.reduce((o,l)=>Math.max(o,l.length),0);for(let o=0;o<5;o++){let l=n.sliceDoc(s.from,Math.min(s.to,s.from+t.length+r)),a=l.indexOf(t);if(!a||a>-1&&i.indexOf(l.slice(0,a))>-1){let c=s.firstChild;for(;c&&c.from==s.from&&c.to-c.from>t.length+a;){if(n.sliceDoc(c.to-t.length,c.to)==t)return!1;c=c.firstChild}return!0}let h=s.to==e&&s.parent;if(!h)break;s=h}return!1}function Wh(n,e,t){let i=n.charCategorizer(e);if(i(n.sliceDoc(e-1,e))!=te.Word)return e;for(let s of t){let r=e-s.length;if(n.sliceDoc(r,e)==s&&i(n.sliceDoc(r-1,r))!=te.Word)return r}return-1}function ex(n={}){return[$1,Fe,me.of(n),W1,tx,q1]}const td=[{key:"Ctrl-Space",run:Fh},{mac:"Alt-`",run:Fh},{key:"Escape",run:I1},{key:"ArrowDown",run:Gn(!0)},{key:"ArrowUp",run:Gn(!1)},{key:"PageDown",run:Gn(!0,"page")},{key:"PageUp",run:Gn(!1,"page")},{key:"Enter",run:B1}],tx=li.highest(vn.computeN([me],n=>n.facet(me).defaultKeymap?[td]:[])),ix=[nl(),Sg(),Wm(),yy(),W0(),Mm(),Rm(),j.allowMultipleSelections.of(!0),C0(),pl(H0,{fallback:!0}),Q0(),H1(),ex(),Zm(),ig(),jm(),Ub(),vn.of([...U1,...Ou,...u1,...Ay,...I0,...td,...Sb])];/*!
* VueCodemirror v6.1.1
* Copyright (c) Surmon. All rights reserved.
* Released under the MIT License.
* Surmon
*/var nx=Object.freeze({autofocus:!1,disabled:!1,indentWithTab:!0,tabSize:2,placeholder:"",autoDestroy:!0,extensions:[ix]}),sx=Symbol("vue-codemirror-global-config"),Me,rx=function(n){var e=n.onUpdate,t=n.onChange,i=n.onFocus,s=n.onBlur,r=function(o,l){var a={};for(var h in o)Object.prototype.hasOwnProperty.call(o,h)&&l.indexOf(h)<0&&(a[h]=o[h]);if(o!=null&&typeof Object.getOwnPropertySymbols=="function"){var c=0;for(h=Object.getOwnPropertySymbols(o);c<h.length;c++)l.indexOf(h[c])<0&&Object.prototype.propertyIsEnumerable.call(o,h[c])&&(a[h[c]]=o[h[c]])}return a}(n,["onUpdate","onChange","onFocus","onBlur"]);return j.create({doc:r.doc,selection:r.selection,extensions:(Array.isArray(r.extensions)?r.extensions:[r.extensions]).concat([R.updateListener.of(function(o){e(o),o.docChanged&&t(o.state.doc.toString(),o),o.focusChanged&&(o.view.hasFocus?i(o):s(o))})])})},di=function(n){var e=new gn;return{compartment:e,run:function(t){e.get(n.state)?n.dispatch({effects:e.reconfigure(t)}):n.dispatch({effects:_.appendConfig.of(e.of(t))})}}},_h=function(n,e){var t=di(n),i=t.compartment,s=t.run;return function(r){var o=i.get(n.state);s(r??o!==e?e:[])}},Xn={type:Boolean,default:void 0},ox={autofocus:Xn,disabled:Xn,indentWithTab:Xn,tabSize:Number,placeholder:String,style:Object,autoDestroy:Xn,phrases:Object,root:Object,extensions:Array,selection:Object},lx={modelValue:{type:String,default:""}},ax=Object.assign(Object.assign({},ox),lx);(function(n){n.Change="change",n.Update="update",n.Focus="focus",n.Blur="blur",n.Ready="ready",n.ModelUpdate="update:modelValue"})(Me||(Me={}));var Yt={};Yt[Me.Change]=function(n,e){return!0},Yt[Me.Update]=function(n){return!0},Yt[Me.Focus]=function(n){return!0},Yt[Me.Blur]=function(n){return!0},Yt[Me.Ready]=function(n){return!0};var id={};id[Me.ModelUpdate]=Yt[Me.Change];var hx=Object.assign(Object.assign({},Yt),id),cx=sd({name:"VueCodemirror",props:Object.assign({},ax),emits:Object.assign({},hx),setup:function(n,e){var t=Ys(),i=Ys(),s=Ys(),r=Object.assign(Object.assign({},nx),rd(sx,{})),o=Vr(function(){var l={};return Object.keys(od(n)).forEach(function(a){var h;a!=="modelValue"&&(l[a]=(h=n[a])!==null&&h!==void 0?h:r[a])}),l});return Wo(function(){var l;i.value=rx({doc:n.modelValue,selection:o.value.selection,extensions:(l=r.extensions)!==null&&l!==void 0?l:[],onFocus:function(h){return e.emit(Me.Focus,h)},onBlur:function(h){return e.emit(Me.Blur,h)},onUpdate:function(h){return e.emit(Me.Update,h)},onChange:function(h,c){h!==n.modelValue&&(e.emit(Me.Change,h,c),e.emit(Me.ModelUpdate,h,c))}}),s.value=function(h){return new R(Object.assign({},h))}({state:i.value,parent:t.value,root:o.value.root});var a=function(h){var c=function(){return h.state.doc.toString()},f=di(h).run,u=_h(h,[R.editable.of(!1),j.readOnly.of(!0)]),d=_h(h,vn.of([ku])),m=di(h).run,x=di(h).run,S=di(h).run,y=di(h).run;return{focus:function(){return h.focus()},getDoc:c,setDoc:function(O){O!==c()&&h.dispatch({changes:{from:0,to:h.state.doc.length,insert:O}})},reExtensions:f,toggleDisabled:u,toggleIndentWithTab:d,setTabSize:function(O){m([j.tabSize.of(O),_s.of(" ".repeat(O))])},setPhrases:function(O){x([j.phrases.of(O)])},setPlaceholder:function(O){S(Xm(O))},setStyle:function(O){O===void 0&&(O={}),y(R.theme({"&":Object.assign({},O)}))}}}(s.value);Ke(function(){return n.modelValue},function(h){h!==a.getDoc()&&a.setDoc(h)}),Ke(function(){return n.extensions},function(h){return a.reExtensions(h||[])},{immediate:!0}),Ke(function(){return o.value.disabled},function(h){return a.toggleDisabled(h)},{immediate:!0}),Ke(function(){return o.value.indentWithTab},function(h){return a.toggleIndentWithTab(h)},{immediate:!0}),Ke(function(){return o.value.tabSize},function(h){return a.setTabSize(h)},{immediate:!0}),Ke(function(){return o.value.phrases},function(h){return a.setPhrases(h||{})},{immediate:!0}),Ke(function(){return o.value.placeholder},function(h){return a.setPlaceholder(h)},{immediate:!0}),Ke(function(){return o.value.style},function(h){return a.setStyle(h)},{immediate:!0}),o.value.autofocus&&a.focus(),e.emit(Me.Ready,{state:i.value,view:s.value,container:t.value})}),_o(function(){o.value.autoDestroy&&s.value&&function(l){l.destroy()}(s.value)}),function(){return ld("div",{class:"v-codemirror",style:{display:"contents"},ref:t})}}}),fx=cx;const ux=al({String:A.string,Number:A.number,"True False":A.bool,PropertyName:A.propertyName,Null:A.null,", :":A.separator,"[ ]":A.squareBracket,"{ }":A.brace}),dx=hn.deserialize({version:14,states:"$bOVQPOOOOQO'#Cb'#CbOnQPO'#CeOvQPO'#ClOOQO'#Cr'#CrQOQPOOOOQO'#Cg'#CgO}QPO'#CfO!SQPO'#CtOOQO,59P,59PO![QPO,59PO!aQPO'#CuOOQO,59W,59WO!iQPO,59WOVQPO,59QOqQPO'#CmO!nQPO,59`OOQO1G.k1G.kOVQPO'#CnO!vQPO,59aOOQO1G.r1G.rOOQO1G.l1G.lOOQO,59X,59XOOQO-E6k-E6kOOQO,59Y,59YOOQO-E6l-E6l",stateData:"#O~OeOS~OQSORSOSSOTSOWQO_ROgPO~OVXOgUO~O^[O~PVO[^O~O]_OVhX~OVaO~O]bO^iX~O^dO~O]_OVha~O]bO^ia~O",goto:"!kjPPPPPPkPPkqwPPPPk{!RPPP!XP!e!hXSOR^bQWQRf_TVQ_Q`WRg`QcZRicQTOQZRQe^RhbRYQR]R",nodeNames:"⚠ JsonText True False Null Number String } { Object Property PropertyName : , ] [ Array",maxTerm:25,nodeProps:[["isolate",-2,6,11,""],["openedBy",7,"{",14,"["],["closedBy",8,"}",15,"]"]],propSources:[ux],skippedNodes:[0],repeatNodeCount:2,tokenData:"(|~RaXY!WYZ!W]^!Wpq!Wrs!]|}$u}!O$z!Q!R%T!R![&c![!]&t!}#O&y#P#Q'O#Y#Z'T#b#c'r#h#i(Z#o#p(r#q#r(w~!]Oe~~!`Wpq!]qr!]rs!xs#O!]#O#P!}#P;'S!];'S;=`$o<%lO!]~!}Og~~#QXrs!]!P!Q!]#O#P!]#U#V!]#Y#Z!]#b#c!]#f#g!]#h#i!]#i#j#m~#pR!Q![#y!c!i#y#T#Z#y~#|R!Q![$V!c!i$V#T#Z$V~$YR!Q![$c!c!i$c#T#Z$c~$fR!Q![!]!c!i!]#T#Z!]~$rP;=`<%l!]~$zO]~~$}Q!Q!R%T!R![&c~%YRT~!O!P%c!g!h%w#X#Y%w~%fP!Q![%i~%nRT~!Q![%i!g!h%w#X#Y%w~%zR{|&T}!O&T!Q![&Z~&WP!Q![&Z~&`PT~!Q![&Z~&hST~!O!P%c!Q![&c!g!h%w#X#Y%w~&yO[~~'OO_~~'TO^~~'WP#T#U'Z~'^P#`#a'a~'dP#g#h'g~'jP#X#Y'm~'rOR~~'uP#i#j'x~'{P#`#a(O~(RP#`#a(U~(ZOS~~(^P#f#g(a~(dP#i#j(g~(jP#X#Y(m~(rOQ~~(wOW~~(|OV~",tokenizers:[0],topRules:{JsonText:[0,1]},tokenPrec:0}),px=cn.define({name:"json",parser:dx.configure({props:[fl.add({Object:Za({except:/^\s*\}/}),Array:Za({except:/^\s*\]/})}),dl.add({"Object Array":T0})]}),languageData:{closeBrackets:{brackets:["[","{",'"']},indentOnInput:/^\s*[\}\]]$/}});function mx(){return new Pf(px)}const gx="#e5c07b",$h="#e06c75",yx="#56b6c2",bx="#ffffff",hs="#abb2bf",Vo="#7d8799",xx="#61afef",vx="#98c379",qh="#d19a66",wx="#c678dd",Sx="#21252b",Hh="#2c313a",zh="#282c34",Fr="#353a42",Ox="#3E4451",jh="#528bff",kx=R.theme({"&":{color:hs,backgroundColor:zh},".cm-content":{caretColor:jh},".cm-cursor, .cm-dropCursor":{borderLeftColor:jh},"&.cm-focused > .cm-scroller > .cm-selectionLayer .cm-selectionBackground, .cm-selectionBackground, .cm-content ::selection":{backgroundColor:Ox},".cm-panels":{backgroundColor:Sx,color:hs},".cm-panels.cm-panels-top":{borderBottom:"2px solid black"},".cm-panels.cm-panels-bottom":{borderTop:"2px solid black"},".cm-searchMatch":{backgroundColor:"#72a1ff59",outline:"1px solid #457dff"},".cm-searchMatch.cm-searchMatch-selected":{backgroundColor:"#6199ff2f"},".cm-activeLine":{backgroundColor:"#6699ff0b"},".cm-selectionMatch":{backgroundColor:"#aafe661a"},"&.cm-focused .cm-matchingBracket, &.cm-focused .cm-nonmatchingBracket":{backgroundColor:"#bad0f847"},".cm-gutters":{backgroundColor:zh,color:Vo,border:"none"},".cm-activeLineGutter":{backgroundColor:Hh},".cm-foldPlaceholder":{backgroundColor:"transparent",border:"none",color:"#ddd"},".cm-tooltip":{border:"none",backgroundColor:Fr},".cm-tooltip .cm-tooltip-arrow:before":{borderTopColor:"transparent",borderBottomColor:"transparent"},".cm-tooltip .cm-tooltip-arrow:after":{borderTopColor:Fr,borderBottomColor:Fr},".cm-tooltip-autocomplete":{"& > ul > li[aria-selected]":{backgroundColor:Hh,color:hs}}},{dark:!0}),Cx=Ri.define([{tag:A.keyword,color:wx},{tag:[A.name,A.deleted,A.character,A.propertyName,A.macroName],color:$h},{tag:[A.function(A.variableName),A.labelName],color:xx},{tag:[A.color,A.constant(A.name),A.standard(A.name)],color:qh},{tag:[A.definition(A.name),A.separator],color:hs},{tag:[A.typeName,A.className,A.number,A.changed,A.annotation,A.modifier,A.self,A.namespace],color:gx},{tag:[A.operator,A.operatorKeyword,A.url,A.escape,A.regexp,A.link,A.special(A.string)],color:yx},{tag:[A.meta,A.comment],color:Vo},{tag:A.strong,fontWeight:"bold"},{tag:A.emphasis,fontStyle:"italic"},{tag:A.strikethrough,textDecoration:"line-through"},{tag:A.link,color:Vo,textDecoration:"underline"},{tag:A.heading,fontWeight:"bold",color:$h},{tag:[A.atom,A.bool,A.special(A.variableName)],color:qh},{tag:[A.processingInstruction,A.string,A.inserted],color:vx},{tag:A.invalid,color:bx}]),Ax=[kx,pl(Cx)],Tx={class:"json-editor-container"},Mx={key:0,class:"error-message"},Ex={class:"json-editor"},Dx={__name:"json",props:{modelValue:{type:[Object,String],default:()=>({})},height:{type:String,default:"500px"}},emits:["update:modelValue"],setup(n,{expose:e,emit:t}){const i=n,s=t,r=ie(null),o=ie(JSON.stringify(i.modelValue,null,2)),l=ie(!1);ie(!1);const a=ie(null),h=Vr(()=>[nl(),mx(),R.baseTheme({}),l.value?Ax:R.theme({},{dark:!1}),R.lineWrapping,j.readOnly.of(!1),R.editable.of(!0),R.theme({"&":{height:"100%",fontSize:"15px",border:"1px solid #3a3f4b",borderRadius:"4px"},".cm-gutters":{backgroundColor:"#282c34",borderRight:"1px solid #3a3f4b",color:"#858585",display:"block !important"},".cm-foldGutter":{display:"none !important"},".cm-cursorLayer":{display:"block !important"},".cm-selectionLayer":{display:"block !important"},".cm-activeLineGutter":{backgroundColor:"#2c313a",color:"#fff"},".cm-scroller":{fontFamily:'"Fira Code", Menlo, Monaco, Consolas, "Courier New", monospace',lineHeight:"1.6"},".cm-content":{caretColor:"#528bff",padding:"8px 0"},".cm-cursor":{borderLeft:"2px solid #000 !important"},".cm-selectionBackground":{background:"#b7b4d0"},"&.cm-focused .cm-selectionBackground":{background:"#b7b4d0"},".cm-selectionMatch":{backgroundColor:"rgba(180, 180, 255, 0.7)"}})]),c=Vr(()=>({height:i.height,border:`1px solid ${l.value?"#444":"#ddd"}`,borderRadius:"4px"}));Ke(()=>o.value,u=>{f(u)},{deep:!0});const f=u=>{try{const d=u?JSON.parse(u):{};return a.value=null,s("update:modelValue",d),!0}catch(d){return a.value=d.message,!1}};return Ke(()=>i.modelValue,u=>{},{deep:!0}),_o(()=>{}),e({getErrors:()=>a.value,validateJson:f}),(u,d)=>(Ot(),cs("div",Tx,[a.value?(Ot(),cs("div",Mx," 文件格式错误！ ")):Kh("",!0),xe("div",Ex,[Nt(Uh(fx),{ref_key:"cmRef",ref:r,modelValue:o.value,"onUpdate:modelValue":d[0]||(d[0]=m=>o.value=m),style:ad(c.value),extensions:h.value},null,8,["modelValue","style","extensions"])])]))}},Px=$o(Dx,[["__scopeId","data-v-f93b698e"]]),Nx=hd("collectSoftwareConfig",{state:()=>({configFileList:[]}),actions:{reset(){this.$reset()},async fetchGetConfigFileList(n){try{const e=await pd(n);let t=bd(e,{label:"fileName",value:"fileName"},{nother:!0});return this.configFileList=t,t}catch(e){throw console.error("获取失败:",e),e}},async fetchGetConfigFileContent(n){try{return await dd(n)}catch(e){throw console.error("获取失败:",e),e}},async fetchDownloadConfigFile(n){try{return await ud(n)}catch(e){throw console.error("获取失败:",e),e}},async fetchSaveConfigFile(n){try{return await fd(n)}catch(e){throw console.error(e),e}},async fetchUploadConfigFile(n){try{return await cd(n)}catch(e){throw console.error(e),e}}}}),Rx={class:"upload"},Bx={class:"formHeader"},Ix={class:"formbody"},Lx={class:"xmlHeader"},Fx={class:"fileUpdateTime"},Vx={class:"btns"},Wx={class:"xmlBox"},_x={class:"editorType"},$x={class:"xmlContent"},qx={__name:"collectSoftwareConfig",setup(n){const e=Nx();gd();let t=[{title:"服务器配置文件",dataIndex:"name",inputType:"select",selectOptions:[],formItemWidth:300,notshowLabel:!0}];const i=ie([]),s=ie(t),r=ie(""),o=ie(""),l=ie(""),a=ie(""),h=ie(""),c=ie(""),f=ie({}),u=ie(null),d=ie(null),m=ie(!1),x=ie(!1),S=async()=>{let w=await e.fetchGetConfigFileList();t[0].selectOptions=w,s.value=t,m.value=!m.value},y=async w=>{let P=await e.fetchGetConfigFileContent({fileName:w});if(P&&P.content&&P.fileName.indexOf(".")>-1){a.value=P.fileName;let V=P.fileName.split(".");if(V.length<2){Pt.error("文件格式错误！");return}h.value=V[V.length-1],h.value==="json"?o.value=JSON.parse(P.content):h.value==="xml"?r.value=P.content:(h.value="文本",l.value=P.content+""),c.value=P.lastModified?md(P.lastModified).format("YYYY-MM-DD HH:mm:ss"):""}},O=async()=>{x.value=!0,await S(),e.configFileList&&e.configFileList.length>0&&(f.value={name:e.configFileList[0].value},m.value=!m.value,y(e.configFileList[0].value)),x.value=!1};Wo(async()=>{O()});const v=w=>{(!w.fileList||w.fileList.length===0)&&(i.value=[])},g=async w=>{w.name&&w.name!==""&&y(w.name)},k=async()=>{let w=await e.fetchDownloadConfigFile({fileName:a.value});h.value=="json"&&(w=JSON.stringify(w,null,2));const P=window.URL.createObjectURL(new Blob([w])),V=document.createElement("a");V.href=P;const I=a.value;V.setAttribute("download",I),document.body.appendChild(V),V.click(),V.remove(),window.URL.revokeObjectURL(P)},T=async()=>{let w="";if(h.value==="json"){const V=d.value.getErrors();if(V&&V.length>0){Pt.error("Json文件格式存在错误，请检查！");return}w=JSON.stringify(o.value)}else if(h.value==="xml"){if(u.value.getErrors().length>0){Pt.error("XML文件格式存在错误，请检查！");return}w=r.value}else w=l.value;let P=await e.fetchSaveConfigFile({fileName:a.value,encoding:"UTF-8",content:w});P.code===1?Pt.success("保存成功"):Pt.error("保存失败！"+P.msg)},M=w=>(i.value=[w],!1),N=async()=>{if(i.value.length===0){Pt.error("请选择文件");return}const w=new FormData;w.append("file",i.value[0]);let P=await e.fetchUploadConfigFile(w);P.code===1?(i.value=[],Pt.success("上传成功"),S()):Pt.error("上传失败！"+P.msg)};return(w,P)=>{const V=vd,I=Sd,z=Od,$=xd;return Ot(),Li($,{spinning:x.value,size:"large"},{default:Dt(()=>[xe("div",Rx,[Nt(I,{"file-list":i.value,"before-upload":M,accept:".xml,.json,.so,.dll,.config",maxCount:1,multiple:!1,onChange:v},{default:Dt(()=>[Nt(V,{class:"noRadius",title:"支持的格式有.xml,.json,.so,.dll,.config"},{default:Dt(()=>[Nt(Uh(wd)),P[3]||(P[3]=Ut(" 上传配置文件 ",-1))]),_:1,__:[3]})]),_:1},8,["file-list"]),Nt(V,{type:"primary",class:"submit",onClick:N},{default:Dt(()=>P[4]||(P[4]=[Ut("确定",-1)])),_:1,__:[4]})]),xe("div",Bx,[P[6]||(P[6]=xe("label",null,"服务器配置文件:",-1)),xe("div",Ix,[(Ot(),Li(yd,{key:m.value,titleCol:s.value,initFormData:f.value,onSubmit:g,actions:["noSubmit"]},{otherInfo:Dt(()=>[Nt(V,{type:"primary","html-type":"submit"},{default:Dt(()=>P[5]||(P[5]=[Ut("获取配置详情",-1)])),_:1,__:[5]})]),_:1},8,["titleCol","initFormData"]))])]),xe("div",null,[xe("div",Lx,[xe("span",null,[P[7]||(P[7]=Ut("配置文件名称: ",-1)),xe("b",null,Js(a.value),1)]),xe("span",Fx,[P[8]||(P[8]=Ut("上次修改时间: ",-1)),xe("b",null,Js(c.value),1)]),xe("div",Vx,[Nt(V,{type:"primary",class:"noRadius",onClick:k},{default:Dt(()=>P[9]||(P[9]=[Ut(" 下载",-1)])),_:1,__:[9]}),Nt(V,{type:"primary",class:"noRadius",onClick:T},{default:Dt(()=>P[10]||(P[10]=[Ut(" 保存",-1)])),_:1,__:[10]})])]),xe("div",Wx,[xe("div",_x,Js(h.value)+"编辑器",1),xe("div",$x,[h.value=="xml"?(Ot(),Li($b,{key:0,ref_key:"xmlEditorRef",ref:u,modelValue:r.value,"onUpdate:modelValue":P[0]||(P[0]=q=>r.value=q)},null,8,["modelValue"])):h.value=="json"?(Ot(),Li(Px,{key:1,ref_key:"jsonEditorRef",ref:d,modelValue:o.value,"onUpdate:modelValue":P[1]||(P[1]=q=>o.value=q)},null,8,["modelValue"])):(Ot(),Li(z,{key:2,value:l.value,"onUpdate:value":P[2]||(P[2]=q=>l.value=q),"auto-size":"",class:"textarea"},null,8,["value"]))])])])]),_:1},8,["spinning"])}}},ov=$o(qx,[["__scopeId","data-v-3be51bb1"]]);export{ov as default};
