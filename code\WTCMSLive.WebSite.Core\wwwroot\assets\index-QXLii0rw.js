import{F as ee,S as te}from"./index-CzSbT6op.js";import{_ as ae}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{B as le}from"./index-7iPMz_Qy.js";import{F as ne}from"./index-CpBSPak5.js";import{y as P,r as S,w as j,ah as se,c as I,o as m,b as R,d as w,i as $,s as h,f as B,g as N,F as q,cg as E,t as z,q as W,m as oe}from"./index-BjOW8S1L.js";import{_ as ie}from"./table-RP3jLHlo.js";import{a as re}from"./ActionButton-C_grUmdF.js";const de="/icons/copy-up.png",ue=(c,_)=>{if(_!==3&&_!==4)throw new Error("递增位必须是 3 或 4");if(!c||c=="")return;const p=c.split(".").map(i=>parseInt(i,10));if(p.length!==4||p.some(i=>isNaN(i)||p.some(s=>s<0||s>255)))return;const k=_-1;p[k]+=1;for(let i=k;i>=0;i--)p[i]>255&&(p[i]=0,i>0?p[i-1]+=1:p[i]=255);return p.join(".")},ce=(c=0,_=1)=>{if(_<=0||!Number.isInteger(_))throw new Error("步长必须是正整数");if(typeof c=="number")return c+_;if(typeof c=="string"){const p=c.match(/(\d+)$/);if(p){const k=c.slice(0,p.index),i=p[1],g=parseInt(i,10)+_,O=String(g).padStart(i.length,"0");return O.length>i.length,k+O}else return c}throw new Error("起始值必须是数字或数字结尾的字符串")},fe={class:"tableBox"},pe={class:"tableBox"},ve={key:0,class:"requiredStyle"},be={key:1,class:"copy-up-icon",src:de,title:"默认同上"},me={key:2,class:"autoIncrementorBox"},ye=["title","onClick"],he={class:"autoIncrementorBoxContentBox"},ge={key:0,class:"actions"},Ie={key:0,class:"addRow"},_e={key:0,class:"footer"},Ce={class:W(["footer-btn"])},we={key:0,class:"btnitem"},ke={key:1,class:"btnitem"},Re={__name:"index",props:{tableColumns:Array,tableDatas:Array,recordKey:String,tableTitle:String,tableOperate:{type:Array,default:()=>[]},noCopyUpKeys:{type:Array,default:()=>[]},removeDuplicateKeys:{type:Array,default:()=>[]},noRepeatKeys:{type:Array,default:()=>[]},noCopyUpAll:{type:Boolean,default:()=>!1},addBtnDisabled:Boolean,size:String,noPagination:Boolean,noForm:Boolean},emits:["addRow","deleteRow","editRow","submit","hangeTableFormChange","update:modelValue"],setup(c,{expose:_,emit:p}){const k=[{value:3,label:"第3位"},{value:4,label:"第4位"}],i=P({}),s=c,g=P({autoIncrementorNames:{},autoIncrementorRules:{}}),O=e=>{g.autoIncrementorNames[e]=!g.autoIncrementorNames[e]},M=(e,a)=>{g.autoIncrementorRules[a]={value:e}},G=e=>e.isrequired||e.validateRules&&e.validateRules.length&&e.validateRules[0].required,A=e=>{var n,r;let a=e.filter(o=>!o.columnHidden);return(n=s.removeDuplicateKeys)!=null&&n.length&&a.forEach(o=>{s.removeDuplicateKeys.includes(o.dataIndex)&&(o.hasChangeEvent=!0)}),(r=s.tableOperate)!=null&&r.length&&a.push({title:"操作",key:"action",dataIndex:"action"}),a},V=S(null),L=S(A(s.tableColumns)),x=p;let K={key:Date.now()};for(let e=0;e<s.tableColumns.length;e++)if(s.tableColumns[e].initValue||s.tableColumns[e].initValue===0){K[s.tableColumns[e].dataIndex]=s.tableColumns[e].initValue;const a=`${s.tableColumns[e].dataIndex}[0]`;i[a]=s.tableColumns[e].initValue}const y=S(s.tableDatas.length?s.tableDatas:[K]),C=S({});j(()=>s.tableDatas,e=>{e.forEach((a,n)=>{s.tableColumns.forEach(r=>{if(!r.noEdit){const o=`${r.dataIndex}[${n}]`;i[o]=a[r.dataIndex]}})})},{immediate:!0});const H=()=>{let e=y.value.length,a={key:Date.now()};y.value.push(a),T(e)};j(()=>s.tableColumns,e=>{L.value=A(e)},{immediate:!0});const J=(e,a,n,r)=>{const o=`${e}[${a}]`;i[o]=r,y.value[a][e]=r,V.value&&e&&n&&n.validate&&setTimeout(()=>{V.value.validateFields([o])},100)},Q=(e,a,n)=>{var r,o;if((r=s.removeDuplicateKeys)!=null&&r.length&&s.removeDuplicateKeys.includes(n)&&(e&&e.value&&typeof e.value=="object"&&e.value!==null?C.value[n]=[...C.value[n]||[],e.value.value]:C.value[n]=[...C.value[n]||[],e.value],C.value[n]&&C.value[n].length>1))for(let f=0;f<L.value.length;f++){let t=L.value[f];if(t.dataIndex==n)for(let d=0;d<t.tableList.length;d++){if(d==a)continue;let l=i[`${t.dataIndex}[${d}]`],u=(o=t.selectOptions)==null?void 0:o.filter(v=>l&&l==v.value||!C.value[t.dataIndex].includes(v.value));t.tableList[d]={...t.tableList[d],selectOptions:u}}}e&&e.value&&typeof e.value=="object"&&e.value!==null?x("hangeTableFormChange",{value:{...e.value,dataIndex:e.dataIndex},dataIndex:n,index:a}):x("hangeTableFormChange",{value:e.value,dataIndex:n,index:a})},X=e=>{x("deleteRow",e);let a=y.value.filter(n=>n.key!==e.key);a.forEach((n,r)=>{Object.keys(n).forEach(o=>{const f=`${o}[${r}]`;i[f]=n[o]})}),y.value=a},T=e=>{const a={};let n=L.value;n.forEach(t=>{var d,l,u;if(!t.noEdit){const v=`${t.dataIndex}[${e-1}]`;if(t.columnOperate&&g.autoIncrementorNames[t.dataIndex])switch(t.columnOperate.type){case"number":a[t.dataIndex]=ce(i[v],1);break;case"ip":let b=((d=g.autoIncrementorRules[t.dataIndex])==null?void 0:d.value)||4;a[t.dataIndex]=ue(i[v],b);break}else a[t.dataIndex]=i[v]}if((l=s.removeDuplicateKeys)!=null&&l.length){if(s.removeDuplicateKeys.includes(t.dataIndex)&&(t.tableList||(t.tableList=[]),t.tableList&&t.tableList.length&&e>=t.tableList.length&&!t.tableList[e]&&t.tableList.push({}),C.value[t.dataIndex]&&C.value[t.dataIndex].length)){let v=(u=t.selectOptions)==null?void 0:u.filter(b=>!C.value[t.dataIndex].includes(b.value));t.tableList[e]={...t.tableList[e-1],selectOptions:v}}}else t.tableList&&t.tableList.length&&(e>=t.tableList.length&&!t.tableList[e]&&t.tableList.push({}),t.tableList[e]={...t.tableList[e-1]})});const o={...y.value[e]};L.value=[...n],Object.keys(a).forEach(t=>{if(!s.noCopyUpAll&&!s.noCopyUpKeys.includes(t)&&t!=="key"){o[t]=a[t];const d=`${t}[${e}]`;i[d]=a[t]}});const f=[...y.value];f[e]=o,y.value=[...f]},Y=()=>{x("cancel")},Z=e=>{var n,r;let a=!0;try{if((n=s.noRepeatKeys)!=null&&n.length){let o="",f={};for(let t=0;t<Object.keys(e).length;t++){const d=Object.keys(e)[t];let l=d.split("["),u=d;if(l.length>1&&(u=l[0]),s.noRepeatKeys.includes(u))if(!f[u])f[u]=[],f[u].push(e[d]);else{if(f[u].includes(e[d])){let v=(r=s.tableColumns.find(F=>F.dataIndex==u))==null?void 0:r.title;o=`第${l[1].split("]")[0]*1+1}行: ${v} 不能重复!`,oe.error(o),a=!1;break}f[u].push(e[d])}}}}catch(o){console.log(o)}a&&x("submit",e)};return _({getTableFieldsValue:()=>se(i),setTableFieldValue:e=>{const{formDataIndex:a,tableDataIndex:n,index:r,value:o}=e;i[a]=o;let f={...y.value[r]},t=[...y.value];f[n]=o,t[r]=f,y.value=[...t]},clearValidate:e=>{V.value.clearValidate(e)}}),(e,a)=>{const n=te,r=re,o=le,f=ie,t=ne;return m(),I("div",fe,[R(t,{ref_key:"formRef",ref:V,model:i,onFinish:Z},{default:w(()=>{var d;return[$("div",pe,[R(f,{bordered:"",columns:L.value,"data-source":y.value,pagination:!c.noPagination&&y.value.length>10,size:c.size||"middle"},{headerCell:w(({column:l})=>[G(l)?(m(),I("span",ve,"*")):h("",!0),!s.noCopyUpKeys.includes(l.dataIndex)&&l.dataIndex!=="action"?(m(),I("img",be)):h("",!0),N(" "+z(l.title)+" ",1),l.columnOperate?(m(),I("div",me,[$("span",{class:W(["title",g.autoIncrementorNames[l.dataIndex]?"activeTitle":"noActiveTitle"]),title:`${g.autoIncrementorNames[l.dataIndex]?"关闭":"开启"}递增(数字或以数字结尾)`,onClick:u=>O(l.dataIndex)},null,10,ye),l.columnOperate&&l.columnOperate.type=="ip"&&g.autoIncrementorNames[l.dataIndex]?(m(),B(r,{key:0,placement:"bottom",overlayClassName:"myPopover",trigger:"click"},{content:w(()=>{var u;return[$("div",he,[a[1]||(a[1]=$("span",null,"递增位：",-1)),R(n,{ref:"select",options:k,style:{width:"100px"},value:((u=g.autoIncrementorRules[l.dataIndex])==null?void 0:u.value)||4,onChange:v=>M(v,l.dataIndex)},null,8,["value","onChange"])])]}),default:w(()=>[a[2]||(a[2]=$("span",{class:"operate"},"IP递增位",-1))]),_:2,__:[2]},1024)):h("",!0)])):h("",!0),l.headerSlotName?E(e.$slots,l.headerSlotName,{key:3},void 0,!0):h("",!0)]),bodyCell:w(({column:l,record:u,text:v,index:b})=>{var F,U;return[l&&l.dataIndex&&l.dataIndex==="action"?(m(),I("div",ge,[(F=c.tableOperate)!=null&&F.includes("copyUp")&&b>0&&!c.noCopyUpAll?(m(),B(o,{key:0,onClick:D=>T(b)},{default:w(()=>a[3]||(a[3]=[N("同上",-1)])),_:2,__:[3]},1032,["onClick"])):h("",!0),(U=c.tableOperate)!=null&&U.includes("delete")?(m(),B(o,{key:1,onClick:D=>X(u)},{default:w(()=>a[4]||(a[4]=[N("删除",-1)])),_:2,__:[4]},1032,["onClick"])):h("",!0)])):l&&!l.noEdit?(m(),I(q,{key:1},[$("div",null,[R(ee,{class:"formItem",notshowLabels:!0,itemProps:{...l,formItemWidth:l.columnWidth,dataIndex:`${l.dataIndex}[${b}]`,...l.tableList&&l.tableList[b]?l.tableList[b]:{}},modelValue:i[`${l.dataIndex}[${b}]`],"onUpdate:modelValue":D=>J(l.dataIndex,b,{validate:l.inputType=="selectinput"},D),onOnchangeSelect:D=>Q(D,b,l.dataIndex)},null,8,["itemProps","modelValue","onUpdate:modelValue","onOnchangeSelect"])]),l.afterContent?E(e.$slots,l.afterContentName||"afterContent",{key:0,column:l,record:u,index:b,text:v},void 0,!0):h("",!0)],64)):(m(),I(q,{key:2},[N(z(v),1)],64))]}),_:3},8,["columns","data-source","pagination","size"]),(d=c.tableOperate)!=null&&d.includes("noAdd")?h("",!0):(m(),I("div",Ie,[R(o,{onClick:a[0]||(a[0]=l=>H())},{default:w(()=>a[5]||(a[5]=[N("添加一行",-1)])),_:1,__:[5]})]))]),c.noForm?h("",!0):(m(),I("div",_e,[E(e.$slots,"footer",{},void 0,!0),$("div",Ce,[s.noCancle?h("",!0):(m(),I("div",we,[R(o,{onClick:Y},{default:w(()=>a[6]||(a[6]=[N("取消",-1)])),_:1,__:[6]})])),s.noSubmit?h("",!0):(m(),I("div",ke,[R(o,{type:"primary","html-type":"submit"},{default:w(()=>a[7]||(a[7]=[N("确定",-1)])),_:1,__:[7]})]))])]))]}),_:3},8,["model"])])}}},Fe=ae(Re,[["__scopeId","data-v-db8a0409"]]);export{Fe as W};
