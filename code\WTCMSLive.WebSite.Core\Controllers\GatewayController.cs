using CMSFramework.BusinessEntity;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using WTCMSLive.BusinessModel;
using WTCMSLive.WebSite.Core.Models.DTOs;
using WTCMSLive.WebSite.Core.Models;
using AppFramework.IDUtility;
using CMSFramework.Logger;

namespace WTCMSLive.WebSite.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class GatewayController : ControllerBase
    {
        /// <summary>
        /// 获取Gateway机组设备信息
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetDevice")]
        public IActionResult GetDevice()
        {
            try
            {
                List<WindPark> windParkList = DevTreeManagement.GetWindParkList();
                var windPark = windParkList.FirstOrDefault();

                if (windPark == null)
                {
                    return Ok(ApiResponse<object>.Success(null, "暂无设备信息"));
                }

                // 获取风场下的机组信息
                var windTurbine = windPark.WindTurbineList?.FirstOrDefault();

                var result = new
                {
                    WindPark = new
                    {
                        windPark.WindParkID,
                        windPark.WindParkName,
                        windPark.WindParkCode,
                        windPark.OperationalDate,
                        windPark.ContactMan,
                        windPark.ContactTel,
                        windPark.Address,
                        windPark.PostCode,
                        windPark.Description,
                        windPark.Country,
                        windPark.Area,
                        windPark.location
                    },
                    WindTurbine = windTurbine != null ? new
                    {
                        windTurbine.WindTurbineID,
                        windTurbine.WindTurbineName,
                        windTurbine.WindTurbineCode,
                        windTurbine.WindTurbineModel,
                        windTurbine.OperationalDate,
                        windTurbine.MinWorkingRotSpeed,
                        windTurbine.Location
                    } : null
                };

                return Ok(ApiResponse<object>.Success(result));
            }
            catch (Exception ex)
            {
                Logger.LogErrorMessage("[GetDevice]获取Gateway设备信息失败", ex);
                return Ok(ApiResponse<string>.Error($"获取设备信息失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 添加Gateway机组设备
        /// </summary>
        /// <param name="dto">机组设备信息</param>
        /// <returns></returns>
        [HttpPost("AddDevice")]
        public IActionResult AddDevice([FromBody] GatewayDeviceDTO dto)
        {
            try
            {
                if (dto == null)
                {
                    return Ok(ApiResponse<string>.Error("请求参数不能为空"));
                }

                if (string.IsNullOrEmpty(dto.WindParkName) || string.IsNullOrEmpty(dto.WindTurbineName))
                {
                    return Ok(ApiResponse<string>.Error("风场名称和机组名称不能为空"));
                }

                if (string.IsNullOrEmpty(dto.WindParkCode) || string.IsNullOrEmpty(dto.WindTurbineCode))
                {
                    return Ok(ApiResponse<string>.Error("风场编码和机组编码不能为空"));
                }

                if (string.IsNullOrEmpty(dto.ComponentIds))
                {
                    return Ok(ApiResponse<string>.Error("机组部件信息不能为空"));
                }

                // 检查是否已存在风场和机组
                List<WindPark> existingWindParks = DevTreeManagement.GetWindParkList();
                var existingPark = existingWindParks.FirstOrDefault(p => p.WindParkName == dto.WindParkName);

                if (existingPark != null)
                {
                    return Ok(ApiResponse<string>.Error("风场名称已存在"));
                }

                var existingParkByCode = existingWindParks.FirstOrDefault(p => p.WindParkCode == dto.WindParkCode.PadLeft(3, '0'));
                if (existingParkByCode != null)
                {
                    return Ok(ApiResponse<string>.Error("风场编码已存在"));
                }

                // 构建风场信息
                WindPark windPark = new WindPark();
                windPark.WindParkName = dto.WindParkName;
                windPark.WindParkCode = int.Parse(dto.WindParkCode).ToString("000");
                windPark.OperationalDate = DateTime.Parse(dto.OperationalDate ?? DateTime.Now.ToString("yyyy-MM-dd"));
                windPark.ContactMan = dto.ContactMan ?? "";
                windPark.ContactTel = dto.ContactTel ?? "";
                windPark.Address = dto.Address ?? "";
                windPark.PostCode = dto.PostCode ?? "";
                windPark.WindParkID = IDProvide.GetWindParkId(dto.WindParkGroupName ?? "其他", windPark.WindParkCode);
                windPark.Description = dto.Description ?? "";
                windPark.Country = dto.Country ?? "";
                windPark.Area = dto.Area ?? "";
                windPark.location = dto.Location ?? "";

                // 添加风场
                DevTreeManagement.AddWindPark(windPark);

                // 构建机组信息
                WindTurbine windTurbine = new WindTurbine();
                windTurbine.WindTurbineID = IDProvide.GetTurbineId(windPark.WindParkID, dto.WindTurbineCode);
                windTurbine.WindParkID = windPark.WindParkID;
                windTurbine.WindTurbineName = dto.WindTurbineName;
                windTurbine.WindTurbineCode = dto.WindTurbineCode;
                windTurbine.WindTurbineModel = dto.WindTurbineModel ?? "";
                windTurbine.OperationalDate = DateTime.Parse(dto.OperationalDate ?? DateTime.Now.ToString("yyyy-MM-dd"));
                windTurbine.MinWorkingRotSpeed = dto.MinWorkingRotSpeed ?? 0;
                windTurbine.Location = dto.Location ?? "";

                // 构建部件列表
                List<WindTurbineComponent> componentList = new List<WindTurbineComponent>();
                string[] componentIds = dto.ComponentIds.Split(',');

                foreach (string componentId in componentIds)
                {
                    if (!string.IsNullOrEmpty(componentId.Trim()))
                    {
                        WindTurbineComponent component = new WindTurbineComponent();
                        component.ComponentID = IDProvide.GetCompotentID(windTurbine.WindTurbineID, componentId.Trim());
                        component.WindTurbineID = windTurbine.WindTurbineID;
                        component.ComponentName = componentId.Trim();
                        componentList.Add(component);
                    }
                }

                windTurbine.TurComponentList = componentList;

                // 构建默认转速测量位置
                MeasLoc_RotSpd rotSpd = new MeasLoc_RotSpd();
                rotSpd.MeasLocName = "发电机转速";
                rotSpd.LineCounts = 2;
                rotSpd.GearRatio = 1f;
                rotSpd.MeasLocationID = IDProvide.GetRotSpdLocID(windTurbine.WindTurbineID, "发电机");
                rotSpd.WindTurbineID = windTurbine.WindTurbineID;
                windTurbine.RotSpdMeasLoc = rotSpd;

                // 添加机组
                DevTreeManagement.AddWindTurbine_Manager(windTurbine);

                // 添加主控系统
                if (!string.IsNullOrEmpty(dto.McsIP))
                {
                    MCS mcs = new MCS();
                    mcs.WindTurbineID = windTurbine.WindTurbineID;
                    mcs.MCSIP = dto.McsIP;
                    mcs.FieldBusType = "0";
                    mcs.MCSPort = 502;
                    DAUMCS.AddMCS(mcs);
                }

                // 记录日志
                LogEntity logEntity = new LogEntity();
                logEntity.LogDB = ConstDefine.UserManagementLog;
                logEntity.LogTime = DateTime.Now;
                logEntity.NodeID = windTurbine.WindTurbineName;
                logEntity.UserName = Request.Cookies["WindCMSUserName"];
                logEntity.OperationDescription = string.Format("Gateway添加_设备树({0})", windTurbine.WindTurbineID);
                LogManagement.UserlogWrite(logEntity);

                return Ok(ApiResponse<string>.Success("OK"));
            }
            catch (Exception ex)
            {
                Logger.LogErrorMessage("[AddDevice]Gateway添加机组失败", ex);
                return Ok(ApiResponse<string>.Error($"添加机组失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 修改Gateway机组设备信息
        /// </summary>
        /// <param name="dto">机组设备信息</param>
        /// <returns></returns>
        [HttpPost("EditDevice")]
        public IActionResult EditDevice([FromBody] GatewayDeviceDTO dto)
        {
            try
            {
                if (dto == null)
                {
                    return Ok(ApiResponse<string>.Error("请求参数不能为空"));
                }

                if (string.IsNullOrEmpty(dto.WindParkID) || string.IsNullOrEmpty(dto.WindTurbineID))
                {
                    return Ok(ApiResponse<string>.Error("风场ID和机组ID不能为空"));
                }

                // 获取现有风场信息
                WindPark existingWindPark = DevTreeManagement.GetWindPark(dto.WindParkID);
                if (existingWindPark == null)
                {
                    return Ok(ApiResponse<string>.Error("风场不存在"));
                }

                // 获取现有机组信息
                WindTurbine existingWindTurbine = DevTreeManagement.GetWindTurbine(dto.WindTurbineID);
                if (existingWindTurbine == null)
                {
                    return Ok(ApiResponse<string>.Error("机组不存在"));
                }

                // 更新风场信息
                if (!string.IsNullOrEmpty(dto.WindParkName))
                {
                    existingWindPark.WindParkName = dto.WindParkName;
                }
                if (!string.IsNullOrEmpty(dto.OperationalDate))
                {
                    existingWindPark.OperationalDate = DateTime.Parse(dto.OperationalDate);
                }
                if (dto.ContactMan != null)
                {
                    existingWindPark.ContactMan = dto.ContactMan;
                }
                if (dto.ContactTel != null)
                {
                    existingWindPark.ContactTel = dto.ContactTel;
                }
                if (dto.Address != null)
                {
                    existingWindPark.Address = dto.Address;
                }
                if (dto.PostCode != null)
                {
                    existingWindPark.PostCode = dto.PostCode;
                }
                if (dto.Description != null)
                {
                    existingWindPark.Description = dto.Description;
                }
                if (dto.Country != null)
                {
                    existingWindPark.Country = dto.Country;
                }
                if (dto.Area != null)
                {
                    existingWindPark.Area = dto.Area;
                }
                if (dto.Location != null)
                {
                    existingWindPark.location = dto.Location;
                }

                // 更新机组信息
                if (!string.IsNullOrEmpty(dto.WindTurbineName))
                {
                    existingWindTurbine.WindTurbineName = dto.WindTurbineName;
                }
                if (!string.IsNullOrEmpty(dto.WindTurbineModel))
                {
                    existingWindTurbine.WindTurbineModel = dto.WindTurbineModel;
                }
                if (!string.IsNullOrEmpty(dto.OperationalDate))
                {
                    existingWindTurbine.OperationalDate = DateTime.Parse(dto.OperationalDate);
                }
                if (dto.MinWorkingRotSpeed.HasValue)
                {
                    existingWindTurbine.MinWorkingRotSpeed = dto.MinWorkingRotSpeed.Value;
                }
                if (dto.Location != null)
                {
                    existingWindTurbine.Location = dto.Location;
                }

                // 保存风场信息
                DevTreeManagement.EditWindPark(existingWindPark);

                // 保存机组信息
                DevTreeManagement.EditWindTurbine(existingWindTurbine);

                // 更新主控系统IP
                if (!string.IsNullOrEmpty(dto.McsIP))
                {
                    MCS existingMcs = DAUMCS.GetMCSByTurbineId(dto.WindTurbineID);
                    if (existingMcs != null)
                    {
                        existingMcs.MCSIP = dto.McsIP;
                        DAUMCS.EditMCS(existingMcs);
                    }
                    else
                    {
                        MCS newMcs = new MCS();
                        newMcs.WindTurbineID = dto.WindTurbineID;
                        newMcs.MCSIP = dto.McsIP;
                        newMcs.FieldBusType = "0";
                        newMcs.MCSPort = 502;
                        DAUMCS.AddMCS(newMcs);
                    }
                }

                // 记录日志
                LogEntity logEntity = new LogEntity();
                logEntity.LogDB = ConstDefine.UserManagementLog;
                logEntity.LogTime = DateTime.Now;
                logEntity.NodeID = existingWindTurbine.WindTurbineName;
                logEntity.UserName = Request.Cookies["WindCMSUserName"];
                logEntity.OperationDescription = string.Format("Gateway修改_设备树({0})", existingWindTurbine.WindTurbineID);
                LogManagement.UserlogWrite(logEntity);

                return Ok(ApiResponse<string>.Success("OK"));
            }
            catch (Exception ex)
            {
                Logger.LogErrorMessage("[EditDevice]Gateway修改机组失败", ex);
                return Ok(ApiResponse<string>.Error($"修改机组失败: {ex.Message}"));
            }
        }
    }
}