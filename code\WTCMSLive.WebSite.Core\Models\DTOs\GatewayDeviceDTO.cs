using System;

namespace WTCMSLive.WebSite.Core.Models.DTOs
{
    /// <summary>
    /// Gateway设备信息DTO
    /// </summary>
    public class GatewayDeviceDTO
    {
        /// <summary>
        /// 风场ID（修改时使用）
        /// </summary>
        public string? WindParkID { get; set; }

        /// <summary>
        /// 风场名称
        /// </summary>
        public string? WindParkName { get; set; }

        /// <summary>
        /// 风场编码
        /// </summary>
        public string? WindParkCode { get; set; }

        /// <summary>
        /// 风场集团名称
        /// </summary>
        public string? WindParkGroupName { get; set; }

        /// <summary>
        /// 投运日期
        /// </summary>
        public string? OperationalDate { get; set; }

        /// <summary>
        /// 联系人
        /// </summary>
        public string? ContactMan { get; set; }

        /// <summary>
        /// 联系电话
        /// </summary>
        public string? ContactTel { get; set; }

        /// <summary>
        /// 地址
        /// </summary>
        public string? Address { get; set; }

        /// <summary>
        /// 邮编
        /// </summary>
        public string? PostCode { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// 国家
        /// </summary>
        public string? Country { get; set; }

        /// <summary>
        /// 区域
        /// </summary>
        public string? Area { get; set; }

        /// <summary>
        /// 位置
        /// </summary>
        public string? Location { get; set; }

        /// <summary>
        /// 机组ID（修改时使用）
        /// </summary>
        public string? WindTurbineID { get; set; }

        /// <summary>
        /// 机组名称
        /// </summary>
        public string? WindTurbineName { get; set; }

        /// <summary>
        /// 机组编码
        /// </summary>
        public string? WindTurbineCode { get; set; }

        /// <summary>
        /// 机组型号
        /// </summary>
        public string? WindTurbineModel { get; set; }

        /// <summary>
        /// 最小工作转速
        /// </summary>
        public int? MinWorkingRotSpeed { get; set; }

        /// <summary>
        /// 主控IP地址
        /// </summary>
        public string? McsIP { get; set; }

        /// <summary>
        /// 部件ID列表（逗号分隔）
        /// </summary>
        public string? ComponentIds { get; set; }
    }
}
