import{W as B}from"./table-RP3jLHlo.js";import{O}from"./index-CzSbT6op.js";import{u as Y,g as x}from"./configRoot-3RAhel6W.js";import{u as j}from"./devTree-Dwa9wLl9.js";import{r as o,h as z,f as P,d as T,o as h,i as L,b as W,c as A,z as N,m as c}from"./index-BjOW8S1L.js";import{_ as H}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{S as $}from"./tools-zTE6InS0.js";import{M as G}from"./index-BnSFuLp6.js";import"./ActionButton-C_grUmdF.js";import"./styleChecker-CFtINSLw.js";import"./index-7iPMz_Qy.js";import"./initDefaultProps-P4j1rGDC.js";import"./shallowequal-gCpTBdTi.js";import"./index-DTxROkTj.js";import"./index-kP-mINcM.js";import"./index-BJEkaghg.js";import"./index-D3J_dKbJ.js";import"./index-CpBSPak5.js";const K={key:1},q="YYYY-MM-DD HH:mm:ss",J={__name:"device",setup(Q){const n=Y(),y=j(),_=e=>[{title:"厂站编号",dataIndex:"windParkCode",columnWidth:80,headerOperations:{sorter:!0}},{title:"厂站名称",dataIndex:"windParkName",columnWidth:100},{title:"联系人",dataIndex:"contactMan",columnWidth:70},{title:"联系人电话",dataIndex:"contactTel",columnWidth:110},{title:"区域",dataIndex:"area",columnWidth:100,headerOperations:{filters:[]}},{title:"地址",dataIndex:"address",columnWidth:150},{title:"经纬度",dataIndex:"location",columnWidth:80},{title:"邮编",dataIndex:"postCode",columnWidth:100},{title:"概况",dataIndex:"description",columnWidth:100}],s=o(null),d=o(""),i=o(""),u=o({}),w=o([]),r=o([]),m=o(!1),p=o(!1),k=async(e,t)=>{let a=s.value.getFieldsValue();if(!a)return;const l=parseFloat(a.location1);return parseFloat(a.location2)&&!l?Promise.reject(new Error("请输入经度")):(s.value.clearValidate("location2"),Promise.resolve())},g=async(e,t)=>{let a=s.value.getFieldsValue();if(!a)return;const l=parseFloat(a.location1);return!parseFloat(a.location2)&&l?Promise.reject(new Error("请输入纬度")):(s.value.clearValidate("location1"),Promise.resolve())},D=o(_()),f=async()=>{p.value=!0,w.value=await n.fetchParkList(),p.value=!1,D.value=_()};z(()=>{f()});const b=()=>{m.value=!0},v=e=>{m.value=!1,u.value={},r.value=[],d.value="",i.value=""},R=async e=>{const{title:t,operateType:a}=e;u.value={country:"中国",area:"河北"},i.value=a,d.value="增加厂站",r.value=[...x({isEdit:!1},k,g)],await C(),b()},M=async(e={})=>{const{selectedkeys:t}=e;if(t.length<1){c.error("请选择要删除的行");return}const a=await n.fetchDeletetPark({windParkID:t[0]});a&&a.code===1?(f(),v(),c.success("提交成功"),y.getDevTreeDatas()):c.error("提交失败:"+a.msg)},S=async e=>{const{rowData:t,tableKey:a,operateType:l,title:F}=e;i.value=l;let V=t.operationalDate?N(t.operationalDate,q):"",I=t.location?t.location.split(","):[];u.value={...t,location1:I[0],location2:I[1],operationalDate:V},d.value="编辑厂站",r.value=[...x({isEdit:!0},k,g)],await C(),b()},C=async()=>{(!n.groupCompanyList||n.groupCompanyList.length<1)&&await n.fetchGroupCompanyList();const e=n.groupCompanyList;if(e&&e.length>0){let t=[...r.value];t[1].selectOptions=e,r.value=[...t]}},E=async e=>{const t=await n.fetchEditWindparkInformation({...e,location:`${e.location1},${e.location2}`,description:e.description||"",windParkID:e.windParkID||""});t&&t.code===1?(f(),c.success("提交成功"),i.value=="add"&&y.getDevTreeDatas(),v()):c.error("提交失败:"+t.msg)};return(e,t)=>{const a=G,l=$;return h(),P(l,{spinning:p.value,size:"large"},{default:T(()=>[L("div",null,[L("div",null,[W(B,{ref:"table",size:"default","table-key":"0","table-title":"厂站列表","table-columns":D.value,noBatchApply:!0,"record-key":"windParkID","table-operate":["edit","delete","add"],"table-datas":w.value,onAddRow:R,onDeleteRow:M,onEditRow:S},null,8,["table-columns","table-datas"])]),W(a,{maskClosable:!1,width:"600px",open:m.value,title:d.value,footer:"",onCancel:v},{default:T(()=>[i.value==="add"||i.value==="edit"?(h(),P(O,{key:0,titleCol:r.value,ref_key:"operateFormRef",ref:s,initFormData:u.value,onSubmit:E},null,8,["titleCol","initFormData"])):(h(),A("div",K))]),_:1},8,["open","title"])])]),_:1},8,["spinning"])}}},ve=H(J,[["__scopeId","data-v-74d8866c"]]);export{ve as default};
