import{u as Me,c as Ne,W as F}from"./table-RP3jLHlo.js";import{O as xe}from"./index-CzSbT6op.js";import{W as We}from"./index-QXLii0rw.js";import{r as v,u as _e,y as Fe,j as se,w as Se,f as ne,d as T,z as pe,o as g,c as A,b as w,i as me,F as Ve,e as Pe,g as B,t as qe,s as be,m as p,aR as he}from"./index-BjOW8S1L.js";import{S as Ge,f as le}from"./tools-zTE6InS0.js";import{u as Ke}from"./configDevice-B6u3cHuO.js";import{u as Ee}from"./model-DhdowwoF.js";import{u as $e}from"./devTree-Dwa9wLl9.js";import{_ as Be}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{D as ze,a as je}from"./index-D82yULGq.js";import{B as Ye}from"./index-7iPMz_Qy.js";import{M as Ue}from"./index-BnSFuLp6.js";import"./ActionButton-C_grUmdF.js";import"./styleChecker-CFtINSLw.js";import"./initDefaultProps-P4j1rGDC.js";import"./shallowequal-gCpTBdTi.js";import"./index-DTxROkTj.js";import"./index-kP-mINcM.js";import"./index-BJEkaghg.js";import"./index-D3J_dKbJ.js";import"./index-CpBSPak5.js";const He={class:"border"},Je={key:0,class:"editForm"},Qe={key:0,class:"getMeasLocName"},Xe={key:0,class:"setNameBtn"},Ze={key:2},fe="YYYY-MM-DD",z=320,et={__name:"device",setup(tt){const a=Ke();Ee();const j=$e(),De=Me(),Y=[{label:"晃度仪",value:"svm",text:"晃度仪"},{label:"倾角仪",value:"tim",text:"倾角仪"},{label:"其他Modbus",value:"modbus",text:"其他Modbus"},{label:"油液",value:"oil",text:"油液"}],S=()=>[{title:"振动测量位置名称",dataIndex:"measLocName",width:260,columnWidth:180,isrequired:!0,afterContent:!0,align:"left"},{title:"部件",dataIndex:"componentID",labelInValue:!0,columnWidth:200,isrequired:!0,headerOperations:{filters:[],filterDataIndex:["devTurComponent","componentName"]},inputType:"select",selectOptions:[],hasChangeEvent:!0,customRender:({record:e})=>e.devTurComponent&&e.devTurComponent.componentName?e.devTurComponent.componentName:""},{title:"截面",dataIndex:"sectionName",inputType:"select",columnWidth:200,isrequired:!0,selectOptions:[],headerOperations:{filters:[]}},{title:"方向",dataIndex:"orientation",inputType:"selectinput",columnWidth:200,isrequired:!0,selectOptions:[],headerOperations:{filters:[]}}],V=e=>[{title:"电流电压测量位置名称",dataIndex:"measLocName",width:260,columnWidth:180,afterContent:!0,isrequired:!0,align:"left"},{title:"部件",dataIndex:"componentID",labelInValue:!0,isrequired:!0,columnWidth:200,inputType:"select",selectOptions:[],hasChangeEvent:!0,headerOperations:{filters:[],filterDataIndex:["devTurComponent","componentName"]},customRender:({record:t})=>t.devTurComponent&&t.devTurComponent.componentName?t.devTurComponent.componentName:""},{title:"截面",dataIndex:"sectionName",inputType:"select",columnWidth:200,isrequired:!0,selectOptions:[],headerOperations:{filters:[]}},{title:"方向",dataIndex:"orientation",inputType:"selectinput",columnWidth:200,isrequired:!0,selectOptions:[],headerOperations:{filters:[]}}],U=e=>[{title:"转速测量位置名称",dataIndex:"measLocName",columnWidth:80,isrequired:!0},{title:"变速比",dataIndex:"gearRatio",columnWidth:100,isrequired:!0},{title:"编码器线数",dataIndex:"lineCounts",columnWidth:70,isrequired:!0}],P=e=>[{title:"工况测量位置名称",dataIndex:"measLocName",width:300,columnWidth:220,afterContent:!0,isrequired:!0},{title:"工况参数",dataIndex:"mDFWorkData",labelInValue:!0,columnWidth:220,inputType:"select",selectOptions:[],isrequired:!0,...e?{}:{customRender:({record:t})=>{if(!a.workCondMeasLocDicOptions||!a.workCondMeasLocDicOptions.length)return t.param_Type_Code||"";const s=a.workCondMeasLocDicOptions.find(l=>l.value==t.param_Type_Code);return s?s.label:t.param_Type_Code}}},{title:"数据来源",dataIndex:"datafrom",labelInValue:!0,inputType:"select",columnWidth:220,selectOptions:[],isrequired:!0,headerOperations:{filters:a.enumWorkConDataSourceOptions},...e?{}:{customRender:({text:t,record:s})=>{if(!a.enumWorkConDataSourceOptions||!a.enumWorkConDataSourceOptions.length)return t;const l=a.enumWorkConDataSourceOptions.find(n=>n.value==s.fieldBusType);return l?l.label:t}}}],q=()=>[{title:"测量位置名称",dataIndex:"measLocName",width:260,columnWidth:180,isrequired:!0,afterContent:!0,align:"left"},{title:"部件",dataIndex:"componentID",labelInValue:!0,isrequired:!0,headerOperations:{filters:[],filterDataIndex:["componentName"]},columnWidth:160,inputType:"select",selectOptions:[],hasChangeEvent:!0,customRender:({record:e})=>e.componentName||""},{title:"截面",dataIndex:"sectionName",columnWidth:160,inputType:"selectinput",isrequired:!0,selectOptions:[],headerOperations:{filters:[]}},{title:"方向",dataIndex:"orientation",inputType:"selectinput",isrequired:!0,selectOptions:[],headerOperations:{filters:[]},columnWidth:160},{title:"测量位置类型",dataIndex:"measType",inputType:"select",isrequired:!0,hasChangeEvent:!0,selectOptions:Y,headerOperations:{filters:Y},customRender:({text:e,record:t})=>{const s=Y.find(l=>l.value==t.measType);return s?s.label:e}},{title:"物理量",dataIndex:"physicalType",inputType:"select",selectOptions:[],tableList:[],headerOperations:{filters:[]}}],ie=_e(),H=v(""),R=v(null),O=v(""),D=v(""),G=v(),f=v({}),i=v(ie.params.id),m=v(""),J=v(!1),h=v([]),Q=v(!1),o=Fe({table1Columns:S(),table2Columns:V(),table3Columns:U(),table4Columns:P(),table5Columns:q(),tableDatas1:[],tableDatas2:[],tableDatas3:[],tableDatas4:[],tableDatas5:[],batchApplyData:[],batchApplyKey:"",bathApplyResponse1:{},bathApplyResponse2:{},bathApplyResponse3:{},bathApplyResponse4:{},bathApplyResponse5:{}}),ye=se(()=>{switch(m.value){case"1":return["measLocName"];case"2":return["measLocName"];case"3":return["measLocName"];case"4":return["measLocName","mDFWorkData"];case"5":return["measLocName"];default:return[]}}),ve=se(()=>{switch(m.value){case"1":return["measLocName"];case"2":return["measLocName"];case"3":return["measLocName"];case"4":return["measLocName"];case"5":return["measLocName"];default:return[]}}),we=()=>{let e=j.findAncestorsWithNodes(i.value);e&&e.length&&e.length>1&&(H.value=e[e.length-2].id)},re=(e={})=>[{label:"设备名称",value:e.windTurbineName},{label:"设备编号",value:e.windTurbineCode},{label:"设备型号",value:e.windTurbineModel},{label:"投运日期",value:e.operationalDate?pe(e.operationalDate).format(fe):""},{label:"额定功率(KW)",value:e.ratedPower},{label:"设备厂商",value:e.wTurbineModel&&e.wTurbineModel.manufactory?e.wTurbineModel.manufactory:""}],ce=v([re({})]),ue=async e=>{if(J.value=!0,i.value){const t=await a.fetchDeviceInfo({turbineID:i.value});ce.value=re(t)}J.value=!1},Te=async e=>{H.value&&await De.fetchDevTreedDevicelist({windParkID:H.value,useTobath:!0})},Ie=se(()=>D.value==="batchAdd"?"1200px":"600px"),K=async e=>{i.value&&(o.tableDatas1=await a.fetchGetVibMeaslocation({turbineID:i.value}),o.table1Columns=S())},E=async e=>{i.value&&(o.tableDatas2=await a.fetchGetProcessMeaslocation({turbineID:i.value}),o.table2Columns=V())},$=async e=>{i.value&&(o.tableDatas4=await a.fetchGetWorkCondMeasLocs({turbineID:i.value}),o.table4Columns=P())},X=async e=>{i.value&&(o.tableDatas3=await a.fetchGetRotSpdMeasLocList({turbineID:i.value}))},Z=async e=>{i.value&&(o.tableDatas5=await a.fetchGetModbusMeasLocList({turbineID:i.value}),o.table5Columns=q())},M=async()=>{(!a.componentList||a.componentList.length<1)&&await a.fetchGetComponentList({turbineID:i.value})},N=async()=>{(!a.orientatioList||a.orientatioList.length<1)&&await a.fetchGetOrientationList()},ee=async()=>{(!a.sVMParamType||a.sVMParamType.length<1)&&await a.fetchGetSVMParamType()},te=async()=>{(!a.workCondMeasLocDicOptions||a.workCondMeasLocDicOptions.length<1)&&await a.fetchGetWorkCondMeasLocDic()},ae=async()=>{(!a.enumWorkConDataSourceOptions||a.enumWorkConDataSourceOptions.length<1)&&await a.fetchGetEnumWorkConDataSource()};Se(()=>ie.params.id,async e=>{e&&(a.reset(),i.value=e,we(),await Te(),ue(),K(),E(),await ae(),await te(),$(),X(),Z())},{immediate:!0});const Le=async()=>{D.value="editDevice",O.value="编辑设备信息";const e=a.deviceInfo;h.value=[...Ae];let t=e.operationalDate?pe(e.operationalDate,fe):"";f.value={...e,operationalDate:t,windTurbineCode:e.windTurbineCode,windTurbineModel:e.windTurbineModel,windTurbineName:e.windTurbineName},oe()},x=async e=>{const{tableKey:t,title:s,operateType:l}=e;switch(D.value=l,m.value=t,O.value="添加"+s.split("列表")[0],t){case"1":await M(),await N();let n=S();n[1].selectOptions=[...a.componentList],n[3].selectOptions=[...a.orientatioList],h.value=[...n];break;case"2":await M(),await N();let c=V();c[1].selectOptions=[...a.componentList],c[3].selectOptions=[...a.orientatioList],h.value=[...c];break;case"3":let u=U();h.value=[...u],f.value={measLocName:"发电机转速"};break;case"4":await te(),await ae();let r=P(!0);r[1].selectOptions=[...a.workCondMeasLocDicOptions],r[2].selectOptions=[...a.enumWorkConDataSourceOptions],h.value=[...r];break;case"5":await M(),await N(),await ee();let d=q();d[1].selectOptions=[...a.componentList],d[3].selectOptions=[...a.orientatioList],d[5].selectOptions=[...a.sVMParamType],h.value=[...d];break}oe()},W=async e=>{const{tableKey:t,selectedkeys:s}=e;if(!(!e||!s||!s.length))switch(t){case"1":const l=await a.fetchDeleteVibMeasLocs({sourceData:s,targetTurbineIds:o.batchApplyData});l&&l.code===1?(K(),o.bathApplyResponse1=l.batchResults||{},p.success("删除成功")):p.error("删除失败:"+l.msg);break;case"2":const n=await a.fetchDeleteProcessMeasLocs({sourceData:s,targetTurbineIds:o.batchApplyData});n&&n.code===1?(E(),o.bathApplyResponse2=n.batchResults||{},p.success("删除成功")):p.error("删除失败:"+n.msg);break;case"3":let u={sourceData:[{WindTurbineID:i.value,MeasLocationID:s[0]}],targetTurbineIds:o.batchApplyData};const r=await a.fetchDeleteRotSpdLoc(u);r&&r.code===1?(X(),o.bathApplyResponse3=r.batchResults||{},p.success("删除成功")):p.error("删除失败:"+r.msg);break;case"4":let d=s.map(L=>({measLocationID:L,windTurbineID:i.value}));const y=await a.fetchDeleteWorkingConditionMeasBatch({sourceData:d,targetTurbineIds:o.batchApplyData});y&&y.code===1?(o.bathApplyResponse4=y.batchResults||{},$(),p.success("删除成功")):p.error("删除失败:"+y.msg);break;case"5":const b=await a.fetchBatchDeleteMeasLoc({sourceData:s,targetTurbineIds:o.batchApplyData});b&&b.code===1?(o.bathApplyResponse5=b.batchResults||{},Z(),p.success("删除成功")):p.error("删除失败:"+b.msg);break}},_=async e=>{var c,u,r,d,y;const{rowData:t,tableKey:s,title:l,operateType:n}=e;switch(D.value=n,m.value=s,O.value="编辑"+l.split("列表")[0],s){case"1":case"2":let b=[];s==1?b=S():b=V(),await M(),await N();let L=await a.fetchGetSectionList({turbineID:i.value,componentName:(c=t.devTurComponent)==null?void 0:c.componentName});b[1].selectOptions=[...a.componentList],b[3].selectOptions=[...a.orientatioList],b[2].selectOptions=L,h.value=[...b],f.value={...t,componentID:{label:(u=t.devTurComponent)==null?void 0:u.componentName,value:(r=t.devTurComponent)==null?void 0:r.componentID}};break;case"3":f.value={...t},h.value=U();break;case"4":await te(),await ae();let k=P(!0);k[1].selectOptions=[...a.workCondMeasLocDicOptions],k[2].selectOptions=[...a.enumWorkConDataSourceOptions],h.value=[...k],f.value={...t,datafrom:{label:"",value:t.fieldBusType}};break;case"5":await M(),await N(),await ee();let C=q();C[1].selectOptions=[...a.componentList],C[3].selectOptions=[...a.orientatioList],C[5].selectOptions=[...a.sVMParamType],h.value=[...C],f.value={...t,componentID:{label:(d=t.devTurComponent)==null?void 0:d.componentName,value:(y=t.devTurComponent)==null?void 0:y.componentID}};break}oe()},de=async e=>{const{value:t,dataIndex:s,index:l}=e;if(s){if(s.indexOf("componentID")>-1&&t.label){let n=[...h.value],c=await a.fetchGetSectionList({turbineID:i.value,componentName:t.label});n[2].selectOptions=c,h.value=[...n],c&&c.length>0&&(D.value=="batchAdd"?R.value.setTableFieldValue({formDataIndex:`${n[2].dataIndex}[${l}]`,tableDataIndex:n[2].dataIndex,index:l,value:c[0].value}):G.value.setFieldValue("sectionList",c[0].value))}else if(s=="measType"&&t){console.log("value",e);let n=[],c=!1,u=h.value;if(t=="oil"?n=await a.fetchGetOilParamType():t=="modbus"?c=!0:(await ee(),n=a.sVMParamType),D.value=="batchAdd"){if(e.index>=u[5].tableList.length)for(let r=u[5].tableList.length;r<=e.index;r++)u[5].tableList.push({});u[5].tableList[e.index].selectOptions=n,u[5].tableList[e.index].disabled=c,n&&R.value.setTableFieldValue({formDataIndex:`physicalType[${e.index}]`,tableDataIndex:"physicalType",index:e.index,value:n.length?n[0].value:""})}}}},Ce=async e=>{var t,s,l,n;if(D.value=="editDevice"){let c={...e,componentIds:e.componentIds&&e.componentIds.length?e.componentIds.join(","):"",windTurbineID:f.value.windTurbineID,windParkId:f.value.windParkID};const u=await a.fetcheditOneDevice(c);u&&u.code===1?(ue(),e.windTurbineName!==f.value.windTurbineName&&await j.getDevTreeDatas(),await j.getDevTreeDatas(),I(),p.success("提交成功")):p.error("提交失败:"+u.msg)}else switch(m.value){case"1":case"2":let c={...e,windTurbineID:i.value,measLocationID:f.value.measLocationID,componentName:(t=e.componentID)==null?void 0:t.value,componentID:(s=e.componentID)==null?void 0:s.value},u={};m.value==1?u=await a.fetchEditVibMeasLoc(c):u=await a.fetchEditProcessMeasLoc(c),u&&u.code===1?(m.value==1?K():E(),I(),p.success("提交成功")):p.error("提交失败:"+u.msg);break;case"3":let r={...e,WindTurbineID:i.value},d={};D.value=="edit"?(r.measLocationID=f.value.measLocationID,d=await a.fetchEditRotSpdLoc({sourceData:r,targetTurbineIds:o.batchApplyData})):d=await a.fetchAddRotSpdMeaslocs({sourceData:r,targetTurbineIds:o.batchApplyData}),d&&d.code===1?(X(),o.bathApplyResponse3=d.batchResults||{},I(),p.success("提交成功")):p.error("提交失败:"+d.msg);break;case"4":let y={windTurbineID:i.value,measLocationID:f.value.measLocationID,measLocName:f.value.measLocName,datafrom:(l=e.datafrom)==null?void 0:l.label,mDFWorkData:(n=e.mDFWorkData)==null?void 0:n.label};const b=await a.fetchEditWorkingConditionMeas(y);b&&b.code===1?($(),I(),p.success("提交成功")):p.error("提交失败:"+b.msg);break}},ge=async e=>{switch(m.value){case"1":case"2":let t={windTurbineID:i.value,measLocationID:""},l=le(e,t,{componentID:{label:"componentName",value:"componentID"}}),n={};m.value==1?n=await a.fetchAddVibMeasLocs({sourceData:l,targetTurbineIds:o.batchApplyData}):n=await a.fetchAddProcessMeasLocs({sourceData:l,targetTurbineIds:o.batchApplyData}),n&&n.code===1?(m.value==1?(K(),o.bathApplyResponse1=n.batchResults||{}):(E(),o.bathApplyResponse2=n.batchResults||{}),I(),p.success("提交成功")):p.error("提交失败:"+n.msg);break;case"4":let c={windTurbineID:i.value,measLocationID:""},r=le(e,c,{mDFWorkData:{label:"MDFWorkData"},datafrom:{label:"datafrom"}}),d=await a.fetchAddWorkingConditionMeaslocs({sourceData:r,targetTurbineIds:o.batchApplyData});d&&d.code===1?($(),o.bathApplyResponse4=d.batchResults||{},I(),p.success("提交成功")):p.error("提交失败:"+d.msg);break;case"5":let y={windTurbineID:i.value,measLocationID:""},L=le(e,y,{componentID:{label:"componentName",value:"componentID"}});for(let C=0;C<L.length;C++)L[C].physicalType=L[C].physicalType||"";let k=await a.fetchAddModbusMeasloc({sourceData:L,targetTurbineIds:o.batchApplyData});k&&k.code===1?(Z(),o.bathApplyResponse5=k.batchResults||{},I(),p.success("提交成功")):p.error("提交失败:"+k.msg);break}},ke=e=>{var n;const{index:t}=e,s=(n=R.value)==null?void 0:n.getTableFieldsValue();if(!s||!Object.keys(s).length||t==null)return;let l="";m.value==1||m.value==2||m.value==5?(l+=s[`componentID[${t}]`]&&s[`componentID[${t}]`].label?s[`componentID[${t}]`].label:"",l+=s[`sectionName[${t}]`]||"",l+=s[`orientation[${t}]`]||""):m.value==4&&(l=s[`mDFWorkData[${t}]`]&&s[`mDFWorkData[${t}]`].label?s[`mDFWorkData[${t}]`].label:""),R.value.setTableFieldValue({formDataIndex:`measLocName[${t}]`,tableDataIndex:"measLocName",index:t,value:l}),l&&l!==""&&R.value.clearValidate(`measLocName[${t}]`)},Re=()=>{var s;const e=(s=G.value)==null?void 0:s.getFieldsValue();let t="";m.value==1||m.value==2?(t+=e.componentID&&e.componentID.label?e.componentID.label:"",t+=e.sectionName||"",t+=e.orientation||""):m.value==4&&(t=e.mDFWorkData&&e.mDFWorkData.label?e.mDFWorkData.label:""),G.value.setFieldValue("measLocName",t)},oe=()=>{Q.value=!0},I=e=>{Q.value=!1,f.value={},h.value=[],D.value="",O.value="",m.value=""},Ae=[{title:"设备编号",dataIndex:"windTurbineCode",formItemWidth:z,disabled:!0},{title:"设备名称",dataIndex:"windTurbineName",formItemWidth:z},{title:"设备型号",dataIndex:"windTurbineModel",formItemWidth:z,inputType:"select",selectOptions:[],disabled:!0},{title:"投递日期",dataIndex:"operationalDate",formItemWidth:z,inputType:"datepicker",timeFormat:"YYYY-MM-DD"}],Oe=async e=>{e.type&&e.type=="close"?(o.batchApplyData=[],o.batchApplyKey="",o[`bathApplyResponse${e.key}`]={}):(o.batchApplyData=e.turbines,o.batchApplyKey=e.key)};return he("deviceId",i),he("bathApplySubmit",Oe),(e,t)=>{const s=Ye,l=je,n=ze,c=Ue,u=Ge;return g(),ne(u,{spinning:J.value,size:"large"},{default:T(()=>[(g(),A("div",{key:i.value},[w(Ne,{tableTitle:"设备信息",defaultCollapse:!0,batchApply:!1},{rightButtons:T(()=>[w(s,{type:"primary",onClick:Le},{default:T(()=>t[1]||(t[1]=[B(" 编辑 ",-1)])),_:1,__:[1]})]),content:T(()=>[me("div",He,[w(n,{column:4,size:"small"},{default:T(()=>[(g(!0),A(Ve,null,Pe(ce.value,r=>(g(),ne(l,{label:r.label,key:r.label},{default:T(()=>[B(qe(r.value),1)]),_:2},1032,["label"]))),128))]),_:1})])]),_:1}),me("div",null,[w(F,{ref:"table",size:"default","table-key":"1","table-title":"振动测量位置列表","table-columns":o.table1Columns,"table-operate":["delete","add","batchDelete","batchAdd"],"record-key":"measLocationID",borderLight:o.batchApplyKey=="1",bathApplyResponse:o.bathApplyResponse1,"table-datas":o.tableDatas1,onAddRow:x,onDeleteRow:W,onEditRow:_,noPagination:!0},null,8,["table-columns","borderLight","bathApplyResponse","table-datas"]),w(F,{ref:"table",size:"default","table-key":"2","table-title":"电流电压测量位置列表","table-columns":o.table2Columns,bathApplyResponse:o.bathApplyResponse2,"table-operate":["batchDelete","batchAdd","delete"],"record-key":"measLocationID",borderLight:o.batchApplyKey=="2","table-datas":o.tableDatas2,onAddRow:x,onDeleteRow:W,onEditRow:_,noPagination:!0},null,8,["table-columns","bathApplyResponse","borderLight","table-datas"]),w(F,{ref:"table",size:"default","table-key":"3","table-title":"转速测量位置列表","table-columns":o.table3Columns,bathApplyResponse:o.bathApplyResponse3,"table-operate":["batchDelete",o.tableDatas3.length?"":"add","delete","edit"],borderLight:o.batchApplyKey=="3","record-key":"measLocationID","table-datas":o.tableDatas3,onAddRow:x,onDeleteRow:W,onEditRow:_,noPagination:!0},null,8,["table-columns","bathApplyResponse","table-operate","borderLight","table-datas"]),w(F,{ref:"table",size:"default","table-key":"4","table-title":"工况测量位置列表","table-columns":o.table4Columns,bathApplyResponse:o.bathApplyResponse4,"table-operate":["batchDelete","batchAdd","delete"],"record-key":"measLocationID",borderLight:o.batchApplyKey=="4","table-datas":o.tableDatas4,onAddRow:x,onDeleteRow:W,onEditRow:_,noPagination:!0},null,8,["table-columns","bathApplyResponse","borderLight","table-datas"]),w(F,{ref:"table",size:"default","table-key":"5","table-title":"Modbus设备测量位置列表","table-columns":o.table5Columns,bathApplyResponse:o.bathApplyResponse5,"table-operate":["batchDelete","batchAdd","delete","batchAdd"],"record-key":"measLocationID",borderLight:o.batchApplyKey=="5","table-datas":o.tableDatas5,onAddRow:x,onDeleteRow:W,onEditRow:_,noPagination:!0},null,8,["table-columns","bathApplyResponse","borderLight","table-datas"])]),w(c,{maskClosable:!1,width:Ie.value,open:Q.value,title:O.value,footer:"",destroyOnClose:!0,onCancel:I},{default:T(()=>[D.value==="add"||D.value==="edit"||D.value==="editDevice"?(g(),A("div",Je,[w(xe,{ref_key:"formModalRef",ref:G,titleCol:h.value,initFormData:f.value,onChange:de,onSubmit:Ce},{otherInfo:T(({formModel:r})=>[m.value=="1"||m.value=="2"||m.value=="4"?(g(),A("div",Qe,[w(s,{onClick:t[0]||(t[0]=d=>Re())},{default:T(()=>t[2]||(t[2]=[B("自动",-1)])),_:1,__:[2]})])):be("",!0)]),_:1},8,["titleCol","initFormData"])])):D.value==="batchAdd"?(g(),ne(We,{key:1,ref_key:"modalTableFormRef",ref:R,size:"default","table-key":"0","table-columns":h.value,"table-operate":["copyUp","delete"],"table-datas":[],"noCopyUp-keys":ye.value,"noRepeat-Keys":ve.value,onSubmit:ge,onHangeTableFormChange:de,onCancel:I},{afterContent:T(({column:r,record:d,text:y,index:b})=>[r.dataIndex==="measLocName"?(g(),A("div",Xe,[w(s,{onClick:L=>ke({record:d,index:b})},{default:T(()=>t[3]||(t[3]=[B("自动",-1)])),_:2,__:[3]},1032,["onClick"])])):be("",!0)]),_:1},8,["table-columns","noCopyUp-keys","noRepeat-Keys"])):(g(),A("div",Ze))]),_:1},8,["width","open","title"])]))]),_:1},8,["spinning"])}}},Lt=Be(et,[["__scopeId","data-v-ded1af26"]]);export{Lt as default};
