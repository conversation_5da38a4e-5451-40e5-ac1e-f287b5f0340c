import{r as d,w,f as p,x as C,d as b,u as L,o as e,i as s,c as r,s as N,g as n,t as a,F as B,e as _}from"./index-BjOW8S1L.js";import{u as T}from"./statusMonitor-eEhdeKkD.js";import{N as y}from"./noPermission-C8WghqCu.js";import{_ as k}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{S as x}from"./tools-zTE6InS0.js";import"./initDefaultProps-P4j1rGDC.js";const P={class:"border padding totalInfo"},D={class:"totalNumber"},V={class:"clearfix totalList"},F={class:"totalNumber pullLeft"},M={class:"pullLeft clearfix"},c={class:"parkNumber pullLeft redBg"},z={class:"parkNumber pullLeft yellowBg"},E={class:"parkNumber pullLeft greenBg"},G={class:"parkNumber pullLeft grayBg"},J={key:0,class:"clearfix parkList"},O={class:"title"},R={class:"number"},j={__name:"status",setup(q){const g=T(),l=d({});let f=window.localStorage.getItem("role")?JSON.parse(window.localStorage.getItem("role")).moduleIds:[],i=window.localStorage.getItem("token")?f.includes("21"):!0;const S=L(),o=d(!1),m=async()=>{o.value=!0,l.value=await g.fetchGetParkStatusCount(),o.value=!1};return w(()=>S.params.id,()=>{i&&m()},{immediate:!0}),(A,t)=>{const v=x;return C(i)?(e(),p(v,{key:0,spinning:o.value,size:"large"},{default:b(()=>[s("div",null,[s("div",P,[s("p",D,[t[0]||(t[0]=n("厂站总数 ",-1)),s("b",null,a(l.value.totalParkCount||""),1)]),s("div",V,[s("p",F,[t[1]||(t[1]=n(" 设备总数 ",-1)),s("b",null,a(l.value.totalTurbineStatus?l.value.totalTurbineStatus.totalCount:""),1)]),s("ul",M,[s("li",c,[t[2]||(t[2]=n("危险 ",-1)),s("span",null,a(l.value.totalTurbineStatus?l.value.totalTurbineStatus.dangerCount:""),1)]),s("li",z,[t[3]||(t[3]=n("注意 ",-1)),s("span",null,a(l.value.totalTurbineStatus?l.value.totalTurbineStatus.warningCount:""),1)]),s("li",E,[t[4]||(t[4]=n("正常 ",-1)),s("span",null,a(l.value.totalTurbineStatus?l.value.totalTurbineStatus.normalCount:""),1)]),s("li",G,[t[5]||(t[5]=n("其他 ",-1)),s("span",null,a(l.value.totalTurbineStatus?l.value.totalTurbineStatus.otherCount:""),1)])])])]),l.value.parkDetailList&&l.value.parkDetailList.length?(e(),r("ul",J,[(e(!0),r(B,null,_(l.value.parkDetailList,u=>(e(),r("li",{class:"border padding pullLeft card",key:u.windParkID},[s("p",O,a(u.windParkName),1),s("p",R,[s("b",null,a(u.turbineStatus?u.turbineStatus.totalCount:0),1),t[6]||(t[6]=n(" 台",-1))]),s("ul",null,[s("li",null,[t[7]||(t[7]=s("span",{class:"redBg"},"危险",-1)),t[8]||(t[8]=n()),s("b",null,a(u.turbineStatus?u.turbineStatus.dangerCount:0),1)]),s("li",null,[t[9]||(t[9]=s("span",{class:"yellowBg"},"注意",-1)),t[10]||(t[10]=n()),s("b",null,a(u.turbineStatus?u.turbineStatus.warningCount:0),1)]),s("li",null,[t[11]||(t[11]=s("span",{class:"greenBg"},"正常",-1)),t[12]||(t[12]=n()),s("b",null,a(u.turbineStatus?u.turbineStatus.normalCount:0),1)]),s("li",null,[t[13]||(t[13]=s("span",{class:"grayBg"},"其他",-1)),t[14]||(t[14]=n()),s("b",null,a(u.turbineStatus?u.turbineStatus.otherCount:0),1)])])]))),128))])):N("",!0)])]),_:1},8,["spinning"])):(e(),p(y,{key:1},{default:b(()=>t[15]||(t[15]=[n(" 无权限 ",-1)])),_:1,__:[15]}))}}},Y=k(j,[["__scopeId","data-v-00999f96"]]);export{Y as default};
