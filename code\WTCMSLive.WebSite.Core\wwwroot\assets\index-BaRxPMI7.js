import{r as O,w as Ne,j as Ue,ah as nt,c as q,o as F,i as A,s as j,b as k,d as _,f as ne,g as N,F as ge,t as Le,cg as ot,q as We,ch as ut,u as rt,y as dt,e as _e,x as B,bO as ct,p as pt,v as vt,m as f,aR as Ee}from"./index-BjOW8S1L.js";import{_ as bt,u as mt,c as Dt,W as se}from"./table-RP3jLHlo.js";import{F as ft,O as te}from"./index-CzSbT6op.js";import{_ as Se}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{B as Pe}from"./index-7iPMz_Qy.js";import{W as ht}from"./index-QXLii0rw.js";import{d as ae,S as It,f as me,a as yt}from"./tools-zTE6InS0.js";import{u as gt}from"./measurementDefinition-CZunja_V.js";import{u as Lt}from"./devTree-Dwa9wLl9.js";import{u as wt}from"./configModbus-CP03_5wA.js";import{D as Tt,a as At}from"./index-D82yULGq.js";import{M as kt}from"./index-BnSFuLp6.js";import"./ActionButton-C_grUmdF.js";import"./styleChecker-CFtINSLw.js";import"./initDefaultProps-P4j1rGDC.js";import"./shallowequal-gCpTBdTi.js";import"./index-DTxROkTj.js";import"./index-kP-mINcM.js";import"./index-BJEkaghg.js";import"./index-D3J_dKbJ.js";import"./index-CpBSPak5.js";const Rt={class:"tableBox"},xt={key:0},Ft={key:1},Ct={class:"addRow"},Vt={key:0,class:"footer"},qt={class:We(["footer-btn"])},Ot={key:0,class:"btnitem"},_t={key:1,class:"btnitem"},Et={__name:"TableBoxComponent",props:{tableColumns:Array,tableDatas:Array,recordKey:String,tableTitle:String,tableOperate:{type:Array,default:()=>[]},noCopyUpKeys:{type:Array,default:()=>[]},addBtnDisabled:Boolean,size:String,noPagination:Boolean,noForm:Boolean,noCopyUp:Boolean,modelValue:{type:Object,default:()=>({})}},emits:["addRow","deleteRow","editRow","submit","hangeTableFormChange","update:modelValue"],setup(n,{expose:de,emit:oe}){const a=n,R=O({...a.modelValue}),W=oe,x=O(a.tableDatas&&a.tableDatas.length?a.tableDatas:[{key:Date.now()}]);Ne(()=>a.tableDatas,o=>{x.value=o&&o.length?o:[{key:Date.now()}],!o||!o.length?R.value={}:o.forEach((d,h)=>{a.tableColumns.forEach(w=>{if(!w.noEdit){const D=`${w.dataIndex}[${h}]`;R.value[D]=d[w.dataIndex]}})})},{immediate:!0});const ce=()=>{const o=a.noCopyUp?{key:Date.now()}:{...x.value[x.value.length-1],key:Date.now()},d=x.value.length;a.tableColumns.forEach(h=>{if(!h.noEdit){const w=`${h.dataIndex}[${d}]`;R.value[w]=o[h.dataIndex]}}),W("update:modelValue",{...a.modelValue,...R.value}),x.value.push(o)},H=(o,d,h)=>{const w=`${o}[${d}]`;R.value[w]=h,x.value[d][o]=h,W("update:modelValue",{...a.modelValue,...R.value,updateModelValue:{value:h,dataIndex:o,index:d}})},g=(o,d,h)=>{W("hangeTableFormChange",{value:o,dataIndex:h,index:d})&&W("hangeTableFormChange",{value:o,dataIndex:h,index:d})},V=o=>{W("deleteRow",o);let d=x.value.filter(h=>h.key!==o.key);d.forEach((h,w)=>{Object.keys(h).forEach(D=>{const l=`${D}[${w}]`;R.value[l]=h[D]})}),W("update:modelValue",{...R.value}),x.value=d},T=o=>{const d={};a.tableColumns.forEach(l=>{if(l.canEdit){const L=`${l.dataIndex}[${o-1}]`;d[l.dataIndex]=R.value[L]}});const w={...x.value[o]};Object.keys(d).forEach(l=>{if(!a.noCopyUpKeys.includes(l)&&l!=="key"){w[l]=d[l];const L=`${l}[${o}]`;R.value[L]=d[l]}});const D=[...x.value];D[o]=w,x.value=[...D],W("update:modelValue",{...a.modelValue,...R.value})},C=Ue(()=>{var d;const o=a.tableColumns.filter(h=>!h.columnHidden);return(d=a.tableOperate)!=null&&d.length&&o.push({title:"操作",key:"action",dataIndex:"action"}),o});return de({getFieldsValue:()=>nt(R.value),setFieldValues:o=>{R.value={...o}},setFieldValue:(o,d)=>{R.value[o]=d}}),(o,d)=>{const h=Pe,w=bt;return F(),q(ge,null,[A("div",Rt,[k(w,{bordered:"",columns:C.value,"data-source":x.value,pagination:!n.noPagination&&x.value.length>10,size:n.size||"middle"},{headerCell:_(({column:D})=>[N(Le(D.title),1)]),bodyCell:_(({column:D,record:l,text:L,index:J})=>{var ue,c;return[D.key==="action"?(F(),q("div",xt,[(ue=n.tableOperate)!=null&&ue.includes("copyUp")&&J>0?(F(),ne(h,{key:0,onClick:S=>T(J)},{default:_(()=>d[1]||(d[1]=[N("同上",-1)])),_:2,__:[1]},1032,["onClick"])):j("",!0),(c=n.tableOperate)!=null&&c.includes("delete")?(F(),ne(h,{key:1,onClick:S=>V(l)},{default:_(()=>d[2]||(d[2]=[N("删除",-1)])),_:2,__:[2]},1032,["onClick"])):j("",!0)])):D.noEdit?(F(),q(ge,{key:2},[N(Le(L),1)],64)):(F(),q("div",Ft,[k(ft,{class:"formItem",notshowLabels:!0,itemProps:{...D,formItemWidth:D.columnWidth,dataIndex:D.dataIndex+"["+J+"]"},modelValue:R.value[D.dataIndex+"["+J+"]"],"onUpdate:modelValue":S=>H(D.dataIndex,J,S),onOnchangeSelect:S=>D.hasChangeEvent?g(S,J,D.dataIndex):null},null,8,["itemProps","modelValue","onUpdate:modelValue","onOnchangeSelect"])]))]}),_:1},8,["columns","data-source","pagination","size"]),A("div",Ct,[k(h,{onClick:d[0]||(d[0]=D=>ce())},{default:_(()=>d[3]||(d[3]=[N("添加一行",-1)])),_:1,__:[3]})])]),n.noForm?j("",!0):(F(),q("div",Vt,[ot(o.$slots,"footer",{},void 0),A("div",qt,[a.noCancle?j("",!0):(F(),q("div",Ot,[k(h,{onClick:o.cancelForm},{default:_(()=>d[4]||(d[4]=[N("取消",-1)])),_:1,__:[4]},8,["onClick"])])),a.noSubmit?j("",!0):(F(),q("div",_t,[k(h,{type:"primary","html-type":"submit"},{default:_(()=>d[5]||(d[5]=[N("确定",-1)])),_:1,__:[5]})]))])]))],64)}}},Me=Se(Et,[["__scopeId","data-v-c7b94e20"]]),I=320,Q=n=>{let de=[{align:"center",formItemWidth:I,title:"测量定义名称",dataIndex:"measDefinitionName",isrequired:!0},{align:"center",title:"测量定义状态",formItemWidth:I,dataIndex:"isAvailable",inputType:"radio",isrequired:!0,selectOptions:[{value:"1",label:"启用"},{value:"0",label:"禁用"}]},{align:"center",title:"采集单元",dataIndex:"dauID",isrequired:!0,inputType:"select",selectOptions:[],disabled:n&&n.edit,hasChangeEvent:!0,formItemWidth:I},{align:"center",title:"采集间隔",dataIndex:"daqInterval",formItemWidth:I,validateRules:ae({title:"采集间隔",type:"number",required:!0})},{align:"center",title:"采集模式",formItemWidth:I,dataIndex:"modelType",inputType:"select",selectOptions:[{label:"主动",value:0},{label:"被动",value:1}]},{align:"center",title:"Modbus设备",formItemWidth:I,dataIndex:"modbusDeviceIDs",inputType:"checkbox",selectOptions:[],disabled:n&&n.edit},{align:"center",title:"采集间隔单位",dataIndex:"daqIntervalUnit",inputType:"select",formItemWidth:I,selectOptions:[],isrequired:!0}],oe=[{align:"center",title:"波形定义名称",dataIndex:"waveDefinitionName",isrequired:!0,formItemWidth:I},{title:n&&n.isForm?"测量定义名称":"振动测量位置",dataIndex:n&&n.isForm?"measDefinitionName":"measLocationID",align:"center",disabled:!0,isrequired:!0,formItemWidth:I},{align:"center",title:"信号类型",dataIndex:"signalType",isrequired:!0,inputType:"select",selectOptions:[],formItemWidth:I,headerOperations:{filters:[],filterOptions:n&&n.waveTypeList&&n.waveTypeList.length>0?n.waveTypeList:[]},...n&&!n.isForm&&n.waveTypeList?{customRender:({text:g,record:V,index:T,column:C})=>{const o=n.waveTypeList.find(d=>d.value==V.signalType);return o?o.label:g}}:{}},{align:"center",title:"信号带宽(Hz)",dataIndex:"upperLimitFreqency",isrequired:!0,inputType:"selectinput",selectOptions:[],formItemWidth:I},{align:"center",title:"采样长度(s)",dataIndex:"waveDefParamID",formItemWidth:I,validateRules:ae({title:"采样长度",type:"number",required:!0})}],a=[{align:"center",title:"波形定义名称",dataIndex:"waveDefinitionName",isrequired:!0,formItemWidth:I},{title:n&&n.isForm?"测量定义名称":"振动测量位置",dataIndex:n&&n.isForm?"measDefinitionName":"measLocationID",align:"center",disabled:!0,isrequired:!0,formItemWidth:I},{align:"center",title:"包络带宽(Hz)",dataIndex:"envBandWidth",isrequired:!0,inputType:"select",selectOptions:[],formItemWidth:I},{align:"center",title:"包络滤波器(Hz)",dataIndex:"envFiterFreq",isrequired:!0,inputType:"select",selectOptions:[],formItemWidth:I},{align:"center",title:"采样长度(s)",dataIndex:"sampleLength",formItemWidth:I,customRender:({text:g,record:V,index:T,column:C})=>V.waveDefParamID?V.waveDefParamID:"",validateRules:ae({title:"采样长度",type:"number",required:!0})}],R=[{align:"center",title:"波形定义名称",dataIndex:"waveDefinitionName",isrequired:!0,formItemWidth:I},{title:n&&n.isForm?"测量定义名称":"振动测量位置",dataIndex:n&&n.isForm?"measDefinitionName":"measLocationID",align:"center",disabled:!0,isrequired:!0,formItemWidth:I},{align:"center",title:"信号类型",dataIndex:"signalType",isrequired:!0,inputType:"select",selectOptions:[],disabled:n&&n.edit,formItemWidth:I,headerOperations:{filters:[],filterOptions:n&&n.waveTypeList&&n.waveTypeList.length>0?n.waveTypeList:[]},...n&&!n.isForm&&n.waveTypeList?{customRender:({text:g,record:V,index:T,column:C})=>{const o=n.waveTypeList.find(d=>d.value==V.signalType);return o?o.label:g}}:{}},{align:"center",title:"采样率(Hz)",isrequired:!0,dataIndex:"upperLimitFreqency",inputType:"select",selectOptions:[],formItemWidth:I},{align:"center",title:"采样长度(s)",formItemWidth:I,dataIndex:"waveDefParamID",validateRules:ae({title:"采样长度",type:"number",required:!0})}],W=[{align:"center",title:"工况类型",dataIndex:"measLocationID",isrequired:!0,inputType:"select",selectOptions:[],disabled:n&&n.edit,canEdit:!0,hasChangeEvent:!0,labelInValue:!0,customRender:({text:g,record:V,index:T,column:C})=>V.measLocName?V.measLocName:""},{title:"上限频率(Hz)",dataIndex:"parmaChannelNumber",align:"center",inputType:"select",selectOptions:[{label:"1",value:"1"},{label:"50",value:"50"},{label:"100",value:"100"},{label:"1K",value:"1000"}],canEdit:!0},{align:"center",title:"采样长度(s)",dataIndex:"param_Type_Name",canEdit:!0,validateRules:ae({title:"采样长度",type:"number",required:!0})}],x=[{align:"center",title:"转速测量位置",dataIndex:"measLocationID",isrequired:!0,inputType:"select",selectOptions:[],hasChangeEvent:!0,labelInValue:!0,disabled:n&&n.edit,canEdit:!0,customRender:({text:g,record:V,index:T,column:C})=>V.measLocName?V.measLocName:""},{align:"center",title:"波形线数",dataIndex:"lineCounts",canEdit:!0}],ce=[{align:"center",title:"波形定义名称",dataIndex:"waveDefinitionName",isrequired:!0,formItemWidth:I},{title:"Modbus设备",dataIndex:"modbusDeviceID",align:"center",formItemWidth:I,inputType:"select",selectOptions:[],disabled:n&&n.edit,isrequired:!0,hasChangeEvent:!0,...n&&n.isForm?{}:{customRender:({text:g,record:V})=>V.modbusDeviceName||""}},{title:"Modbus测量位置",dataIndex:"measLocationID",align:"center",formItemWidth:I,inputType:"select",selectOptions:[],isrequired:!0,tableList:[],disabled:n&&n.edit,...n&&n.isForm?{}:{customRender:({text:g,record:V})=>V.measLocationName||""}},{align:"center",title:"信号类型",dataIndex:"singleType",isrequired:!0,inputType:"select",selectOptions:[],formItemWidth:I,...n&&n.isForm?{}:{customRender:({text:g,record:V})=>V.singleTypeName||""}},{align:"center",title:"采样频率(Hz)",dataIndex:"sampleRate",isrequired:!0,formItemWidth:I,columnWidth:100},{align:"center",title:"采样长度(s)",dataIndex:"sampleLength",isrequired:!0,formItemWidth:I,columnWidth:100}],H=[{align:"center",title:"触发规则名称",dataIndex:"ruleName",isrequired:!0,formItemWidth:I},{title:"测量定义名称",dataIndex:"measdName",align:"center",formItemWidth:I,isrequired:!0,disabled:!0},{align:"center",title:"触发规则",dataIndex:"triggerRule",formItemWidth:I,hidden:!!(n&&n.isForm)},{align:"center",title:"被触发测量定义",dataIndex:"triggerMeasdedName",formItemWidth:I,hidden:!!(n&&n.isForm)},{align:"center",title:"触发采集类型",dataIndex:"triggerRuleType",formItemWidth:I,inputType:"select",hasChangeEvent:!0,headerOperations:{filters:[{text:"工况",value:"工况"},{text:"时间",value:"时间"}]},selectOptions:[{label:"工况",value:"工况"},{label:"时间",value:"时间"}]}];return[de,oe,a,R,W,x,ce,H]},Mt=()=>[{align:"center",title:"下限频率",dataIndex:"underLimitValue",canEdit:!0,columnWidth:230,validateRules:ae({title:"下限频率",type:"number",required:!0})},{title:"上限频率",dataIndex:"upperLimitValue",align:"center",canEdit:!0,columnWidth:230,validateRules:ae({title:"上限频率",type:"number",required:!0})}],Nt=()=>[{align:"center",title:"特征值类型",dataIndex:"type",canEdit:!0,isrequired:!0,inputType:"select",selectOptions:[],columnWidth:200},{title:"逻辑关系",dataIndex:"relationship",align:"center",canEdit:!0,inputType:"select",isrequired:!0,columnWidth:100,selectOptions:[{label:">",value:">"},{label:"<",value:"<>"},{label:">=",value:">="},{label:"<=",value:"<="},{label:"=",value:"="},{label:"!=",value:"!="}]},{align:"center",title:"数值",dataIndex:"value",editable:!0,isrequired:!0,canEdit:!0,columnWidth:200}],Re=()=>[{align:"center",title:"选择采集单元",dataIndex:"collectionUnit",inputType:"select",isrequired:!0,selectOptions:[],hasChangeEvent:!0,labelInValue:!0,formItemWidth:I},{align:"center",hasLabelPosition:!0,dataIndex:"measLocIds",inputType:"checkboxGroup",defaultCheckAll:!0,labelInValue:!0,formItemWidth:"600",selectOptions:[],cancheckall:!0,defaultCheckAll:!0,isdisplay:!1},{title:"",hasLabelPosition:!0,dataIndex:"generalEVList",align:"center",inputType:"checkboxGroup",selectOptions:[],formItemWidth:"600",defaultCheckAll:!0},{align:"center",title:"",dataIndex:"isAll",inputType:"checkbox",selectOptions:[{value:"1",label:"将通带特征值配置和频带特征值配置应用到其他波形定义"}]},{title:"特征值配置",dataIndex:"evLists",align:"center",inputType:"checkbox",width:"100%",selectOptions:[{value:"41",label:"均值"},{value:"38",label:"最大值"},{value:"39",label:"最小值"}],cancheckall:!0},{title:"",dataIndex:"generalEVList",hasLabelPosition:!0,align:"center",inputType:"checkboxGroup",selectOptions:[],formItemWidth:"600",defaultCheckAll:!0},{title:"时间间隔(分钟)",dataIndex:"timeInterval",align:"center",formItemWidth:I,validateRules:ae({title:"时间间隔",type:"number"})}],Ut={class:"btnGroups"},Wt={class:"clearfix"},St=["onClick"],Pt={class:"baseInfo"},$t={class:"border"},Gt={class:"tableItems"},Bt={class:"tableItems"},Kt={class:"tableItems"},zt={class:"tableItems"},jt={class:"tableItems"},Ht={class:"tableItems"},Jt={class:"tableItems"},Qt={key:1,class:"nodata"},Xt={key:0},Yt={key:0,class:"modalContent"},Zt={class:"otherFromContent"},ea={class:"getMeasLocName"},ta={key:0,class:"modalPart"},aa={class:"modalPart"},ia={class:"modalPart"},la={key:1,class:"otherFromContent"},sa={class:"getMeasLocName"},na={key:0,class:"checkboxGroup1"},oa={key:2,class:"otherFromContent"},ua={key:0},ra={key:3,class:"otherFromContent"},da={class:"modalPart"},ca={class:"modalPart"},pa={__name:"index",setup(n){const de=Lt(),oe=rt(),a=gt(),{measdList:R,waveTypeList:W}=ut(a),x=wt(),ce=mt(),H=O(!1),g=O(""),V=O(!1),T=O(""),C=O({}),o=O(),d=O(),h=O({}),w=O([]),D=O(Re()),l=O({}),L=O(""),J=O([]),ue=O({}),c=O(oe.params.id),S=O(),we=O(!0),s=dt({table1Data:[],table2Data:[],table3Data:[],table4Data:[],table5Data:[],table6Data:[],table7Data:[],modal7TableColumns:Nt(),batchApplyData:[],batchApplyKey:"",bathApplyResponse1:{},bathApplyResponse2:{},bathApplyResponse3:{},bathApplyResponse4:{},bathApplyResponse5:{},bathApplyResponse6:{},bathApplyResponse7:{}}),$e=async e=>{S.value&&await ce.fetchDevTreedDevicelist({windParkID:S.value,useTobath:!0})},xe=async()=>{(!a.measdDaqIntervalUnitType||!a.measdDaqIntervalUnitType.length)&&await a.fetchGetMeasdDaqIntervalUnitType()},Ge=Ue(()=>L.value==="batchAdd"?"1200px":T.value==="0"||T.value==="6"?"600px":"700px"),Te=async()=>{if(await a.fetchGetMeasdList({turbineID:c.value}),JSON.stringify(l.value)!=="{}"){let e=R.value.find(t=>t.measDefinitionID==l.value.measDefinitionID);l.value=e}else l.value=R.value[0]},Ae=async e=>{if(H.value=!0,await a.fetchGetMeasdList({turbineID:c.value}),H.value=!1,R.value&&R.value.length){if(e&&e=="edit"&&JSON.stringify(l.value)!=="{}"){let t=R.value.find(i=>i.measDefinitionID==l.value.measDefinitionID);l.value=t}else l.value=R.value[0];if(De(l.value),e&&e=="edit")return;await ke(),fe(),he(),Ie(),pe(),ve(),be(),ye()}else l.value={},De({})},De=e=>{var i;ue.value=e;let t="";a.measdDaqIntervalUnitType&&a.measdDaqIntervalUnitType.length&&(t=(i=a.measdDaqIntervalUnitType.find(b=>{var u;return b.value==((u=e.mdf_Ex)==null?void 0:u.daqIntervalUnit)}))==null?void 0:i.label),J.value=[{label:"测量定义名称",value:e.measDefinitionName},{label:"测量定义状态",value:e.isAvailable?"开启":"禁用"},{label:"采集单元",value:e.dauName},{label:"采集模式",value:e.mdf_Ex?e.mdf_Ex.modelType==1?"被动":"主动":""},{label:"采集间隔",value:e.mdf_Ex?`${e.mdf_Ex.daqInterval}${t}`:""}]},Be=()=>{let e=de.findAncestorsWithNodes(c.value);e&&e.length&&e.length>1&&(S.value=e[e.length-2].id)};Ne(()=>oe.params.id,async e=>{e&&(a.reset(),x.reset(),l.value={},De({}),c.value=e,Be(),await xe(),await $e(),Ae())},{immediate:!0});const X=async(e,t)=>{let i=[...D.value],b=[...w.value];if(e){switch(T.value){case"0":console.log("dauID",e,t),e.dataIndex=="dauID"&&(e.value=="无"?(b[3].validateRules[0].required=!0,b[3].isdisplay=!0):(b[3].validateRules[0].required=!1,b[3].isdisplay=!1));break;case"1":case"2":case"3":let u=[];e.dataIndex=="collectionUnit"&&(e.value!=="无"?(T.value=="3"?(await He(e.value.value),u=a.unusedVoltageCurrentMeasLocList):T.value=="1"?(await je(e.value.value),u=a.vibMeasLocList):T.value=="2"&&(await Je(e.value.value),u=a.parameUnusedVibMeasLocList),i[1].selectOptions=u,i[1].isdisplay=!!(u&&u.length)):(i[1].selectOptions=[],i[1].isdisplay=!1));break;case"4":e.dataIndex&&e.dataIndex=="measLocationID"&&(b[3].tableList[e.index].selectOptions=[{label:e.value.label,value:"1"}]);break;case"5":e.dataIndex&&e.dataIndex=="measLocationID"&&(b[2].tableList[e.index].selectOptions=[{label:e.value.label,value:"1"}]);break;case"6":if(e.dataIndex&&e.dataIndex=="modbusDeviceID")if(await x.fetchGetModbusChannelList({turbineID:c.value,modbusDeviceID:e.value}),console.log(e),L.value=="batchAdd"){if(e.index>=b[2].tableList.length)for(let r=b[2].tableList.length;r<=e.index;r++)b[2].tableList.push({});b[2].tableList[e.index].selectOptions=x.modbusLocaOptions,d.value.setTableFieldValue({formDataIndex:`measLocationID[${e.index}]`,tableDataIndex:"measLocationID",index:e.index,value:x.modbusLocaOptions&&x.modbusLocaOptions.length?x.modbusLocaOptions[0].value:""})}else b[2].selectOptions=x.modbusLocaOptions;break;case"7":e.dataIndex&&e.dataIndex=="triggerRuleType"&&(we.value=e.value=="工况");return}w.value=[...b],D.value=[...i]}},Ke=async()=>{(!a.dauList||!a.dauList.length)&&await a.fetchGetDauList({turbineID:c.value})},ze=async()=>{(!a.initUpperLimitFreqList||!a.initUpperLimitFreqList.length)&&await a.fetchGetInitUpperLimitFreqList()},je=async e=>{await a.fetchGetVibMeasLocList({turbineID:c.value,MeasDefinitionID:l.value.measDefinitionID,DAUID:e})},He=async e=>{await a.fetchGetUnusedVoltageCurrentMeasLocList({turbineID:c.value,MeasDefinitionID:l.value.measDefinitionID,DAUID:e})},Je=async e=>{await a.fetchGetParameUnusedVibMeasLocList({turbineID:c.value,MeasDefinitionID:l.value.measDefinitionID,DAUID:e})},Qe=async()=>{(!a.eigenValueTypeList||!a.eigenValueTypeList.length)&&await a.fetchGetEigenValueTypeList()},Xe=async()=>await a.fetchGetWorkConOptionList({turbineID:c.value,MeasDefId:l.value.measDefinitionID}),Ye=async()=>await a.fetchGetWorkCondSpdMeasdOptions({WindTurbineID:c.value,MeasDefinitionID:l.value.measDefinitionID}),Ze=async()=>{(!a.envelopeFilterList||!a.envelopeFilterList.length)&&await a.fetchGetShowEnvelopeFilterList()},et=async()=>{(!a.upperLimitFreqList||!a.upperLimitFreqList.length)&&await a.fetchGetUpperLimitFreqList()},ke=async()=>{(!a.waveTypeList||!a.waveTypeList.length)&&await a.fetchGetWaveTypeList()},Fe=()=>{var i;const e=(i=o.value)==null?void 0:i.getFieldsValue();let t="";T.value==1?t=(e.measDefinitionName||"")+(e.upperLimitFreqency||"")+"Hz"+(e.waveDefParamID||"")+" 秒":T.value==2?t="高频包络"+(e.envBandWidth||"")+"Hz"+(e.envFiterFreq||"")+"Hz"+(e.sampleLength||"")+" 秒":T.value==3&&(t=(e.measDefinitionName||"")+(e.upperLimitFreqency||"")+"Hz"+(e.waveDefParamID||"")+" 秒"),o.value.setFieldValue("waveDefinitionName",t),t&&t!==""&&o.value.clearValidate("waveDefinitionName")},Ce=()=>{V.value=!0},Y=e=>{const{title:t,operateType:i,tableKey:b}=e;let u=Q({isForm:!0,validateWaveLineCounts:qe})[b];L.value=i,T.value=b;let r=Re(),p={tableDatas:[]};switch(b){case"0":g.value="添加测量定义",p={isAvailable:"1",daqIntervalUnit:0};break;case"1":g.value="添加时域波形定义",p={measDefinitionName:l.value.measDefinitionName?l.value.measDefinitionName:""};break;case"3":g.value="添加电流电压波形定义",p={measDefinitionName:l.value.measDefinitionName?l.value.measDefinitionName:""};break;case"2":g.value="添加高频包络波形定义",p={measDefinitionName:l.value.measDefinitionName?l.value.measDefinitionName:""};break;case"4":case"5":u.push({title:"工况监视特征值",dataIndex:"evAvg",inputType:"checkbox",selectOptions:[],tableList:[{selectOptions:[]}]}),b=="4"?g.value="添加工况波形定义":g.value="添加转速波形定义";break;case"6":g.value="添加Modbus设备波形定义",u.push({...r[4],columnWidth:200,width:""});break;case"7":g.value="添加触发采集配置",p={measdName:l.value.measDefinitionName,triggerRuleType:"工况"};break}C.value={...p},w.value=u,D.value=[...r],Ve(b),Ce()},tt=async e=>{switch(T.value){case"0":let t={...e,measDefinitionID:L.value=="edit"?C.value.measDefinitionID:"",windTurbineID:c.value,windParkID:S.value,isAvailable:e.isAvailable&&e.isAvailable=="1",dauid:e.DauID&&e.DauID=="无"?"":e.DauID,modbusDeviceIDs:e.modbusDeviceIDs&&e.modbusDeviceIDs.length?e.modbusDeviceIDs.join(","):""},i={};L.value=="edit"?i=await a.fetchEditMeasDefinition(t):i=await a.fetchAddMeasDefinition(t),i&&i.code===1?(Ae(L.value),P(),f.success("提交成功")):f.error("提交失败:"+i.msg);break;case"1":case"3":let b=me(e),u="";b&&b.length&&b.forEach((re,Oe)=>{Oe>0&&(u+=","),u+="FBE#"+re.underLimitValue+"#"+re.upperLimitValue});let r={...e,waveDefinitionID:L.value=="edit"?C.value.waveDefinitionID:"",measDefinitionID:l.value.measDefinitionID,windTurbineID:c.value,windParkID:S.value,measLocIds:e.measLocIds&&e.measLocIds.length?e.measLocIds.join(","):"",messLocNames:"",dauid:e.collectionUnit&&e.collectionUnit.value?e.collectionUnit.value:"",isAll:!!(e.isAll&&e.isAll=="1"),FBEList:u,generalEVList:e.generalEVList&&e.generalEVList.length?e.generalEVList.join(","):"",timeWdfSampleLength:e.waveDefParamID,timeWdfUpFreq:e.upperLimitFreqency,isAddType:L.value=="edit"?"0":"1"},p={};T.value=="3"?p=await a.fetchMakeWaveDefinitionVoltageCurrent({sourceData:r,targetTurbineIds:s.batchApplyData}):p=await a.fetchMakeWaveDefinition({sourceData:r,targetTurbineIds:s.batchApplyData}),p&&p.code===1?(T.value=="3"?(Ie(),s.bathApplyResponse3=p.batchResults||{}):(fe(),s.bathApplyResponse1=p.batchResults||{}),f.success("提交成功"),P()):f.error("提交失败:"+p.msg);break;case"2":let v={...e,waveDefinitionID:L.value=="edit"?C.value.waveDefinitionID:"",measDefinitionID:l.value.measDefinitionID,windTurbineID:c.value,windParkID:S.value,measLocIds:e.measLocIds&&e.measLocIds.length?e.measLocIds.join(","):"",messLocNames:"",dauid:e.collectionUnit&&e.collectionUnit.value?e.collectionUnit.value:"",isAll:!!(e.isAll&&e.isAll=="1"),timeWdfSampleLength:e.waveDefParamID,isAddType:L.value=="edit"?"0":"1",FBEList:"",GeneralEVList:""};const m=await a.fetchMakeParamEnvDefinition({sourceData:v,targetTurbineIds:s.batchApplyData});m&&m.code===1?(he(),s.bathApplyResponse2=m.batchResults||{},P(),f.success("提交成功")):f.error("提交失败:"+m.msg);break;case"4":let y={dauID:l.value.dauID,windTurbineID:c.value,measDefinitionID:l.value.measDefinitionID,evAvg:!!(e.evAvg&&e.evAvg.length),measLocId:e.measLocationID&&e.measLocationID.length>0?e.measLocationID[0].value:"",upperLimitFreqency:e.parmaChannelNumber,sampleLength:e.param_Type_Name},U=await a.fetchBatchEditWorkCondMeasd({sourceData:[y],targetTurbineIds:s.batchApplyData});U&&U.code===1?(pe(),s.bathApplyResponse4=U.batchResults||{},f.success("提交成功"),P(),Te()):f.error("提交失败:"+U.msg);break;case"5":let ie={...e,upperLimitFreqency:e.lineCounts?e.lineCounts*1:0,dauID:l.value.dauID,windTurbineID:c.value,measDefinitionID:l.value.measDefinitionID,evAvg:!!(e.evAvg&&e.evAvg.length),measLocId:C.value.measLocId},$=await a.fetchBatchEditWorkCondSpdMeasd({sourceData:[ie],targetTurbineIds:s.batchApplyData});$&&$.code===1?(ve(),s.bathApplyResponse5=$.batchResults||{},f.success("提交成功"),P()):f.error("提交失败:"+$.msg);break;case"6":let E={...e,windTurbineID:c.value,measDefinitionID:l.value.measDefinitionID,waveDefinitionID:C.value.waveDefinitionID||"",evLists:e.evLists?e.evLists:[]},M={};L.value=="add"?M=await a.fetchMeasdAddModbusWave({sourceData:[E],targetTurbineIds:s.batchApplyData}):M=await a.fetchEditModbusWave({sourceData:E,targetTurbineIds:s.batchApplyData}),M&&M.code===1?(be(),s.bathApplyResponse6=M.batchResults||{},f.success("提交成功"),P()):f.error("提交失败:"+M.msg);break;case"7":let K=me(e),le=[];K&&K.length&&K.forEach((re,Oe)=>{le.push(re.type+","+re.relationship+","+re.value)});let G={isAddType:L.value=="edit"?"0":"1",turbineID:c.value,triggerRuleName:e.ruleName,triggerMeasDefName:l.value.measDefinitionName,conditionMonitoringLocIds:e.generalEVList&&e.generalEVList.length?e.generalEVList.join("#"):"",triggerData:le,dauid:l.value.dauID||"",triggertime:e.timeInterval||""},z=await a.fetchAddMeasdTirggerAcq({sourceData:G,targetTurbineIds:s.batchApplyData});z&&z.code===1?(ye(),s.bathApplyResponse7=z.batchResults||{},P(),f.success("提交成功")):f.error("提交失败:"+z.msg);break}},at=async e=>{switch(T.value){case"4":let t=me(e);if(t&&t.length){let r=t.map((v,m)=>({dauID:l.value.dauID,windTurbineID:c.value,measDefinitionID:l.value.measDefinitionID,evAvg:!!(v.evAvg&&v.evAvg.length),measLocId:v.measLocationID,upperLimitFreqency:v.parmaChannelNumber,sampleLength:v.param_Type_Name})),p=await a.fetchBatchAddWorkCondMeasd({sourceData:r,targetTurbineIds:s.batchApplyData});p&&p.code===1?(pe(),s.bathApplyResponse4=p.batchResults||{},f.success("提交成功"),P(),Te()):f.error("提交失败:"+p.msg)}break;case"5":let i=me(e);if(i&&i.length){let r=i.map((v,m)=>({dauID:l.value.dauID,windTurbineID:c.value,measDefinitionID:l.value.measDefinitionID,evAvg:!!(v.evAvg&&v.evAvg.length),measLocId:v.measLocationID,upperLimitFreqency:v.lineCounts,sampleLength:v.gearRatio})),p=await a.fetchBatchAddWorkCondSpdMeasd({sourceData:r,targetTurbineIds:s.batchApplyData});p&&p.code===1?(ve(),s.bathApplyResponse5=p.batchResults||{},f.success("提交成功"),P()):f.error("提交失败:"+p.msg)}break;case"6":console.log(e);let b={windTurbineID:c.value,measDefinitionID:l.value.measDefinitionID,waveDefinitionID:""},u=me(e,b);if(u&&u.length){u.forEach((p,v)=>{p.evLists=p.evLists?p.evLists:[]});let r=await a.fetchMeasdAddModbusWave({sourceData:u,targetTurbineIds:s.batchApplyData});r&&r.code===1?(be(),s.bathApplyResponse6=r.batchResults||{},f.success("提交成功"),P()):f.error("提交失败:"+r.msg)}break}},Z=e=>{const{tableKey:t,rowData:i}=e;T.value=t,L.value="edit";let b=Re();D.value=b;let u=Q({isForm:!0,edit:!0,validateWaveLineCounts:qe})[t],r={measDefinitionName:l.value.measDefinitionName?l.value.measDefinitionName:""};switch(t){case"0":g.value="编辑测量定义",i.dauID&&(u[3].isdisplay=!1,u[3].validateRules[0].required=!1);let p=[];i.modbusUnits&&i.modbusUnits.length&&i.modbusUnits.map(E=>{p.push(E.modbusDeviceID)}),r={...r,...i,...i.mdf_Ex,isAvailable:i.isAvailable?"1":"0",dauID:i.dauID?i.dauID:"无",modbusDeviceIDs:p};break;case"1":case"3":let v=[],m=[],y={},U=0;i.vibEigenValueConf&&i.vibEigenValueConf.length&&i.vibEigenValueConf.forEach((E,M)=>{E.evType==0?v.push(E.type):E.evType==1&&(m.push({key:M,underLimitValue:E.underLimitValue,upperLimitValue:E.upperLimitValue}),y[`underLimitValue[${U}]`]=E.underLimitValue,y[`upperLimitValue[${U}]`]=E.upperLimitValue,U++)}),r={...r,...i,generalEVList:v,tableDatas:m,...y},t=="1"?g.value="编辑时域波形定义":g.value="编辑电流电压波形定义";break;case"2":g.value="编辑高频包络波形定义",r={...r,...i,sampleLength:i.waveDefParamID};break;case"4":case"5":u.push({title:"工况监视特征值",dataIndex:"evAvg",inputType:"checkbox",selectOptions:[{label:i.measLocName,value:"1"}]}),r={...r,...i,measLocId:i.measLocationID,evAvg:i.evAvg?["1"]:[],measLocationID:[{label:i.measLocName,value:i.measLocationID}],upperLimitFreqency:i.parmaChannelNumber},t=="4"?g.value="编辑工况波形定义":g.value="编辑转速波形定义";break;case"6":g.value="编辑Modbus设备波形定义",r={...r,...i,evLists:i.eigenValues},w.value=[...u],i.modbusDeviceID&&i.modbusDeviceID!==""&&X({dataIndex:"modbusDeviceID",value:i.modbusDeviceID});break;case"7":g.value="编辑触发采集配置";let ie=[],$="";i.triggerRuleType&&(we.value=i.triggerRuleType=="工况",i.triggerRuleList&&i.triggerRuleList.length&&(i.triggerRuleType=="工况"?i.triggerRuleList.forEach((E,M)=>{let K=E.split(",");ie.push({key:M,type:K[0],relationship:K[1],value:K[2]})}):$=i.triggerRuleList[0])),r={...r,...i,generalEVList:i.triggerMeasdId,timeInterval:$,tableDatas:ie};break}t!=="6"&&(w.value=[...u]),Ve(t),C.value={...r},Ce()},ee=async e=>{const{tableKey:t,selectedkeys:i,rowData:b,record:u}=e;if((!i||!i.length)&&t!=="0"){f.error("请选择要删除的行");return}switch(t){case"0":const r=await a.fetchDeleteMeasDef({windTurbineID:c.value,measDefId:b.measDefinitionID});r&&r.code===1?(Ae(),f.success("删除成功")):f.error("删除失败:"+r.msg);break;case"1":const p=await a.fetchDeleteWaveChannel({sourceData:{windTurbineID:c.value,measDefId:l.value.measDefinitionID,waveId:i.join(",")},targetTurbineIds:s.batchApplyData});p&&p.code===1?(fe(),s.bathApplyResponse1=p.batchResults||{},f.success("删除成功")):f.error("删除失败:"+p.msg);break;case"2":const v=await a.fetchDeleteParamEnvChannel({sourceData:{windTurbineID:c.value,measDefId:l.value.measDefinitionID,waveId:i.join(",")},targetTurbineIds:s.batchApplyData});v&&v.code===1?(he(),s.bathApplyResponse2=v.batchResults||{},f.success("删除成功")):f.error("删除失败:"+v.msg);break;case"3":const m=await a.fetchDeleteWaveChannelVoltageCurrenl({sourceData:{windTurbineID:c.value,measDefId:l.value.measDefinitionID,waveId:i.join(",")},targetTurbineIds:s.batchApplyData});m&&m.code===1?(Ie(),s.bathApplyResponse3=m.batchResults||{},f.success("删除成功")):f.error("删除失败:"+m.msg);break;case"4":let y=[];i.forEach((G,z)=>{y.push({windTurbineID:c.value,dauID:l.value.dauID,measDefinitionID:l.value.measDefinitionID,measLocId:G})});let U=await a.fetchBatchDeleteWorkCondMeasd({sourceData:y,targetTurbineIds:s.batchApplyData});U&&U.code===1?(pe(),s.bathApplyResponse4=U.batchResults||{},f.success("删除成功"),Te()):f.error("删除失败:"+U.msg);break;case"5":let ie=[];i.forEach((G,z)=>{ie.push({windTurbineID:c.value,dauID:l.value.dauID,measDefinitionID:l.value.measDefinitionID,measLocId:G})});let $=await a.fetchBatchDeleteWorkCondSpdMeasd({sourceData:ie,targetTurbineIds:s.batchApplyData});$&&$.code===1?(ve(),s.bathApplyResponse5=$.batchResults||{},f.success("删除成功")):f.error("删除失败:"+$.msg);break;case"6":let E=[];if(u)E.push({windTurbineID:c.value,measDefinitionID:u.measDefinitionID,waveDefinitionID:u.waveDefinitionID,measLocationID:u.measLocationID});else for(let G=0;G<i.length;G++){let z=i[G].split("&&");E.push({windTurbineID:c.value,measDefinitionID:l.value.measDefinitionID,waveDefinitionID:z[0],measLocationID:z[1]})}let M=await a.fetchBatchDeleteModbusWave({sourceData:E,targetTurbineIds:s.batchApplyData});M&&M.code===1?(be(),s.bathApplyResponse6=M.batchResults||{},f.success("删除成功")):f.error("删除失败:"+M.msg);break;case"7":let K=[];i.forEach((G,z)=>{K.push({turbineID:c.value,dauID:l.value.dauID,triggerRuleName:G,triggerMeasDefName:l.value.measDefinitionName})});let le=await a.fetchDeleteTriggerGatherDispose({sourceData:K,targetTurbineIds:s.batchApplyData});le&&le.code===1?(ye(),s.bathApplyResponse7=le.batchResults||{},f.success("删除成功")):f.error("删除失败:"+le.msg);break}},it=async()=>{(!x.modbusDeviceOptions||!x.modbusDeviceOptions.length)&&await x.fetchGetModbusDeviceList({turbineID:c.value})},Ve=async e=>{const t=w.value;if(!t||!t.length)return;const i=D.value;await Ke();const b=[{label:"无",value:"无"},...a.dauList];switch(e){case"0":await it(),await xe(),t[5].selectOptions=[...x.modbusDeviceOptions],t[2].selectOptions=b,t[6].selectOptions=[...a.measdDaqIntervalUnitType],w.value=[...t];break;case"1":case"3":await ke(),await ze(),await Qe(),t[2].selectOptions=a.waveTypeList,t[3].selectOptions=a.initUpperLimitFreqList,i[0].selectOptions=b,i[2].selectOptions=a.eigenValueTypeList,w.value=[...t],D.value=[...i];break;case"2":await Ze(),await et(),t[2].selectOptions=a.upperLimitFreqList,t[3].selectOptions=a.envelopeFilterList,w.value=[...t],i[0].selectOptions=b,D.value=[...i];break;case"4":case"5":if(L.value=="batchAdd"){let u=[];e=="4"?u=await Xe():u=await Ye(),t[0].selectOptions=u,w.value=[...t]}break;case"6":if(l.value.modbusUnits&&l.value.modbusUnits.length){let u=yt(l.value.modbusUnits,{label:"modbusDeviceName",value:"modbusDeviceID"});t[1].selectOptions=u}else t[1].selectOptions=[];await ke(),t[3].selectOptions=a.waveTypeList;break;case"7":if(l.value.superviseEvName&&l.value.superviseEvName!==""){let u=l.value.superviseEvName.split(","),r=[];u.forEach((p,v)=>{r.push({label:p,value:p})}),s.modal7TableColumns[0].selectOptions=r}else s.modal7TableColumns[0].selectOptions=[];if(l.value.mdf_Ex&&l.value.mdf_Ex.modelType==0){let u=[];a.measdList.forEach((r,p)=>{r.mdf_Ex&&r.mdf_Ex.modelType==1&&u.push({label:r.measDefinitionName,value:r.measDefinitionID})}),i[5].selectOptions=u}}},qe=async(e,t)=>{var u,r;const i=L.value=="batchAdd"?(u=d.value)==null?void 0:u.getTableFieldsValue():(r=o.value)==null?void 0:r.getFieldsValue();if(!i)return;let b=!0;if(L.value=="batchAdd"){let v=e.field.match(/\d+/)[0];b=i[`gearRatio[${v}]`]&&i[`gearRatio[${v}]`]%t==0}else b=i.gearRatio&&i.gearRatio%t==0;return b?Promise.resolve(b):Promise.reject(new Error("编码器线数必须是波形线数的整数倍！"))},P=()=>{V.value=!1,C.value={},T.value="",g.value="",L.value="",w.value=[],D.value=[],h.value={}},lt=e=>{if(e.measDefinitionID!=l.value.measDefinitionID){if(H.value=!0,l.value=e,De(e),!R.value||!R.value.length){f.warning("无法点击！");return}fe(),he(),Ie(),pe(),ve(),be(),ye(),H.value=!1}},fe=async()=>{s.table1Data=await a.fetchGetTimeWaveDefList({turbineID:c.value,MeasDefinitionID:l.value.measDefinitionID})},he=async()=>{s.table2Data=await a.fetchGetTimeParamEnvDefList({turbineID:c.value,MeasDefinitionID:l.value.measDefinitionID})},Ie=async()=>{s.table3Data=await a.fetchGetVoltageCurrentWaveDefList({turbineID:c.value,MeasDefinitionID:l.value.measDefinitionID})},pe=async()=>{s.table4Data=await a.fetchGetWorkConListForMeasLocList({turbineID:c.value,MeasDefinitionID:l.value.measDefinitionID})},ve=async()=>{s.table5Data=await a.fetchGetWorkCondSpdMeasdList({turbineID:c.value,MeasDefinitionID:l.value.measDefinitionID})},be=async()=>{s.table6Data=await a.fetchGetModbusWaveList({turbineID:c.value,measDefinitionID:l.value.measDefinitionID})},ye=async()=>{s.table7Data=await a.fetchGetMeasdTriggerList({turbineID:c.value,MeasDefinitionID:l.value.measDefinitionID})},st=async e=>{e.type&&e.type=="close"?(s.batchApplyData=[],s.batchApplyKey="",s[`bathApplyResponse${e.key}`]={}):(s.batchApplyData=e.turbines,s.batchApplyKey=e.key)};return Ee("deviceId",c),Ee("bathApplySubmit",st),(e,t)=>{const i=Pe,b=At,u=Tt,r=kt,p=It;return F(),ne(p,{spinning:H.value,size:"large"},{default:_(()=>[A("h1",null,[t[18]||(t[18]=N(" 测量定义 ",-1)),k(i,{class:"addbtnOfPageTitle",type:"primary",onClick:t[0]||(t[0]=v=>Y({tableKey:"0",operateType:"add",title:"测量定义"}))},{default:_(()=>t[17]||(t[17]=[N("添加",-1)])),_:1,__:[17]})]),A("div",Ut,[A("ul",Wt,[(F(!0),q(ge,null,_e(B(R),v=>(F(),q("li",{key:v.measDefinitionID,class:We({active:l.value.measDefinitionID===v.measDefinitionID}),onClick:m=>lt(v)},Le(v.measDefinitionName),11,St))),128))])]),A("div",Pt,[k(Dt,{tableTitle:"测量定义信息",defaultCollapse:!0,batchApply:!1},ct({content:_(()=>[A("div",$t,[k(u,{column:5,size:"small"},{default:_(()=>[(F(!0),q(ge,null,_e(J.value,v=>(F(),ne(b,{label:v.label,key:v.label},{default:_(()=>[N(Le(v.value),1)]),_:2},1032,["label"]))),128))]),_:1})])]),_:2},[l.value&&l.value.measDefinitionID?{name:"rightButtons",fn:_(()=>[k(i,{type:"primary",onClick:t[1]||(t[1]=v=>Z({tableKey:"0",rowData:ue.value,title:"测量定义"}))},{default:_(()=>t[19]||(t[19]=[N("编辑",-1)])),_:1,__:[19]}),k(i,{onClick:t[2]||(t[2]=v=>ee({tableKey:"0",rowData:ue.value,title:"测量定义"}))},{default:_(()=>t[20]||(t[20]=[N("删除",-1)])),_:1,__:[20]})]),key:"0"}:void 0]),1024)]),l.value&&l.value.measDefinitionID?(F(),q("div",{class:"blockBorder",key:c.value},[A("div",Gt,[k(se,{"table-key":"1","table-title":"时域波形定义列表","table-columns":B(Q)({waveTypeList:B(W)})[1],borderLight:s.batchApplyKey=="1",bathApplyResponse:s.bathApplyResponse1,"table-operate":["edit","delete","add","batchDelete"],"record-key":"waveDefinitionID",onAddRow:Y,"table-datas":s.table1Data,onDeleteRow:ee,onEditRow:Z,noPagination:!0},null,8,["table-columns","borderLight","bathApplyResponse","table-datas"])]),A("div",Bt,[k(se,{"table-key":"2","table-title":"高频包络波形定义列表","table-columns":B(Q)()[2],borderLight:s.batchApplyKey=="2",bathApplyResponse:s.bathApplyResponse2,"table-operate":["edit","delete","add","batchDelete"],"record-key":"waveDefinitionID","table-datas":s.table2Data,onAddRow:Y,onDeleteRow:ee,onEditRow:Z,noPagination:!0},null,8,["table-columns","borderLight","bathApplyResponse","table-datas"])]),A("div",Kt,[k(se,{"table-key":"3","table-title":"电流电压波形定义列表","table-columns":B(Q)({waveTypeList:B(W)})[3],borderLight:s.batchApplyKey=="3",bathApplyResponse:s.bathApplyResponse3,"table-operate":["edit","delete","add","batchDelete"],"record-key":"waveDefinitionID","table-datas":s.table3Data,onAddRow:Y,onDeleteRow:ee,onEditRow:Z,noPagination:!0},null,8,["table-columns","borderLight","bathApplyResponse","table-datas"])]),A("div",zt,[k(se,{"table-key":"4","table-title":"工况波形定义列表","table-columns":B(Q)()[4],borderLight:s.batchApplyKey=="4",bathApplyResponse:s.bathApplyResponse4,"table-operate":["edit","delete","add","batchDelete","batchAdd"],"record-key":"measLocationID","table-datas":s.table4Data,onAddRow:Y,onDeleteRow:ee,onEditRow:Z,noPagination:!0},null,8,["table-columns","borderLight","bathApplyResponse","table-datas"])]),A("div",jt,[k(se,{"table-key":"5","table-title":"转速波形定义列表","table-columns":B(Q)()[5],borderLight:s.batchApplyKey=="5",bathApplyResponse:s.bathApplyResponse5,"table-operate":["edit","delete","add","batchDelete","batchAdd"],"record-key":"measLocationID","table-datas":s.table5Data,onAddRow:Y,onDeleteRow:ee,onEditRow:Z,noPagination:!0},null,8,["table-columns","borderLight","bathApplyResponse","table-datas"])]),A("div",Ht,[k(se,{"table-key":"6","table-title":"Modbus设备波形定义列表","table-columns":B(Q)()[6],borderLight:s.batchApplyKey=="6",bathApplyResponse:s.bathApplyResponse6,"table-operate":["edit","delete","add","batchDelete","batchAdd"],recordKey:v=>`${v.waveDefinitionID}&&${v.measLocationID}`,"table-datas":s.table6Data,onAddRow:Y,onDeleteRow:ee,onEditRow:Z,noPagination:!0},null,8,["table-columns","borderLight","bathApplyResponse","recordKey","table-datas"])]),A("div",Jt,[k(se,{"table-key":"7","table-title":"触发采集配置列表","table-columns":B(Q)()[7],borderLight:s.batchApplyKey=="7",bathApplyResponse:s.bathApplyResponse7,"table-operate":["edit","delete","add","batchDelete"],"record-key":"ruleName","table-datas":s.table7Data,onAddRow:Y,onDeleteRow:ee,onEditRow:Z,noPagination:!0},null,8,["table-columns","borderLight","bathApplyResponse","table-datas"])])])):(F(),q("div",Qt," 请先添加测量定义!")),k(r,{maskClosable:!1,destroyOnClose:!0,width:Ge.value,open:V.value,title:g.value,footer:"",onCancel:P},{default:_(()=>[L.value==="add"||L.value==="edit"?(F(),q("div",Xt,[k(te,{ref_key:"formModalRef",ref:o,model:h.value,"title-col":w.value,initFormData:C.value,actions:["submit","change"],onChange:X,onSubmit:tt},{otherInfo:_(({formModel:v})=>[T.value=="1"||T.value=="3"?(F(),q("div",Yt,[A("div",Zt,[A("div",ea,[k(i,{onClick:t[3]||(t[3]=m=>Fe())},{default:_(()=>t[21]||(t[21]=[N("自动生成",-1)])),_:1,__:[21]})]),L.value==="add"?(F(),q("div",ta,[t[22]||(t[22]=A("p",null,"振动测量位置",-1)),k(te,{modelValue:h.value,"onUpdate:modelValue":[t[4]||(t[4]=m=>h.value=m),t[5]||(t[5]=m=>{var y;return(y=o.value)==null?void 0:y.updateSlotFormData(m,["collectionUnit","measLocIds"])})],onlyFormList:!0,onChange:X,"title-col":[D.value[0],D.value[1]],initFormData:C.value},null,8,["modelValue","title-col","initFormData"])])):j("",!0),A("div",aa,[t[23]||(t[23]=A("p",null,"通带特征值配置",-1)),k(te,{modelValue:h.value,"onUpdate:modelValue":[t[6]||(t[6]=m=>h.value=m),t[7]||(t[7]=m=>{var y;return(y=o.value)==null?void 0:y.updateSlotFormData(m,["generalEVList"])})],onlyFormList:!0,"title-col":[{...D.value[2]}],initFormData:C.value},null,8,["modelValue","title-col","initFormData"])]),A("div",ia,[t[24]||(t[24]=A("p",null,"频带特征值配置",-1)),k(Me,{modelValue:h.value,"onUpdate:modelValue":[t[8]||(t[8]=m=>h.value=m),t[9]||(t[9]=m=>{var y;return(y=o.value)==null?void 0:y.updateSlotFormData(m,["underLimitValue","upperLimitValue"])})],size:"default","table-columns":B(Mt)(),"table-operate":["delete"],"table-datas":C.value.tableDatas,noForm:!0,noCopyUp:!0,"noCopyUp-keys":["underLimitValue","upperLimitValue"],onHangeTableFormChange:X},null,8,["modelValue","table-columns","table-datas"]),k(te,{onlyFormList:!0,titleCol:[{...D.value[3]}],initFormData:C.value},null,8,["titleCol","initFormData"])])])])):T.value=="2"?(F(),q("div",la,[A("div",sa,[k(i,{onClick:t[10]||(t[10]=m=>Fe())},{default:_(()=>t[25]||(t[25]=[N("自动生成",-1)])),_:1,__:[25]})]),L.value=="add"?(F(),q("div",na,[t[26]||(t[26]=A("p",null,"振动测量位置",-1)),k(te,{onlyFormList:!0,onChange:X,"title-col":[D.value[0],D.value[1]],initFormData:C.value,"onUpdate:modelValue":t[11]||(t[11]=m=>{var y;return(y=o.value)==null?void 0:y.updateSlotFormData(m,["measLocIds","collectionUnit"])})},null,8,["title-col","initFormData"])])):j("",!0)])):T.value=="6"?(F(),q("div",oa,[L.value=="add"||L.value=="edit"?(F(),q("div",ua,[k(te,{onlyFormList:!0,onChange:X,"title-col":[D.value[4]],"onUpdate:modelValue":t[12]||(t[12]=m=>{var y;return(y=o.value)==null?void 0:y.updateSlotFormData(m,["evLists"])}),initFormData:C.value},null,8,["title-col","initFormData"])])):j("",!0)])):T.value=="7"?(F(),q("div",ra,[A("div",da,[t[27]||(t[27]=A("p",null,"触发规则",-1)),we.value?(F(),ne(Me,{key:0,modelValue:h.value,"onUpdate:modelValue":[t[13]||(t[13]=m=>h.value=m),t[14]||(t[14]=m=>{var y;return(y=o.value)==null?void 0:y.updateSlotFormData(m,["relationship","type","value"])})],size:"default","table-columns":s.modal7TableColumns,"table-operate":["delete"],"table-datas":C.value.tableDatas,noForm:!0,noCopyUp:!0,"noCopyUp-keys":["type","relationship","value"]},null,8,["modelValue","table-columns","table-datas"])):(F(),ne(te,{key:1,onlyFormList:!0,"title-col":[D.value[6]],"onUpdate:modelValue":t[15]||(t[15]=m=>{var y;return(y=o.value)==null?void 0:y.updateSlotFormData(m,["timeInterval"])}),initFormData:C.value},null,8,["title-col","initFormData"]))]),pt(A("div",ca,[t[28]||(t[28]=A("p",null,"被触发测量定义",-1)),k(te,{onlyFormList:!0,onChange:X,"title-col":[D.value[5]],initFormData:C.value,"onUpdate:modelValue":t[16]||(t[16]=m=>{var y;return(y=o.value)==null?void 0:y.updateSlotFormData(m,["generalEVList"])})},null,8,["title-col","initFormData"])],512),[[vt,!!(l.value&&l.value.mdf_Ex&&l.value.mdf_Ex.modelType==0)]])])):j("",!0)]),_:1},8,["model","title-col","initFormData"])])):L.value==="batchAdd"?(F(),ne(ht,{key:1,ref_key:"tableModalRef",ref:d,size:"default","table-columns":w.value,"table-operate":["copyUp","delete"],"table-datas":[],onHangeTableFormChange:X,"noCopyUp-keys":["parkNumber"],onSubmit:at,onCancel:P},null,8,["table-columns"])):j("",!0)]),_:1},8,["width","open","title"])]),_:1},8,["spinning"])}}},_a=Se(pa,[["__scopeId","data-v-60368cc8"]]);export{_a as default};
