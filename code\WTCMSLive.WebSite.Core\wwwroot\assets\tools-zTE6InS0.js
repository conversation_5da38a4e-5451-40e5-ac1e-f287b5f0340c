import{Z as j,_ as N,a0 as x,a1 as M,ax as A,a4 as _,a7 as P,af as L,w as X,ae as B,b as g,aa as O,aJ as G,a5 as E,ac as R,e7 as I,ep as T}from"./index-BjOW8S1L.js";import{i as H}from"./initDefaultProps-P4j1rGDC.js";function F(e,t,i){var n=i||{},a=n.noTrailing,l=a===void 0?!1:a,c=n.noLeading,d=c===void 0?!1:c,p=n.debounceMode,o=p===void 0?void 0:p,s,r=!1,u=0;function D(){s&&clearTimeout(s)}function z(v){var $=v||{},h=$.upcomingOnly,y=h===void 0?!1:h;D(),r=!y}function S(){for(var v=arguments.length,$=new Array(v),h=0;h<v;h++)$[h]=arguments[h];var y=this,f=Date.now()-u;if(r)return;function b(){u=Date.now(),t.apply(y,$)}function m(){s=void 0}!d&&o&&!s&&b(),D(),o===void 0&&f>e?d?(u=Date.now(),l||(s=setTimeout(o?m:b,e))):b():l!==!0&&(s=setTimeout(o?m:b,o===void 0?e-f:e))}return S.cancel=z,S}function U(e,t,i){var n={},a=n.atBegin,l=a===void 0?!1:a;return F(e,t,{debounceMode:l!==!1})}const V=new A("antSpinMove",{to:{opacity:1}}),Z=new A("antRotate",{to:{transform:"rotate(405deg)"}}),q=e=>({[`${e.componentCls}`]:x(x({},M(e)),{position:"absolute",display:"none",color:e.colorPrimary,textAlign:"center",verticalAlign:"middle",opacity:0,transition:`transform ${e.motionDurationSlow} ${e.motionEaseInOutCirc}`,"&-spinning":{position:"static",display:"inline-block",opacity:1},"&-nested-loading":{position:"relative",[`> div > ${e.componentCls}`]:{position:"absolute",top:0,insetInlineStart:0,zIndex:4,display:"block",width:"100%",height:"100%",maxHeight:e.contentHeight,[`${e.componentCls}-dot`]:{position:"absolute",top:"50%",insetInlineStart:"50%",margin:-e.spinDotSize/2},[`${e.componentCls}-text`]:{position:"absolute",top:"50%",width:"100%",paddingTop:(e.spinDotSize-e.fontSize)/2+2,textShadow:`0 1px 2px ${e.colorBgContainer}`},[`&${e.componentCls}-show-text ${e.componentCls}-dot`]:{marginTop:-(e.spinDotSize/2)-10},"&-sm":{[`${e.componentCls}-dot`]:{margin:-e.spinDotSizeSM/2},[`${e.componentCls}-text`]:{paddingTop:(e.spinDotSizeSM-e.fontSize)/2+2},[`&${e.componentCls}-show-text ${e.componentCls}-dot`]:{marginTop:-(e.spinDotSizeSM/2)-10}},"&-lg":{[`${e.componentCls}-dot`]:{margin:-(e.spinDotSizeLG/2)},[`${e.componentCls}-text`]:{paddingTop:(e.spinDotSizeLG-e.fontSize)/2+2},[`&${e.componentCls}-show-text ${e.componentCls}-dot`]:{marginTop:-(e.spinDotSizeLG/2)-10}}},[`${e.componentCls}-container`]:{position:"relative",transition:`opacity ${e.motionDurationSlow}`,"&::after":{position:"absolute",top:0,insetInlineEnd:0,bottom:0,insetInlineStart:0,zIndex:10,width:"100%",height:"100%",background:e.colorBgContainer,opacity:0,transition:`all ${e.motionDurationSlow}`,content:'""',pointerEvents:"none"}},[`${e.componentCls}-blur`]:{clear:"both",opacity:.5,userSelect:"none",pointerEvents:"none","&::after":{opacity:.4,pointerEvents:"auto"}}},"&-tip":{color:e.spinDotDefault},[`${e.componentCls}-dot`]:{position:"relative",display:"inline-block",fontSize:e.spinDotSize,width:"1em",height:"1em","&-item":{position:"absolute",display:"block",width:(e.spinDotSize-e.marginXXS/2)/2,height:(e.spinDotSize-e.marginXXS/2)/2,backgroundColor:e.colorPrimary,borderRadius:"100%",transform:"scale(0.75)",transformOrigin:"50% 50%",opacity:.3,animationName:V,animationDuration:"1s",animationIterationCount:"infinite",animationTimingFunction:"linear",animationDirection:"alternate","&:nth-child(1)":{top:0,insetInlineStart:0},"&:nth-child(2)":{top:0,insetInlineEnd:0,animationDelay:"0.4s"},"&:nth-child(3)":{insetInlineEnd:0,bottom:0,animationDelay:"0.8s"},"&:nth-child(4)":{bottom:0,insetInlineStart:0,animationDelay:"1.2s"}},"&-spin":{transform:"rotate(45deg)",animationName:Z,animationDuration:"1.2s",animationIterationCount:"infinite",animationTimingFunction:"linear"}},[`&-sm ${e.componentCls}-dot`]:{fontSize:e.spinDotSizeSM,i:{width:(e.spinDotSizeSM-e.marginXXS/2)/2,height:(e.spinDotSizeSM-e.marginXXS/2)/2}},[`&-lg ${e.componentCls}-dot`]:{fontSize:e.spinDotSizeLG,i:{width:(e.spinDotSizeLG-e.marginXXS)/2,height:(e.spinDotSizeLG-e.marginXXS)/2}},[`&${e.componentCls}-show-text ${e.componentCls}-text`]:{display:"block"}})}),J=j("Spin",e=>{const t=N(e,{spinDotDefault:e.colorTextDescription,spinDotSize:e.controlHeightLG/2,spinDotSizeSM:e.controlHeightLG*.35,spinDotSizeLG:e.controlHeight});return[q(t)]},{contentHeight:400});var K=function(e,t){var i={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(i[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,n=Object.getOwnPropertySymbols(e);a<n.length;a++)t.indexOf(n[a])<0&&Object.prototype.propertyIsEnumerable.call(e,n[a])&&(i[n[a]]=e[n[a]]);return i};const Q=()=>({prefixCls:String,spinning:{type:Boolean,default:void 0},size:String,wrapperClassName:String,tip:E.any,delay:Number,indicator:E.any});let C=null;function W(e,t){return!!e&&!!t&&!isNaN(Number(t))}function Y(e){const t=e.indicator;C=typeof t=="function"?t:()=>g(t,null,null)}const w=_({compatConfig:{MODE:3},name:"ASpin",inheritAttrs:!1,props:H(Q(),{size:"default",spinning:!0,wrapperClassName:""}),setup(e,t){let{attrs:i,slots:n}=t;const{prefixCls:a,size:l,direction:c}=P("spin",e),[d,p]=J(a),o=L(e.spinning&&!W(e.spinning,e.delay));let s;return X([()=>e.spinning,()=>e.delay],()=>{s==null||s.cancel(),s=U(e.delay,()=>{o.value=e.spinning}),s==null||s()},{immediate:!0,flush:"post"}),B(()=>{s==null||s.cancel()}),()=>{var r,u;const{class:D}=i,z=K(i,["class"]),{tip:S=(r=n.tip)===null||r===void 0?void 0:r.call(n)}=e,v=(u=n.default)===null||u===void 0?void 0:u.call(n),$={[p.value]:!0,[a.value]:!0,[`${a.value}-sm`]:l.value==="small",[`${a.value}-lg`]:l.value==="large",[`${a.value}-spinning`]:o.value,[`${a.value}-show-text`]:!!S,[`${a.value}-rtl`]:c.value==="rtl",[D]:!!D};function h(f){const b=`${f}-dot`;let m=R(n,e,"indicator");return m===null?null:(Array.isArray(m)&&(m=m.length===1?m[0]:m),I(m)?T(m,{class:b}):C&&I(C())?T(C(),{class:b}):g("span",{class:`${b} ${f}-dot-spin`},[g("i",{class:`${f}-dot-item`},null),g("i",{class:`${f}-dot-item`},null),g("i",{class:`${f}-dot-item`},null),g("i",{class:`${f}-dot-item`},null)]))}const y=g("div",O(O({},z),{},{class:$,"aria-live":"polite","aria-busy":o.value}),[h(a.value),S?g("div",{class:`${a.value}-text`},[S]):null]);if(v&&G(v).length){const f={[`${a.value}-container`]:!0,[`${a.value}-blur`]:o.value};return d(g("div",{class:[`${a.value}-nested-loading`,e.wrapperClassName,p.value]},[o.value&&g("div",{key:"loading"},[y]),g("div",{class:f,key:"container"},[v])]))}return d(y)}}});w.setDefaultIndicator=Y;w.install=function(e){return e.component(w.name,w),e};const ne=e=>{const t=[];for(const i in e){if(!e.hasOwnProperty(i))continue;const n=i.match(/^([^\[]+)\[(\d+)\]$/);if(!n)continue;const a=n[1],l=parseInt(n[2]);t[l]||(t[l]={}),t[l][a]=e[i]}return t.filter(i=>i)},te=(e,t={label:"value",value:"key"},i={nother:!1})=>{if(!e||!e.length)return[];const n=[];return t.loopSelf?e.forEach(a=>{n.push({label:a,value:a})}):e!=null&&e.length!==0&&e.forEach(a=>{let l=i.nother?{}:a;Object.keys(t).forEach(c=>{l[c]=a[t[c]]}),n.push(l)}),n},ie=(e,t)=>{let i=[];return Object.keys(e).forEach(n=>{t=="key"?i.push({value:n,label:n}):t=="value"?i.push({value:e[n],label:e[n]}):t?i.push({value:e[n],label:n}):i.push({value:n,label:e[n]})}),i},ae=(e,t={},i)=>{const n=[],a=new Set;return Object.keys(e).forEach(l=>{const c=l.match(/\[(\d+)\]/);c&&a.add(parseInt(c[1]))}),Array.from(a).sort().forEach(l=>{const c={...t};Object.keys(e).forEach(d=>{if(d.includes(`[${l}]`)){const p=d.replace(`[${l}]`,""),o=e[d];o&&typeof o=="object"&&o.value!==void 0?i&&i[p]?Object.keys(i[p]).forEach(s=>{if(Array.isArray(i[p][s])){let r=i[p][s];c[r[0]]=o[s][r[1]]}else c[i[p][s]]=o[s]}):c[p]=o.value:c[p]=o}}),n.push(c)}),n},oe=(e={type:"",title:"",required:!1})=>{const{type:t,title:i,required:n}=e;let a=[];switch(n&&(a.push({required:!0,message:i+"是必填项！"}),a.push({pattern:/\S/,message:i+"不能为空字符串！"})),t){case"ip":a.push({pattern:/^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,message:"请输入正确的IP地址！"});break;case"email":a.push({pattern:/^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/,message:"请输入正确的邮箱地址！"});break;case"phone":a.push({pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码！"});break;case"number":a.push({pattern:/^[0-9]+(\.[0-9]+)?$/,message:`${i}为数字！`});break;case"postCode":a.push({pattern:/^\d{6}$/,message:"邮编格式不正确!"});break;case"integer":a.push({pattern:/^[0-9]+$/,message:"请输入正整数！"});break;case"port":a.push({pattern:/^([1-9]|[1-9]\d{1,3}|[1-5]\d{4}|6[0-4]\d{4}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])$/,message:"请输入正确的端口号！"});break}return a};function se(e,t,i){let n=[];const a=e.map(r=>(n.push(r.dataIndex),r.title)),l=r=>(n.map(u=>{typeof r[u]=="string"&&(r[u].includes(",")||r[u].includes('"')||r[u].includes(`
`))&&(r[u]=`"${r[u].replace(/"/g,'""')}"`)}),n.map(u=>r[u]).join(","));let d="\uFEFF"+a.join(",")+`
`+t.map(l).join(`
`);const p=new Blob([d],{type:"text/csv;charset=utf-8;"}),o=document.createElement("a"),s=URL.createObjectURL(p);o.setAttribute("href",s),o.setAttribute("download",i),o.style.visibility="hidden",document.body.appendChild(o),o.click(),document.body.removeChild(o)}const le=e=>{let t="",i="";switch(e){case 3:t="#339900",i="正常";break;case 5:t="#ff9900",i="注意";break;case 6:t="#cc0000",i="危险";break;default:t="#999",i="未知";break}return{color:t,text:i}},re=e=>{let t="",i="",n="";switch(e){case 3:t="#339900",i="正常",n="dauNormal";break;case 4:t="#ff2d55",i="通讯异常",n="dauAlarm";break;case 5:t="#ff2d55",i="无数据到达",n="dauAlarm";break;case 7:t="#ff2d55",i="传感器故障",n="dauAlarm";break;case 8:t="#ff2d55",i="转速异常",n="dauAlarm";break;default:t="#999",i="未知",n="dauUnknown";break}return{color:t,text:i,className:n}},ce=e=>new Promise(t=>{const i=new Image;i.onload=()=>t(!0),i.onerror=()=>t(!1),i.src=e,setTimeout(()=>t(!1),2e3)});export{w as S,te as a,re as b,ce as c,oe as d,ie as e,ae as f,le as g,se as h,ne as t};
