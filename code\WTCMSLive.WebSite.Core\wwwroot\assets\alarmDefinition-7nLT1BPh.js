import{u as ee,W as te}from"./table-RP3jLHlo.js";import{O as ae}from"./index-CzSbT6op.js";import{W as le}from"./index-QXLii0rw.js";import{C as re,cv as se,cw as ne,cx as oe,cy as ie,cz as ue,cA as ce,cB as de,r as p,u as pe,y as me,w as he,j as fe,f as x,d as w,o as V,c as S,b as C,g as G,m,aR as O}from"./index-BjOW8S1L.js";import{a as N,S as be,d as $,f as ve}from"./tools-zTE6InS0.js";import{u as ye}from"./devTree-Dwa9wLl9.js";import{B as ge}from"./index-7iPMz_Qy.js";import{M as De}from"./index-BnSFuLp6.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./ActionButton-C_grUmdF.js";import"./styleChecker-CFtINSLw.js";import"./initDefaultProps-P4j1rGDC.js";import"./shallowequal-gCpTBdTi.js";import"./index-DTxROkTj.js";import"./index-kP-mINcM.js";import"./index-BJEkaghg.js";import"./index-D3J_dKbJ.js";import"./index-CpBSPak5.js";const we=re("alarmDefinition",{state:()=>({alarmDefineList:[],measLocations:[],eigenValueTypeList:[]}),actions:{reset(){this.$reset()},async fetchGetAlarmDefineList(u){try{const t=await de(u);return t&&t.length>0&&(this.alarmDefineList=t),t}catch(t){throw console.error("获取失败:",t),t}},async fetchMeasLocations(u){try{const t=await ce(u);let h=N(t,{label:"measLocName",value:"measLocationID"});return this.deviceOptions=h,h}catch(t){throw console.error("获取失败:",t),t}},async fetchGetEigenValueTypeList(u){try{const t=await ue(u);let h=N(t,{label:"eigenValueName",value:"eigenValueID"},{nother:!0});return this.eigenValueTypeList=h,h}catch(t){throw console.error("获取失败:",t),t}},async fetchWarnBatchAddWarnRulee(u){try{return await ie(u)}catch(t){throw console.error(t),t}},async fetchEditWarnRule(u){try{return await oe(u)}catch(t){throw console.error(t),t}},async fetchBatchDeleteWarnRule(u){try{return await ne(u)}catch(t){throw console.error(t),t}},async fetchInitTurbineWarn(u){try{return await se(u)}catch(t){throw console.error(t),t}}}}),Ve={key:2},$e={__name:"alarmDefinition",setup(u){const t=we(),h=ye(),M=ee(),E=async(e,r)=>{let l=null,a="warnValue",s="alarmValue";if(c.value==="edit")l=b.value.getFieldsValue();else{l=v.value.getTableFieldsValue();let D=e.field.match(/\d+/)[0];a=`warnValue[${D}]`,s=`alarmValue[${D}]`}if(!l)return;const n=parseFloat(l[a]),i=parseFloat(l[s]);if(n!==void 0&&i!==void 0&&n>i)return Promise.reject(new Error("正向危险值不能小于正向注意值"));let g=e.field==a?s:a;return c.value==="edit"?b.value.clearValidate(g):v.value.clearValidate(g),Promise.resolve()},F=async(e,r)=>{let l=null,a="reverseWarnValue",s="reverseAlarmValue";if(c.value==="edit")l=b.value.getFieldsValue();else{l=v.value.getTableFieldsValue();let D=e.field.match(/\d+/)[0];a=`reverseWarnValue[${D}]`,s=`reverseAlarmValue[${D}]`}if(!l)return;const n=parseFloat(l[a]),i=parseFloat(l[s]);if(n!==void 0&&i!==void 0&&n<i)return Promise.reject(new Error("反向危险值不能大于反向注意值"));let g=e.field==a?s:a;return c.value==="edit"?b.value.clearValidate(g):v.value.clearValidate(g),Promise.resolve()},I=(e={isform:!1,idEdit:!1})=>{let r=320;return[{title:"测量位置",dataIndex:"measLocationID",columnWidth:150,formItemWidth:r,labelInValue:!e.idEdit,isrequired:!e.idEdit,inputType:"select",selectOptions:[],hasChangeEvent:!0,isdisplay:!e.idEdit,...e.isform?{}:{customRender:({text:l,record:a})=>a.measLocationName}},{title:"特征值",dataIndex:"eigenValueID",columnWidth:160,formItemWidth:r,tableList:[],isdisplay:!e.idEdit,inputType:"select",selectOptions:[],isrequired:!e.idEdit,...e.isform?{}:{customRender:({text:l,record:a})=>a.eigenValueName}},{title:"工况参数",dataIndex:"workConditionParams",columnWidth:120,formItemWidth:r,isdisplay:!e.idEdit,afterContent:!0,headerOperations:{filters:[],filterDataIndex:["workConParameterName"]},inputType:"select",selectOptions:[{label:"功率(KW)",value:"功率"},{label:"转速(RPM)",value:"转速"},{label:"温度(°C)",value:"温度"}],...e.isform?{}:{customRender:({text:l,record:a})=>a.workConParameterName}},{title:"工况下限",dataIndex:"lowerLimitValue",columnWidth:80,formItemWidth:r,isdisplay:!e.idEdit,validateRules:$({title:"工况下限",type:"number"})},{title:"工况上限",dataIndex:"upperLimitValue",columnWidth:80,formItemWidth:r,isdisplay:!e.idEdit,validateRules:$({title:"工况下限",type:"number"})},{title:"正向注意",dataIndex:"warnValue",columnWidth:80,formItemWidth:r,validateRules:[{pattern:/^[0-9]+(\.[0-9]+)?$/,message:"请输入数字"},{validator:E,trigger:"change"}]},{title:"正向危险",dataIndex:"alarmValue",columnWidth:80,formItemWidth:r,validateRules:[{pattern:/^[0-9]+(\.[0-9]+)?$/,message:"请输入数字"},{validator:E,trigger:"change"}]},{title:"反向注意",dataIndex:"reverseWarnValue",columnWidth:80,formItemWidth:r,validateRules:[{pattern:/^[0-9]+(\.[0-9]+)?$/,message:"请输入数字"},{validator:F,trigger:"change"}]},{title:"反向危险",dataIndex:"reverseAlarmValue",columnWidth:80,formItemWidth:r,validateRules:[{pattern:/^[0-9]+(\.[0-9]+)?$/,message:"请输入数字"},{validator:F,trigger:"change"}]}]},W=p(!1),T=p(""),c=p(""),b=p(),L=p({}),f=p([]),k=p(!1),_=pe(),d=p(_.params.id),R=p(""),o=me({tableColumns:I(),tableData:[],batchApplyData:[],batchApplyKey:"",bathApplyResponse1:{}}),v=p({}),y=async e=>{W.value=!0,o.tableData=await t.fetchGetAlarmDefineList({turbineID:d.value}),o.tableColumns=I(),W.value=!1},q=async e=>{R.value&&await M.fetchDevTreedDevicelist({windParkID:R.value,useTobath:!0})},K=()=>{let e=h.findAncestorsWithNodes(d.value);e&&e.length&&e.length>1&&(R.value=e[e.length-2].id)};he(()=>_.params.id,async e=>{e&&(t.reset(),d.value=e,K(),await q(),y())},{immediate:!0});const j=fe(()=>c.value==="add"||c.value==="batchAdd"?"1200px":"600px"),P=()=>{k.value=!0},A=e=>{k.value=!1,f.value=[],L.value={},c.value="",T.value=""},z=e=>{const{tableKey:r,title:l,operateType:a}=e;c.value=a,T.value="批量添加报警定义",f.value=[...I({isform:!0})],J(),P()},U=async e=>{const{selectedkeys:r,record:l}=e;let a=[];if(!r||r.length===0)return;let s=t.alarmDefineList.filter(i=>r.includes(i.thresholdGroup));if(!s||s.length===0)return;for(let i=0;i<s.length;i++)a.push({windTurbineID:d.value,workConParameter:s[i].workConParameter,thresholdGroup:s[i].thresholdGroup,eigenValueID:s[i].eigenValueID,measLocationID:s[i].measLocationID});const n=await t.fetchBatchDeleteWarnRule({sourceData:a,targetTurbineIds:o.batchApplyData});n&&n.code===1?(y(),o.bathApplyResponse1=n.batchResults||{},m.success("删除成功")):m.error("删除失败:"+n.msg)},H=e=>{const{rowData:r,tableKey:l,title:a,operateType:s}=e;c.value=s,T.value="编辑报警定义",f.value=[...I({isform:!0,idEdit:!0})],L.value={...r},P()},B=async(e,r,l)=>{let a=f.value,s=e.value.value;if(e&&e.dataIndex&&e.dataIndex=="measLocationID"&&c.value=="batchAdd"){let n=await t.fetchGetEigenValueTypeList({turbineID:d.value,measLocId:s});if(c.value=="batchAdd"){if(e.index>=a[1].tableList.length)for(let i=a[1].tableList.length;i<=e.index;i++)a[1].tableList.push({});a[1].tableList[e.index].selectOptions=n,v.value.setTableFieldValue({formDataIndex:`eigenValueID[${e.index}]`,tableDataIndex:"eigenValueID",index:e.index,value:n&&n.length?n[0].value:""})}else a[1].selectOptions=n,b.value.setFieldValue("eigenValueID",n&&n.length?n[0].value:"")}},J=async e=>{let r=f.value,l=await t.fetchMeasLocations({turbineID:d.value});r[0].selectOptions=l;let a="";if(a=l&&l.length?l[0].value:"",l&&l.length){let s=await t.fetchGetEigenValueTypeList({turbineID:d.value,measLocId:a});r[1].selectOptions=s}},Q=async e=>{let r={...e,thresholdGroup:L.value.thresholdGroup,WindTurbineID:d.value,applyToAll:!1},l=await t.fetchEditWarnRule({sourceData:r,targetTurbineIds:o.batchApplyData});l&&l.code===1?(y(),o.bathApplyResponse1=l.batchResults||{},m.success("提交成功"),A()):m.error("提交失败:"+l.msg)},X=async e=>{let r={measLocationID:{option:["measLocType","measLocType"],value:"measLocationID"}},l=ve(e,{applyToAll:!1,windParkID:R.value,windTurbineID:d.value},r);for(let a=0;a<l.length;a++)l[a].workConditionParams=l[a].workConditionParams||"";if(l&&l.length){let a=await t.fetchWarnBatchAddWarnRulee({sourceData:l,targetTurbineIds:o.batchApplyData});a&&a.code===1?(y(),o.bathApplyResponse1=a.batchResults||{},m.success("提交成功"),A()):m.error("提交失败:"+a.msg)}},Y=async()=>{const e=await t.fetchInitTurbineWarn({turbineID:d.value});e&&e.code===1?(y(),m.success("初始化成功")):m.error("初始化失败:"+e.msg)},Z=async e=>{e.type&&e.type=="close"?(o.batchApplyData=[],o.batchApplyKey="",o[`bathApplyResponse${e.key}`]={}):(o.batchApplyData=e.turbines,o.batchApplyKey=e.key)};return O("deviceId",d),O("bathApplySubmit",Z),(e,r)=>{const l=ge,a=De,s=be;return V(),x(s,{spinning:W.value,size:"large"},{default:w(()=>[(V(),S("div",{key:d.value},[C(te,{ref:"table",size:"default","table-key":"1","table-title":"报警设置列表",borderLight:o.batchApplyKey=="1",bathApplyResponse:o.bathApplyResponse1,"table-columns":o.tableColumns,"table-operate":["delete","add","edit","batchDelete","batchAdd"],"record-key":"thresholdGroup","table-datas":o.tableData,onAddRow:z,onDeleteRow:U,onEditRow:H},{rightButtons:w(({selectedRowKeys:n})=>[C(l,{type:"primary",onClick:r[0]||(r[0]=i=>Y()),disabled:!!o.tableData.length},{default:w(()=>r[1]||(r[1]=[G(" 初始化报警定义 ",-1)])),_:1,__:[1]},8,["disabled"])]),default:w(()=>[r[2]||(r[2]=G(" > ",-1))]),_:1,__:[2]},8,["borderLight","bathApplyResponse","table-columns","table-datas"]),C(a,{maskClosable:!1,width:j.value,open:k.value,title:T.value,footer:"",onCancel:A},{default:w(()=>[c.value==="add"||c.value==="edit"?(V(),x(ae,{key:0,titleCol:f.value,ref_key:"formRef",ref:b,initFormData:L.value,onChange:B,onSubmit:Q},null,8,["titleCol","initFormData"])):c.value==="batchAdd"?(V(),x(le,{key:1,ref_key:"tableFormRef",ref:v,size:"default","table-key":"0","table-columns":f.value,"table-operate":["copyUp","delete"],"table-datas":[],"noCopyUp-keys":["measLocationID","eigenValueID"],onSubmit:X,onHangeTableFormChange:B,onCancel:A},null,8,["table-columns"])):(V(),S("div",Ve))]),_:1},8,["width","open","title"])]))]),_:1},8,["spinning"])}}};export{$e as default};
