import{u as ve,c as ye,W as we}from"./table-RP3jLHlo.js";import{j as Y,y as De,r,u as be,U as he,h as ge,w as ke,f as y,d as i,A as Ie,z as M,o as f,i as U,b as L,c as _e,F as Oe,e as Te,g as T,t as Pe,s as x,m as d}from"./index-BjOW8S1L.js";import{O as Le}from"./index-CzSbT6op.js";import{W as xe}from"./index-QXLii0rw.js";import{S as Ce,d as C,t as Fe,a as We}from"./tools-zTE6InS0.js";import{u as Ne,g as Re}from"./configRoot-3RAhel6W.js";import{u as Se}from"./model-DhdowwoF.js";import{u as Me}from"./devTree-Dwa9wLl9.js";import{_ as Ue}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{D as Ae,a as Ve}from"./index-D82yULGq.js";import{B as qe}from"./index-7iPMz_Qy.js";import{M as Ee}from"./index-BnSFuLp6.js";import{_ as $e}from"./index-9RFCYCf2.js";import"./ActionButton-C_grUmdF.js";import"./styleChecker-CFtINSLw.js";import"./initDefaultProps-P4j1rGDC.js";import"./shallowequal-gCpTBdTi.js";import"./index-DTxROkTj.js";import"./index-kP-mINcM.js";import"./index-BJEkaghg.js";import"./index-D3J_dKbJ.js";import"./index-CpBSPak5.js";import"./useRefs-CX1uwt0r.js";const je={class:"border"},Be={style:{float:"left"}},g=320,F="YYYY-MM-DD",ze={__name:"device",setup(Ge){const k=Se(),o=ve(),D=Ne(),l=Me(),W=e=>[{title:"设备编号",dataIndex:"windTurbineCode",columnWidth:"130",formItemWidth:g,hidden:e&&e.edit,isrequired:!0,headerOperations:{sorter:!0},columnOperate:{type:"number"}},{title:"设备名称",dataIndex:"windTurbineName",columnWidth:"130",formItemWidth:g,isrequired:!0,columnOperate:{type:"number"}},{title:"设备型号",dataIndex:"windTurbineModel",columnWidth:"140",formItemWidth:g,inputType:"select",isrequired:!0,selectOptions:[],disabled:e&&e.edit&&!e.canEditModel,headerOperations:{filters:[]}},{title:"部件",dataIndex:"componentIds",columnWidth:"200",formItemWidth:g,inputType:"select",mode:"tags",isrequired:!0,selectOptions:[],slotName:"part",hidden:e&&e.edit,...e&&e.isForm?{}:{customRender:({record:t})=>t.componentName?Ie("span",{style:{textAlign:"left",display:"block",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap",maxWidth:"500px"},title:t.componentName.join(",")},t.componentName.join(",")):""}},{title:"投递日期",dataIndex:"operationalDate",columnWidth:"120",formItemWidth:g,inputType:"datepicker",timeFormat:F,headerOperations:{sorter:!0,date:!0}},{title:"主控IP",dataIndex:"mcsIP",columnWidth:"180",formItemWidth:g,hidden:e&&e.edit,columnOperate:{type:"ip"},validateRules:C({type:"ip",title:"主控IP",required:!0})},{title:"设备坐标",dataIndex:"location",formItemWidth:g,validateRules:[{pattern:/^(\-?\d+(\.\d+)?),\s*(\-?\d+(\.\d+)?)$/,message:"请输入两个数字并用英文逗号隔开!"}]}],H=Y(()=>m.noOperatesOfUser&&m.noOperatesOfUser.length&&m.noOperatesOfUser.includes("add")?["edit"]:["edit","delete","add","batchDelete","batchAdd"]),J={strokeColor:{"0%":"#108ee9","100%":"#87d068"},strokeWidth:3,format:e=>`${parseFloat(e.toFixed(2))}%`,class:"test"},E=window.localStorage.getItem("templateManagement")==="true",I=r(null),N=r([]),_=r(""),c=r(""),b=r({}),$=r([]),u=r([]),R=r(!1),j=be(),p=r(j.params.id),K=r({}),P=r(!1),m=De({partValue:"",partList:[],noOperatesOfUser:he()});let A=[...Re({isEdit:!0},async(e,t)=>{let a=I.value.getFieldsValue();if(!a)return;const n=parseFloat(a.location1);return parseFloat(a.location2)&&!n?Promise.reject(new Error("请输入经度")):(I.value.clearValidate("location2"),Promise.resolve())},async(e,t)=>{let a=I.value.getFieldsValue();if(!a)return;const n=parseFloat(a.location1);return!parseFloat(a.location2)&&n?Promise.reject(new Error("请输入纬度")):(I.value.clearValidate("location1"),Promise.resolve())})];const B=(e={})=>[{label:"名称",value:e.windParkName},{label:"编号",value:e.windParkCode},{label:"投运日期",value:e.operationalDate?M(e.operationalDate).format(F):""},{label:"联系人",value:e.contactMan},{label:"联系人电话",value:e.contactTel},{label:"区域",value:`${e.country} - ${e.area}`},{label:"地址",value:e.address},{label:"经纬度",value:e.location},{label:"邮编",value:e.postCode},{label:"厂站概况",value:e.description}],z=r(W()),Q=Y(()=>c.value==="batchAdd"?"1200px":"600px"),G=r([B({})]),O=async e=>{p.value&&(P.value=!0,$.value=await o.fetchDevTreedDevicelist({windParkID:p.value}),P.value=!1,z.value=W())},V=async()=>{if(p.value){const e=await o.fetchParkInfo({windParkID:p.value});K.value=e,G.value=B(e)}};ge(()=>{V(),O()}),ke(()=>j.params.id,e=>{o.reset(),p.value=e,V(),O()});const X=async()=>{c.value="editPark",_.value="编辑厂站信息";const e=o.parkInfo;let t=e.operationalDate?M(e.operationalDate,F):"",a=e.location?e.location.split(","):[];b.value={...e,location1:a[0],location2:a[1],operationalDate:t},D.groupCompanyList&&D.groupCompanyList.length>0?A[1].selectOptions=D.groupCompanyList:await Z(),u.value=[...A],S()},Z=async()=>{const e=await D.fetchGroupCompanyList();e&&e.length>0&&(A[1].selectOptions=e)},q=async()=>{k.modelOptions&&k.modelOptions.length>0||await k.fetchModellist()},ee=async()=>{(!o.comPonentList||o.comPonentList.length<1)&&await o.fetchGetAllComPonentList()},S=()=>{R.value=!0},h=e=>{R.value=!1,m.partList=[],m.partValue="",b.value={},u.value=[],_.value="",c.value=""},te=async e=>{const{operateType:t}=e;c.value=t,_.value="批量增加设备",await q(),await ee();let a=W({isForm:!0});a[2].selectOptions=k.modelOptions,a[3].selectOptions=o.comPonentList,u.value=[...a],S()},ae=async(e={})=>{const{selectedkeys:t}=e,a=await o.fetchDeletetDevice(t);a&&a.code===1?(O(),h(),d.success("提交成功"),l.getDevTreeDatas()):d.error("提交失败:"+a.msg)},oe=async e=>{const{rowData:t,operateType:a}=e;c.value=a;let n=t.operationalDate?M(t.operationalDate,F):"";b.value={...t,componentIds:t.componentIds&&t.componentIds.length?t.componentIds.split(","):[],operationalDate:n},_.value="编辑设备",await q();let s=W({edit:!0,isForm:!0,canEditModel:m.noOperatesOfUser&&m.noOperatesOfUser.length});s[2].selectOptions=k.modelOptions,s[3].inputType="checkboxGroup",u.value=[...s],S()},le=async()=>{c.value="copyDevice",_.value="复制设备",await l.getTemplateParkList(),await D.fetchParkList(),await q();let e=[],t=[];l.templateDeviceList&&l.templateDeviceList.length>0&&(e.push({label:"模版风场",value:l.templateDeviceList[0].id}),l.templateDevicoptions&&l.templateDevicoptions.length>0&&(t=l.templateDevicoptions)),e=[...e,...D.parkOptions];let a=pe({parklist:e,turbineList:t,modelList:k.modelOptions});u.value=a,S()},ie=async e=>(N.value=[e],!1),ne=async e=>{if(!e.file||e.fileList.length<1){d.error("请选择文件");return}P.value=!0;const t=new FormData;t.append("file",e.file);let a=await o.fetchTemplateUpload(t);a.code===1?(N.value=[],d.success("上传成功")):d.error("上传失败！"+a.msg),P.value=!1},se=()=>{o.fetchTemplateDownload({parkId:p.value})},re=async()=>{o.fetchExportDauConfig({parkId:p.value})},de=async(e,t,a)=>{if(e.dataIndex==="windParkID"){if(e.value==l.templateDeviceList[0].windParkID)u.value[0].rows[1].cols[0].selectOptions=l.templateDevicoptions||[];else{let n=await o.fetchDevTreedDevicelist({windParkID:e.value});if(n&&n.length>0){let s=We(n,{label:"windTurbineName",value:"windTurbineID"},{nother:!0});u.value[0].rows[1].cols[0].selectOptions=s}else u.value[0].rows[1].cols[0].selectOptions=[]}I.value.setFieldValue("_windTurbineIDOld",null)}},ce=async e=>{if(c.value=="editPark"){const t=await D.fetchEditWindparkInformation({...e,location:`${e.location1},${e.location2}`,windParkID:p.value,windParkGroupName:e.windParkName});t&&t.code===1?(V(),d.success("提交成功"),e.windParkName!==o.parkInfo.windParkName&&l.getDevTreeDatas(),h()):d.error("提交失败:"+t.msg)}else if(c.value=="copyDevice"){const t=await o.fetchCopyTurbine({...e,_curParkId:p.value,_prefix:e._prefix||"",_suffix:e._suffix||"",startNum:e.startNum||""});t&&t.code===1?(d.success("复制成功"),O(),h(),l.getDevTreeDatas()):d.error("提交失败:"+t.msg)}else{let t=[{...e,componentIds:e.componentIds&&e.componentIds.length?e.componentIds.join(","):"",windTurbineID:b.value.windTurbineID,windParkID:b.value.windParkId}];const a=await o.fetchEditDevices(t);a&&a.code===1?(O(),d.success("提交成功"),e.windTurbineName!==b.value.windTurbineName&&l.getDevTreeDatas(),h()):d.error("提交失败:"+a.msg)}},ue=async e=>{if(c.value=="batchAdd"){let t={WindTurbineID:"",WindParkId:p.value},n=Fe(e).map(w=>({...w,...t,componentIds:w.componentIds&&w.componentIds.length?w.componentIds.join(","):"",operationalDate:w.operationalDate?M(w.operationalDate).format(F):""}));const s=await o.fetchAddDevice(n);s&&s.code===1?(O(),h(),d.success("提交成功"),l.getDevTreeDatas()):d.error("提交失败:"+s.msg)}},pe=(e={parklist:[],turbineList:[],modelList:[]})=>[{title:"设备模版",key:"设备模版",isrequired:!0,rows:[{cols:[{inputType:"select",selectOptions:e.parklist,formItemWidth:400,dataIndex:"windParkID",hasChangeEvent:!0,validateRules:C({title:"风场",required:!0})}]},{cols:[{dataIndex:"_windTurbineIDOld",inputType:"select",selectOptions:e.turbineList,formItemWidth:400,validateRules:C({title:"设备",required:!0})}]}]},{title:"复制数量",key:"复制数量",isrequired:!0,rows:[{cols:[{dataIndex:"_num",type:"input",formItemWidth:400,validateRules:C({type:"integer",title:"复制数量",required:!0})}]}]},{title:"复制后机组名称",key:"机组名称模板",rows:[{cols:[{title:"前缀",dataIndex:"_prefix",type:"input",formItemWidth:160},{title:"后缀",dataIndex:"_suffix",type:"input",formItemWidth:160}]},{cols:[{dataIndex:"startNum",title:"起始编号",type:"input",formItemWidth:140}]}]},{title:"机组型号",key:"机组型号",isrequired:!0,rows:[{cols:[{dataIndex:"turbineModel",inputType:"select",formItemWidth:400,selectOptions:e.modelList,validateRules:C({title:"机组型号",required:!0})}]}]}];return(e,t)=>{const a=qe,n=Ve,s=Ae,w=$e,me=Ee,fe=Ce;return f(),y(fe,{spinning:P.value,size:"large"},{default:i(()=>[U("div",null,[L(ye,{tableTitle:"厂站信息",defaultCollapse:!0,batchApply:!1},{rightButtons:i(()=>[m.noOperatesOfUser.includes("editPark")?x("",!0):(f(),y(a,{key:0,type:"primary",onClick:t[0]||(t[0]=v=>X())},{default:i(()=>t[5]||(t[5]=[T(" 编辑 ",-1)])),_:1,__:[5]}))]),content:i(()=>[U("div",je,[L(s,{column:5,size:"small"},{default:i(()=>[(f(!0),_e(Oe,null,Te(G.value,v=>(f(),y(n,{label:v.label,key:v.label},{default:i(()=>[T(Pe(v.value),1)]),_:2},1032,["label"]))),128))]),_:1})])]),_:1}),U("div",null,[L(we,{ref:"table",size:"default","table-key":"1","table-title":"设备列表","table-columns":z.value,"table-operate":H.value,"record-key":"windTurbineID","table-datas":$.value,onAddRow:te,onDeleteRow:ae,onEditRow:oe,noBatchApply:!0},{rightButtons:i(()=>[U("span",Be,[E?(f(),y(w,{key:0,"file-list":N.value,"onUpdate:fileList":t[1]||(t[1]=v=>N.value=v),name:"file",progress:J,onChange:ne,"before-upload":ie,"show-upload-list":!1},{default:i(()=>[L(a,{type:"primary",style:{float:"none"}},{default:i(()=>t[6]||(t[6]=[T(" 导入 ",-1)])),_:1,__:[6]})]),_:1},8,["file-list"])):x("",!0)]),E?(f(),y(a,{key:0,type:"primary",onClick:t[2]||(t[2]=v=>se())},{default:i(()=>t[7]||(t[7]=[T(" 导出 ",-1)])),_:1,__:[7]})):x("",!0),m.noOperatesOfUser.includes("copyDevice")?x("",!0):(f(),y(a,{key:1,type:"primary",onClick:t[3]||(t[3]=v=>le())},{default:i(()=>t[8]||(t[8]=[T(" 复制设备 ",-1)])),_:1,__:[8]})),m.noOperatesOfUser.includes("exportConfigers")?x("",!0):(f(),y(a,{key:2,type:"primary",onClick:t[4]||(t[4]=v=>re())},{default:i(()=>t[9]||(t[9]=[T(" 导出配置 ",-1)])),_:1,__:[9]}))]),_:1},8,["table-columns","table-operate","table-datas"])]),L(me,{width:Q.value,open:R.value,title:_.value,footer:"",maskClosable:!1,onCancel:h},{default:i(()=>[c.value==="batchAdd"?(f(),y(xe,{key:0,ref:"table",size:"default","table-key":"0","table-columns":u.value,"table-operate":["copyUp","delete"],"table-datas":[],onSubmit:ue,onCancel:h},{footer:i(()=>t[10]||(t[10]=[])),_:1},8,["table-columns"])):(f(),y(Le,{key:R.value,titleCol:u.value,initFormData:b.value,ref_key:"operateFormRef",ref:I,onChange:de,onSubmit:ce,formlayout:c.value=="copyDevice"?"table":"horizontal"},null,8,["titleCol","initFormData","formlayout"]))]),_:1},8,["width","open","title"])])]),_:1},8,["spinning"])}}},Dt=Ue(ze,[["__scopeId","data-v-7b590993"]]);export{Dt as default};
