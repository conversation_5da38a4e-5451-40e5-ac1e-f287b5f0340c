import{C as r,D as s,E as o,G as n,H as a,I as i,J as u,K as c}from"./index-BjOW8S1L.js";import{a as h}from"./tools-zTE6InS0.js";const S=r("statusMonitor",{state:()=>({parkStatus:{},devStatus:{},monitorLog:{},eigenValueRT:[],deviceStatusList:[]}),actions:{reset(){this.$reset()},async fetchGetParkStatusCount(t){try{const e=await c(t);return this.parkStatus=e,e||{}}catch(e){throw console.error("获取失败:",e),e}},async fetchGetDevStatusCount(t){try{const e=await u(t);return this.devStatus=e,e||{}}catch(e){throw console.error("获取失败:",e),e}},async fetchGetSearchMonitorLog(t){try{const e=await i(t);return this.monitorLog=e,e||{}}catch(e){throw console.error("获取失败:",e),e}},async fetchGetEigenValueRT(t){try{const e=await a(t);return this.eigenValueRT=e,e||{}}catch(e){throw console.error("获取失败:",e),e}},async fetchGetWorkCondEVRT(t){try{const e=await n(t);return h(e,{label:"name",value:"value"},{nother:!0})||[]}catch(e){throw console.error("获取失败:",e),e}},async fetchGetTrendAnalyseByTime(t){try{return await o(t)}catch(e){throw console.error("获取失败:",e),e}},async fetchGetTurbineStatusCount(t){try{const e=await s(t);return this.deviceStatusList=e,e}catch(e){throw console.error("获取失败:",e),e}}}});export{S as u};
