import{C as r,L as o,M as s,N as n,O as a,P as c}from"./index-BjOW8S1L.js";const h=r("collectionUnitMonitor",{state:()=>({parkStatus:{},devStatus:{},monitorLog:{},eigenValueRT:[]}),actions:{reset(){this.$reset()},async fetchGetDauStatusCount(t){try{const e=await c(t);return this.eigenValueRT=e,e||{}}catch(e){throw console.error("获取失败:",e),e}},async fetchGetDauStatusInfoByTurbineIDAndDauID(t){try{return await a(t)}catch(e){throw console.error("获取失败:",e),e}},async fetchGetChannelDCTrendChart(t){try{return await n(t)}catch(e){throw console.error("获取失败:",e),e}},async fetchGetDAUListByParkID(t){try{return await s(t)}catch(e){throw console.error("获取失败:",e),e}},async fetchGetDSearchDAULog(t){try{return await o(t)}catch(e){throw console.error("获取失败:",e),e}}}});export{h as u};
