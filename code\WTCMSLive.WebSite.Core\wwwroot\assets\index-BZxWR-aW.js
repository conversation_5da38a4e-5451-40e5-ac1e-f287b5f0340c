import{Z as ke,_ as we,a0 as _,a1 as Ee,ag as ze,aR as ue,a4 as I,a5 as H,af as O,a7 as V,j as A,w as W,h as de,a8 as oe,ac as Le,b as d,aa as R,aE as Ie,aQ as Fe,r as G,ao as je,ae as De,aP as he,a9 as ve,dk as ge,f as B,o as w,d as C,i as z,t as J,g as ie,c as N,q as ye,dm as We,u as Ge,a as Ve,F as ae,e as re,s as U,dn as Xe,dp as Se,dq as Ue,dr as xe}from"./index-BjOW8S1L.js";import{_ as He}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{b as Ke,e as Ze,a as qe,S as Te,L as be,R as Ce,c as Je,M as Be,_ as Oe,d as Re}from"./ActionButton-C_grUmdF.js";import{r as Qe,R as Ye,a as et}from"./styleChecker-CFtINSLw.js";import{S as tt}from"./tools-zTE6InS0.js";import{M as nt}from"./index-BnSFuLp6.js";import{c as ot,e as at,C as rt}from"./ChangeLanguage-Dy5BmE5j.js";import{i as lt}from"./initDefaultProps-P4j1rGDC.js";const st=e=>{const{antCls:n,componentCls:t,iconCls:o,avatarBg:r,avatarColor:g,containerSize:i,containerSizeLG:c,containerSizeSM:p,textFontSize:h,textFontSizeLG:v,textFontSizeSM:u,borderRadius:f,borderRadiusLG:y,borderRadiusSM:$,lineWidth:a,lineType:s}=e,l=(x,m,b)=>({width:x,height:x,lineHeight:`${x-a*2}px`,borderRadius:"50%",[`&${t}-square`]:{borderRadius:b},[`${t}-string`]:{position:"absolute",left:{_skip_check_:!0,value:"50%"},transformOrigin:"0 center"},[`&${t}-icon`]:{fontSize:m,[`> ${o}`]:{margin:0}}});return{[t]:_(_(_(_({},Ee(e)),{position:"relative",display:"inline-block",overflow:"hidden",color:g,whiteSpace:"nowrap",textAlign:"center",verticalAlign:"middle",background:r,border:`${a}px ${s} transparent`,"&-image":{background:"transparent"},[`${n}-image-img`]:{display:"block"}}),l(i,h,f)),{"&-lg":_({},l(c,v,y)),"&-sm":_({},l(p,u,$)),"> img":{display:"block",width:"100%",height:"100%",objectFit:"cover"}})}},it=e=>{const{componentCls:n,groupBorderColor:t,groupOverlapping:o,groupSpace:r}=e;return{[`${n}-group`]:{display:"inline-flex",[`${n}`]:{borderColor:t},"> *:not(:first-child)":{marginInlineStart:o}},[`${n}-group-popover`]:{[`${n} + ${n}`]:{marginInlineStart:r}}}},Me=ke("Avatar",e=>{const{colorTextLightSolid:n,colorTextPlaceholder:t}=e,o=we(e,{avatarBg:t,avatarColor:n});return[st(o),it(o)]},e=>{const{controlHeight:n,controlHeightLG:t,controlHeightSM:o,fontSize:r,fontSizeLG:g,fontSizeXL:i,fontSizeHeading3:c,marginXS:p,marginXXS:h,colorBorderBg:v}=e;return{containerSize:n,containerSizeLG:t,containerSizeSM:o,textFontSize:Math.round((g+i)/2),textFontSizeLG:c,textFontSizeSM:r,groupSpace:h,groupOverlapping:-p,groupBorderColor:v}}),Pe=Symbol("AvatarContextKey"),ct=()=>ze(Pe,{}),ut=e=>ue(Pe,e),dt=()=>({prefixCls:String,shape:{type:String,default:"circle"},size:{type:[Number,String,Object],default:()=>"default"},src:String,srcset:String,icon:H.any,alt:String,gap:Number,draggable:{type:Boolean,default:void 0},crossOrigin:String,loadError:{type:Function}}),E=I({compatConfig:{MODE:3},name:"AAvatar",inheritAttrs:!1,props:dt(),slots:Object,setup(e,n){let{slots:t,attrs:o}=n;const r=O(!0),g=O(!1),i=O(1),c=O(null),p=O(null),{prefixCls:h}=V("avatar",e),[v,u]=Me(h),f=ct(),y=A(()=>e.size==="default"?f.size:e.size),$=Ke(),a=Ze(()=>{if(typeof e.size!="object")return;const m=Qe.find(S=>$.value[S]);return e.size[m]}),s=m=>a.value?{width:`${a.value}px`,height:`${a.value}px`,lineHeight:`${a.value}px`,fontSize:`${m?a.value/2:18}px`}:{},l=()=>{if(!c.value||!p.value)return;const m=c.value.offsetWidth,b=p.value.offsetWidth;if(m!==0&&b!==0){const{gap:S=4}=e;S*2<b&&(i.value=b-S*2<m?(b-S*2)/m:1)}},x=()=>{const{loadError:m}=e;(m==null?void 0:m())!==!1&&(r.value=!1)};return W(()=>e.src,()=>{oe(()=>{r.value=!0,i.value=1})}),W(()=>e.gap,()=>{oe(()=>{l()})}),de(()=>{oe(()=>{l(),g.value=!0})}),()=>{var m,b;const{shape:S,src:k,alt:F,srcset:Y,draggable:j,crossOrigin:T}=e,D=(m=f.shape)!==null&&m!==void 0?m:S,M=Le(t,e,"icon"),L=h.value,ee={[`${o.class}`]:!!o.class,[L]:!0,[`${L}-lg`]:y.value==="large",[`${L}-sm`]:y.value==="small",[`${L}-${D}`]:!0,[`${L}-image`]:k&&r.value,[`${L}-icon`]:M,[u.value]:!0},te=typeof y.value=="number"?{width:`${y.value}px`,height:`${y.value}px`,lineHeight:`${y.value}px`,fontSize:M?`${y.value/2}px`:"18px"}:{},X=(b=t.default)===null||b===void 0?void 0:b.call(t);let P;if(k&&r.value)P=d("img",{draggable:j,src:k,srcset:Y,onError:x,alt:F,crossorigin:T},null);else if(M)P=M;else if(g.value||i.value!==1){const ne=`scale(${i.value}) translateX(-50%)`,Ae={msTransform:ne,WebkitTransform:ne,transform:ne},Ne=typeof y.value=="number"?{lineHeight:`${y.value}px`}:{};P=d(Ye,{onResize:l},{default:()=>[d("span",{class:`${L}-string`,ref:c,style:_(_({},Ne),Ae)},[X])]})}else P=d("span",{class:`${L}-string`,ref:c,style:{opacity:0}},[X]);return v(d("span",R(R({},o),{},{ref:p,class:ee,style:[te,s(!!M),o.style]}),[P]))}}}),gt=()=>({prefixCls:String,maxCount:Number,maxStyle:{type:Object,default:void 0},maxPopoverPlacement:{type:String,default:"top"},maxPopoverTrigger:String,size:{type:[Number,String,Object],default:"default"},shape:{type:String,default:"circle"}}),ce=I({compatConfig:{MODE:3},name:"AAvatarGroup",inheritAttrs:!1,props:gt(),setup(e,n){let{slots:t,attrs:o}=n;const{prefixCls:r,direction:g}=V("avatar",e),i=A(()=>`${r.value}-group`),[c,p]=Me(r);return Ie(()=>{const h={size:e.size,shape:e.shape};ut(h)}),()=>{const{maxPopoverPlacement:h="top",maxCount:v,maxStyle:u,maxPopoverTrigger:f="hover",shape:y}=e,$={[i.value]:!0,[`${i.value}-rtl`]:g.value==="rtl",[`${o.class}`]:!!o.class,[p.value]:!0},a=Le(t,e),s=Fe(a).map((x,m)=>et(x,{key:`avatar-key-${m}`})),l=s.length;if(v&&v<l){const x=s.slice(0,v),m=s.slice(v,l);return x.push(d(qe,{key:"avatar-popover-key",content:m,trigger:f,placement:h,overlayClassName:`${i.value}-popover`},{default:()=>[d(E,{style:u,shape:y},{default:()=>[`+${l-v}`]})]})),c(d("div",R(R({},o),{},{class:$,style:o.style}),[x]))}return c(d("div",R(R({},o),{},{class:$,style:o.style}),[s]))}}});E.Group=ce;E.install=function(e){return e.component(E.name,E),e.component(ce.name,ce),e};const pt=e=>!isNaN(parseFloat(e))&&isFinite(e),mt=e=>{const{componentCls:n,colorBgContainer:t,colorBgBody:o,colorText:r}=e;return{[`${n}-sider-light`]:{background:t,[`${n}-sider-trigger`]:{color:r,background:t},[`${n}-sider-zero-width-trigger`]:{color:r,background:t,border:`1px solid ${o}`,borderInlineStart:0}}}},ft=e=>{const{antCls:n,componentCls:t,colorText:o,colorTextLightSolid:r,colorBgHeader:g,colorBgBody:i,colorBgTrigger:c,layoutHeaderHeight:p,layoutHeaderPaddingInline:h,layoutHeaderColor:v,layoutFooterPadding:u,layoutTriggerHeight:f,layoutZeroTriggerSize:y,motionDurationMid:$,motionDurationSlow:a,fontSize:s,borderRadius:l}=e;return{[t]:_(_({display:"flex",flex:"auto",flexDirection:"column",color:o,minHeight:0,background:i,"&, *":{boxSizing:"border-box"},[`&${t}-has-sider`]:{flexDirection:"row",[`> ${t}, > ${t}-content`]:{width:0}},[`${t}-header, &${t}-footer`]:{flex:"0 0 auto"},[`${t}-header`]:{height:p,paddingInline:h,color:v,lineHeight:`${p}px`,background:g,[`${n}-menu`]:{lineHeight:"inherit"}},[`${t}-footer`]:{padding:u,color:o,fontSize:s,background:i},[`${t}-content`]:{flex:"auto",minHeight:0},[`${t}-sider`]:{position:"relative",minWidth:0,background:g,transition:`all ${$}, background 0s`,"&-children":{height:"100%",marginTop:-.1,paddingTop:.1,[`${n}-menu${n}-menu-inline-collapsed`]:{width:"auto"}},"&-has-trigger":{paddingBottom:f},"&-right":{order:1},"&-trigger":{position:"fixed",bottom:0,zIndex:1,height:f,color:r,lineHeight:`${f}px`,textAlign:"center",background:c,cursor:"pointer",transition:`all ${$}`},"&-zero-width":{"> *":{overflow:"hidden"},"&-trigger":{position:"absolute",top:p,insetInlineEnd:-y,zIndex:1,width:y,height:y,color:r,fontSize:e.fontSizeXL,display:"flex",alignItems:"center",justifyContent:"center",background:g,borderStartStartRadius:0,borderStartEndRadius:l,borderEndEndRadius:l,borderEndStartRadius:0,cursor:"pointer",transition:`background ${a} ease`,"&::after":{position:"absolute",inset:0,background:"transparent",transition:`all ${a}`,content:'""'},"&:hover::after":{background:"rgba(255, 255, 255, 0.2)"},"&-right":{insetInlineStart:-y,borderStartStartRadius:l,borderStartEndRadius:0,borderEndEndRadius:0,borderEndStartRadius:l}}}}},mt(e)),{"&-rtl":{direction:"rtl"}})}},ht=ke("Layout",e=>{const{colorText:n,controlHeightSM:t,controlHeight:o,controlHeightLG:r,marginXXS:g}=e,i=r*1.25,c=we(e,{layoutHeaderHeight:o*2,layoutHeaderPaddingInline:i,layoutHeaderColor:n,layoutFooterPadding:`${t}px ${i}px`,layoutTriggerHeight:r+g*2,layoutZeroTriggerSize:r});return[ft(c)]},e=>{const{colorBgLayout:n}=e;return{colorBgHeader:"#001529",colorBgBody:n,colorBgTrigger:"#002140"}}),pe=()=>({prefixCls:String,hasSider:{type:Boolean,default:void 0},tagName:String});function Q(e){let{suffixCls:n,tagName:t,name:o}=e;return r=>I({compatConfig:{MODE:3},name:o,props:pe(),setup(i,c){let{slots:p}=c;const{prefixCls:h}=V(n,i);return()=>{const v=_(_({},i),{prefixCls:h.value,tagName:t});return d(r,v,p)}}})}const me=I({compatConfig:{MODE:3},props:pe(),setup(e,n){let{slots:t}=n;return()=>d(e.tagName,{class:e.prefixCls},t)}}),vt=I({compatConfig:{MODE:3},inheritAttrs:!1,props:pe(),setup(e,n){let{slots:t,attrs:o}=n;const{prefixCls:r,direction:g}=V("",e),[i,c]=ht(r),p=G([]);ue(Te,{addSider:u=>{p.value=[...p.value,u]},removeSider:u=>{p.value=p.value.filter(f=>f!==u)}});const v=A(()=>{const{prefixCls:u,hasSider:f}=e;return{[c.value]:!0,[`${u}`]:!0,[`${u}-has-sider`]:typeof f=="boolean"?f:p.value.length>0,[`${u}-rtl`]:g.value==="rtl"}});return()=>{const{tagName:u}=e;return i(d(u,_(_({},o),{class:[v.value,o.class]}),t))}}}),le=Q({suffixCls:"layout",tagName:"section",name:"ALayout"})(vt),K=Q({suffixCls:"layout-header",tagName:"header",name:"ALayoutHeader"})(me),se=Q({suffixCls:"layout-footer",tagName:"footer",name:"ALayoutFooter"})(me),Z=Q({suffixCls:"layout-content",tagName:"main",name:"ALayoutContent"})(me);var yt={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M912 192H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 284H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 284H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM104 228a56 56 0 10112 0 56 56 0 10-112 0zm0 284a56 56 0 10112 0 56 56 0 10-112 0zm0 284a56 56 0 10112 0 56 56 0 10-112 0z"}}]},name:"bars",theme:"outlined"};function _e(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?Object(arguments[n]):{},o=Object.keys(t);typeof Object.getOwnPropertySymbols=="function"&&(o=o.concat(Object.getOwnPropertySymbols(t).filter(function(r){return Object.getOwnPropertyDescriptor(t,r).enumerable}))),o.forEach(function(r){St(e,r,t[r])})}return e}function St(e,n,t){return n in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}var fe=function(n,t){var o=_e({},n,t.attrs);return d(je,_e({},o,{icon:yt}),null)};fe.displayName="BarsOutlined";fe.inheritAttrs=!1;const $e={xs:"479.98px",sm:"575.98px",md:"767.98px",lg:"991.98px",xl:"1199.98px",xxl:"1599.98px",xxxl:"1999.98px"},xt=()=>({prefixCls:String,collapsible:{type:Boolean,default:void 0},collapsed:{type:Boolean,default:void 0},defaultCollapsed:{type:Boolean,default:void 0},reverseArrow:{type:Boolean,default:void 0},zeroWidthTriggerStyle:{type:Object,default:void 0},trigger:H.any,width:H.oneOfType([H.number,H.string]),collapsedWidth:H.oneOfType([H.number,H.string]),breakpoint:H.oneOf(ve("xs","sm","md","lg","xl","xxl","xxxl")),theme:H.oneOf(ve("light","dark")).def("dark"),onBreakpoint:Function,onCollapse:Function}),bt=(()=>{let e=0;return function(){let n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"";return e+=1,`${n}${e}`}})(),q=I({compatConfig:{MODE:3},name:"ALayoutSider",inheritAttrs:!1,props:lt(xt(),{collapsible:!1,defaultCollapsed:!1,reverseArrow:!1,width:200,collapsedWidth:80}),emits:["breakpoint","update:collapsed","collapse"],setup(e,n){let{emit:t,attrs:o,slots:r}=n;const{prefixCls:g}=V("layout-sider",e),i=ze(Te,void 0),c=O(!!(e.collapsed!==void 0?e.collapsed:e.defaultCollapsed)),p=O(!1);W(()=>e.collapsed,()=>{c.value=!!e.collapsed}),ue(Je,c);const h=(a,s)=>{e.collapsed===void 0&&(c.value=a),t("update:collapsed",a),t("collapse",a,s)},v=O(a=>{p.value=a.matches,t("breakpoint",a.matches),c.value!==a.matches&&h(a.matches,"responsive")});let u;function f(a){return v.value(a)}const y=bt("ant-sider-");i&&i.addSider(y),de(()=>{W(()=>e.breakpoint,()=>{try{u==null||u.removeEventListener("change",f)}catch{u==null||u.removeListener(f)}if(typeof window<"u"){const{matchMedia:a}=window;if(a&&e.breakpoint&&e.breakpoint in $e){u=a(`(max-width: ${$e[e.breakpoint]})`);try{u.addEventListener("change",f)}catch{u.addListener(f)}f(u)}}},{immediate:!0})}),De(()=>{try{u==null||u.removeEventListener("change",f)}catch{u==null||u.removeListener(f)}i&&i.removeSider(y)});const $=()=>{h(!c.value,"clickTrigger")};return()=>{var a,s;const l=g.value,{collapsedWidth:x,width:m,reverseArrow:b,zeroWidthTriggerStyle:S,trigger:k=(a=r.trigger)===null||a===void 0?void 0:a.call(r),collapsible:F,theme:Y}=e,j=c.value?x:m,T=pt(j)?`${j}px`:String(j),D=parseFloat(String(x||0))===0?d("span",{onClick:$,class:he(`${l}-zero-width-trigger`,`${l}-zero-width-trigger-${b?"right":"left"}`),style:S},[k||d(fe,null,null)]):null,M={expanded:b?d(Ce,null,null):d(be,null,null),collapsed:b?d(be,null,null):d(Ce,null,null)},L=c.value?"collapsed":"expanded",ee=M[L],te=k!==null?D||d("div",{class:`${l}-trigger`,onClick:$,style:{width:T}},[k||ee]):null,X=[o.style,{flex:`0 0 ${T}`,maxWidth:T,minWidth:T,width:T}],P=he(l,`${l}-${Y}`,{[`${l}-collapsed`]:!!c.value,[`${l}-has-trigger`]:F&&k!==null&&!D,[`${l}-below`]:!!p.value,[`${l}-zero-width`]:parseFloat(T)===0},o.class);return d("aside",R(R({},o),{},{class:P,style:X}),[d("div",{class:`${l}-children`},[(s=r.default)===null||s===void 0?void 0:s.call(r)]),F||p.value&&D?te:null])}}}),Ct=K,Vt=q,Xt=Z,Ut=_(le,{Header:K,Footer:se,Content:Z,Sider:q,install:e=>(e.component(le.name,le),e.component(K.name,K),e.component(se.name,se),e.component(q.name,q),e.component(Z.name,Z),e)}),_t="/logo.png",$t=ge(),kt={name:"AvatarDropdown",props:{currentUser:{type:Object,default:()=>null},menu:{type:Boolean,default:!0}},methods:{handleToCenter(){this.$router.push({path:"/account/center"})},handleToSettings(){this.$router.push({path:"/account/settings"})},handleLogout(e){nt.confirm({title:this.$t("layouts.usermenu.dialog.title"),content:this.$t("layouts.usermenu.dialog.content"),okText:this.$t("button.confirm"),cancelText:this.$t("button.cancel"),onOk:()=>{$t.logout()},onCancel(){}})}}},wt={class:"ant-pro-account-avatar"};function zt(e,n,t,o,r,g){const i=E,c=Oe,p=Be,h=Re,v=tt;return t.currentUser&&t.currentUser.name?(w(),B(h,{key:0,placement:"bottomRight",overlayClassName:"logoutClass"},{overlay:C(()=>[d(p,{class:"ant-pro-drop-down menu","selected-keys":[]},{default:C(()=>[d(c,{key:"logout",onClick:g.handleLogout},{default:C(()=>[ie(J(e.$t("menu.logout")),1)]),_:1},8,["onClick"])]),_:1})]),default:C(()=>[z("span",wt,[d(i,{size:"small",src:"/userIcon.png",class:"antd-pro-global-header-index-avatar"}),z("span",null,J(t.currentUser.name),1)])]),_:1})):(w(),B(h,{key:1,overlayClassName:"logoutClass",placement:"bottomRight"},{overlay:C(()=>[d(p,{class:"ant-pro-drop-down menu","selected-keys":[]},{default:C(()=>[d(c,{key:"logout",onClick:g.handleLogout},{default:C(()=>n[1]||(n[1]=[ie(" 返回登录 ",-1)])),_:1,__:[1]},8,["onClick"])]),_:1})]),default:C(()=>[z("span",null,[d(v,{size:"small",style:{marginLeft:8,marginRight:8}}),n[0]||(n[0]=z("span",{class:"noLogin"},"未登录",-1))])]),_:1}))}const Lt=He(kt,[["render",zt],["__scopeId","data-v-7c843132"]]),Ht={__name:"RightContent",props:{prefixCls:{type:String,default:"ant-pro-global-header-index-action"},isMobile:{type:Boolean,default:()=>!1}},setup(e){const n=ge(),t=G(!0),o=G({}),r=A(()=>({"ant-pro-global-header-index-right":!0}));return de(()=>{let g=localStorage.getItem("user");if(g){let i=JSON.parse(g);o.value={name:i.username}}else n.logout()}),(g,i)=>(w(),N("div",{class:ye(r.value)},[d(Lt,{menu:t.value,"current-user":o.value,class:ye(e.prefixCls)},null,8,["menu","current-user","class"])],2))}},Tt={class:"topMenus"},Bt={class:"topRight clearfix"},Ot={key:0,class:"pullLeft"},Rt={key:1,class:"pullLeft"},Mt=["onClick"],Pt={class:"pullLeft"},At={__name:"index",props:{currentTreeNode:{type:Object,default:()=>({})}},emits:["menu-select","changeView"],setup(e,{emit:n}){const t=n,o=e,{locale:r}=We(),g=Ge(),i=Ve();ge();const c=G(window.localStorage.getItem("templateManagement")==="true"),p=G([]);A(()=>r.value==="en"?ot:at);const h=A(()=>{const a=i.getRoutes();return c.value?a.filter(s=>{var l;return((l=s.meta)==null?void 0:l.locationLeft)&&s.name==="config"}):o.currentTreeNode&&o.currentTreeNode.type?a.filter(s=>{var l;return((l=s.meta)==null?void 0:l.locationLeft)&&s.children.some(x=>{var m;return((m=x.meta)==null?void 0:m.selectNodeType)===o.currentTreeNode.type})}):a.filter(s=>{var l;return(l=s.meta)==null?void 0:l.locationLeft})}),v=A(()=>i.getRoutes().filter(a=>{var s;return(s=a.meta)==null?void 0:s.locationRight})),u=({item:a,key:s,keyPath:l})=>{t("menu-select",s)},f=a=>{try{a.name=="templateManagement"?(window.localStorage.setItem("templateManagement",!0),xe({templateView:!0}),c.value=!0,t("changeView","enter")):i.push({name:a.name})}catch(s){console.error("Error navigating to route:",s)}},y=()=>{window.localStorage.setItem("templateManagement",!1),xe({}),c.value=!1,t("changeView","exit")};W(()=>g.path,()=>{g.matched&&g.matched.length>0&&(p.value=[g.matched[0].path])},{immediate:!0});const $=()=>{Ue(g,i)};return(a,s)=>{const l=Oe,x=Be,m=Re,b=Ct;return w(),B(b,{class:"header clearfix"},{default:C(()=>[z("div",{class:"logo pullLeft",onClick:$},s[2]||(s[2]=[z("img",{src:_t,alt:"logo"},null,-1)])),z("div",Tt,[d(x,{selectedKeys:p.value,"onUpdate:selectedKeys":s[0]||(s[0]=S=>p.value=S),theme:"dark",mode:"horizontal",onSelect:u},{default:C(()=>[(w(!0),N(ae,null,re(h.value,S=>(w(),B(l,{key:S.path},{default:C(()=>[ie(J(a.$t(S.meta.title)),1)]),_:2},1024))),128))]),_:1},8,["selectedKeys"])]),z("div",Bt,[c.value?(w(),N("div",Ot,[z("span",{class:"exitTemp",onClick:y},"返回")])):U("",!0),d(rt),c.value?U("",!0):(w(),N("div",Rt,[(w(!0),N(ae,null,re(v.value,S=>(w(),B(m,{key:S.path,placement:"bottom",overlayClassName:"manageClass",arrow:""},{overlay:C(()=>[d(x,null,{default:C(()=>[(w(!0),N(ae,null,re(S.children,k=>(w(),B(l,{key:k.path},{default:C(()=>[k.icon?(w(),B(Se(k.icon),{key:0})):U("",!0),z("a",{href:"javascript:;",onClick:F=>f(k),class:"dropMenuItem"},J(a.$t(k.meta.title)),9,Mt)]),_:2},1024))),128))]),_:2},1024)]),default:C(()=>[z("a",{class:"dropdown-link",onClick:s[1]||(s[1]=Xe(()=>{},["prevent"]))},[S.meta&&S.meta.icon?(w(),B(Se(S.meta.icon),{key:0})):U("",!0)])]),_:2},1024))),128))])),z("div",Pt,[d(Ht)])])]),_:1})}}},Kt=He(At,[["__scopeId","data-v-4a266b80"]]);export{Kt as H,Vt as L,Ut as _,Xt as a};
